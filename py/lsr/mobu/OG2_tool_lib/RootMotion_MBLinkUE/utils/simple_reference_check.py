#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的Unreal资产引用检查示例
"""

import unreal


def is_asset_referenced(asset_path):
    """
    检查资产是否被引用的简单方法
    
    Args:
        asset_path (str): 资产路径，例如 "/Game/MyAsset"
        
    Returns:
        bool: 如果被引用返回True
    """
    try:
        asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        
        # 获取资产数据
        asset_data = asset_registry.get_asset_by_object_path(asset_path)
        if not asset_data:
            print(f"资产不存在: {asset_path}")
            return False
        
        # 获取引用者
        referencers = asset_registry.get_referencers(
            asset_data.package_name,
            unreal.AssetRegistryDependencyOptions()
        )
        
        return len(referencers) > 0
        
    except Exception as e:
        print(f"检查引用时出错: {e}")
        return False


def get_asset_referencers(asset_path):
    """
    获取引用指定资产的所有资产
    
    Args:
        asset_path (str): 资产路径
        
    Returns:
        list: 引用者路径列表
    """
    try:
        asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        asset_data = asset_registry.get_asset_by_object_path(asset_path)
        
        if not asset_data:
            return []
        
        referencers = asset_registry.get_referencers(
            asset_data.package_name,
            unreal.AssetRegistryDependencyOptions()
        )
        
        return [str(ref) for ref in referencers]
        
    except Exception as e:
        print(f"获取引用者时出错: {e}")
        return []


def check_selected_assets():
    """
    检查当前选中资产的引用情况
    """
    try:
        # 获取选中的资产
        selected_assets = unreal.EditorUtilityLibrary.get_selected_assets()
        
        if not selected_assets:
            print("没有选中任何资产")
            return
        
        for asset in selected_assets:
            asset_path = asset.get_path_name()
            is_ref = is_asset_referenced(asset_path)
            
            print(f"\n资产: {asset_path}")
            print(f"是否被引用: {is_ref}")
            
            if is_ref:
                referencers = get_asset_referencers(asset_path)
                print(f"引用者数量: {len(referencers)}")
                for ref in referencers[:5]:  # 只显示前5个
                    print(f"  - {ref}")
                if len(referencers) > 5:
                    print(f"  ... 还有 {len(referencers) - 5} 个引用者")
            else:
                print("该资产未被引用，可以安全删除")
                
    except Exception as e:
        print(f"检查选中资产时出错: {e}")


def find_unused_assets_in_current_folder():
    """
    查找当前内容浏览器文件夹中未被引用的资产
    """
    try:
        # 获取当前内容浏览器的路径
        current_path = unreal.EditorUtilityLibrary.get_current_content_browser_path()
        if not current_path:
            current_path = "/Game"
        
        print(f"检查文件夹: {current_path}")
        
        # 获取文件夹中的所有资产
        editor_asset_lib = unreal.EditorAssetLibrary()
        assets = editor_asset_lib.list_assets(current_path, recursive=False)
        
        unused_assets = []
        for asset_path in assets:
            if not is_asset_referenced(asset_path):
                unused_assets.append(asset_path)
        
        print(f"\n找到 {len(unused_assets)} 个未被引用的资产:")
        for asset in unused_assets:
            print(f"  - {asset}")
        
        return unused_assets
        
    except Exception as e:
        print(f"查找未使用资产时出错: {e}")
        return []


# 直接运行的示例
if __name__ == "__main__":
    # 示例1: 检查特定资产
    test_asset = "/Game/Characters/MyCharacter"
    print(f"检查资产: {test_asset}")
    print(f"是否被引用: {is_asset_referenced(test_asset)}")
    
    # 示例2: 检查选中的资产
    print("\n=== 检查选中资产 ===")
    check_selected_assets()
    
    # 示例3: 查找当前文件夹中未使用的资产
    print("\n=== 查找未使用资产 ===")
    find_unused_assets_in_current_folder()
