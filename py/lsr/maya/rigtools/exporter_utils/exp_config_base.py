import json
from typing import List, Dict, Optional
import ctypes
import os
from lsr.maya.ui.model_panel import logger


class FbxConfig(object):

    def __init__(self, open_fbx_file, save_fbx_file):
        self._open_fbx_file = open_fbx_file
        self._save_fbx_file = save_fbx_file

        self._export_joints: List = []
        self._export_meshes: List = []

        self._extract_joints: Dict = {}
        self._extract_groups: Dict = {}

        self._enable_prefix: bool = True
        self._prefix: str = ""

        self._enable_animation: bool = False

        self._enable_force_visibility = False

        self._rename_dict: Dict = {}
        self._parent_dict: Dict = {}

    def set_export_joints(self, export_joints: List) -> None:
        self._export_joints = export_joints

    def set_export_meshes(self, export_meshes: List) -> None:
        self._export_meshes = export_meshes

    def set_extract_joints(self, extract_joints: Dict) -> None:
        self._extract_joints = extract_joints

    def set_extract_groups(self, extract_groups: Dict) -> None:
        self._extract_groups = extract_groups

    def set_enable_prefix(self, enable_prefix: bool) -> None:
        self._enable_prefix = enable_prefix

    def set_prefix(self, prefix: str) -> None:
        self._prefix = prefix

    def set_enable_animation(self, enable_animation: bool) -> None:
        self._enable_animation = enable_animation

    def set_enable_force_visibility(self, enable_force_visibility: bool) -> None:
        self._enable_force_visibility = enable_force_visibility

    def set_rename_dict(self, rename_dict: Dict) -> None:
        self._rename_dict = rename_dict

    def set_parent_dict(self, parent_dict: Dict) -> None:
        self._parent_dict = parent_dict

    def get_export_joints(self) -> List:
        return self._export_joints

    def get_export_meshes(self) -> List:
        return self._export_meshes

    def get_extract_joints(self) -> Dict:
        return self._extract_joints

    def get_extract_groups(self) -> Dict:
        return self._extract_groups

    def get_enable_prefix(self) -> bool:
        return self._enable_prefix

    def get_prefix(self) -> str:
        return self._prefix

    def get_enable_animation(self) -> bool:
        return self._enable_animation

    def get_rename_dict(self) -> Dict:
        return self._rename_dict

    def add_rename_dict(self, name: str, rename: str) -> None:
        self._rename_dict[name] = rename

    def get_parent_dict(self) -> Dict:
        return self._parent_dict

    def add_parent_dict(self, name: str, parent_name: str) -> None:
        self._parent_dict[name] = parent_name

    def get_open_fbx_file(self) -> str:
        return self._open_fbx_file

    def get_save_fbx_file(self) -> str:
        return self._save_fbx_file

    def set_open_fbx_file(self, open_fbx_file: str) -> None:
        self._open_fbx_file = open_fbx_file

    def set_save_fbx_file(self, save_fbx_file: str) -> None:
        self._save_fbx_file = save_fbx_file

    def add_extract_joint(self, name: str, matrix: List, orient: List) -> None:
        attr = {'matrix': matrix, 'orient': orient}
        self._extract_joints[name] = attr

    def add_extract_group(self, name: str, matrix: List) -> None:
        attr = {'matrix': matrix}
        self._extract_groups[name] = attr

    def get_json_str(self) -> str:
        config_dict = {}
        for attr_name, value in vars(self).items():
            if attr_name.startswith('_'):
                key = attr_name[1:]
                config_dict[key] = value

        return json.dumps(config_dict, indent=4)


class LSRKitManager:
    _instance: Optional["LSRKitManager"] = None

    def __new__(cls, *args, **kwargs):
        # Implement singleton pattern
        if cls._instance is None:
            cls._instance = super(LSRKitManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        # Avoid re-initialization
        if self._initialized:
            return
        # Try to load DLL library
        self._initialized = self._load_dll()

    def _load_dll(self)-> bool:
        dll_file = self._get_dll_file()
        if '' == dll_file:
            logger.error("LSRKit.dll not found in system paths")
            return False

        self._lsrkit_lib = ctypes.CDLL(dll_file)
        logger.info("LSRKit.dll loaded {}".format(dll_file))
        return True


    def _get_dll_file(self) -> str:
        # Search for and load DLL in system paths
        path_list = os.environ.get('PATH', '').split(os.pathsep)
        for path in path_list:
            lsrkit_dll_file = os.path.join(path, 'LSRKit.dll')
            if os.path.isfile(lsrkit_dll_file):
                return lsrkit_dll_file
        return ''

    def lsrkit_fbx_lkreprocess_export_fbx(self, config: FbxConfig, debug: bool = False)-> None:
        if not self._initialized:
            raise RuntimeError("LSRKitManager not initialized")

        if debug:
            fbx_base_file = config.get_open_fbx_file()
            save_fbx_file = fbx_base_file.replace(".fbx", "_debug.fbx")
            config.set_save_fbx_file(save_fbx_file)

            logger.info("open file to: {}".format(fbx_base_file))
            logger.info("save file to: {}".format(save_fbx_file))
            json_file = fbx_base_file.replace(".fbx", ".json")
            with open(json_file, 'w') as json_file:
                json_file.write(config.get_json_str())

        self._lsrkit_lib.lsrkit_fbx_lkreprocess_export_fbx.argtypes = [ctypes.c_char_p]
        self._lsrkit_lib.lsrkit_fbx_lkreprocess_export_fbx.restype = ctypes.c_bool

        c_string = config.get_json_str().encode('utf-8')
        self._lsrkit_lib.lsrkit_fbx_lkreprocess_export_fbx(c_string)
