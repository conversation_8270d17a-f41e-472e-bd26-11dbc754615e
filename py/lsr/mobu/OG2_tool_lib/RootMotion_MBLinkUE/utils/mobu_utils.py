# -*- coding: utf-8 -*-
"""
This file contains some utility functions for working with RootMotionLink.
"""

import pyfbsdk as fb
from lsr.mobu.nodezoo.node import Character
import lsr.mobu.utils.anim_util as anim_util


def get_current_character_longname():
    """
    Get the current character long name.

    Returns:
        str: The current character long name.
    """
    current_character = Character.current_character()
    if not current_character:
        return ''
    return current_character.fb_node.LongName

def get_char_node_by_longname(char_node_longname):
    """
    Get the character node by long name.

    Args:
        char_node_longname (str): The character node long name.

    Returns:
        FBCharacter: The character node.
    """
    for char in fb.FBSystem().Scene.Characters:
        if char.LongName == char_node_longname:
            return char
    return None

def reset_root_motion(char_node_longname):
    """
    Reset root motion.

    Args:
        char_node_longname (str): The character node long name.

    Returns:
        None
    """
    char_node = get_char_node_by_longname(char_node_longname)
    if not char_node:
        return
    fb.FBApplication().CurrentCharacter = char_node
    current_character = Character.current_character()
    char_node_root = current_character.root_bone
    if not char_node_root:
        return

    anim_util.clear_keyframes(char_node_root)
    char_node_root.set_translation(fb.FBVector3d(0, 0, 0))
    char_node_root.set_rotation(fb.FBVector3d(0, 0, 0))

def set_key_matrix(data, time_step, char_node_longname):
    """
    Set key matrix.

    Args:
        data (list): The data to set.
        time_step (int): The time step.
        char_node_longname (str): The character node long name.

    Returns:
        None
    """
    reset_root_motion(char_node_longname)
    char_node = get_char_node_by_longname(char_node_longname)
    if not char_node:
        return
    fb.FBApplication().CurrentCharacter = char_node
    current_character = Character.current_character()
    char_node_root = current_character.root_bone
    if not char_node_root:
        return
    y_ro_minus90 = fb.FBMatrix([0, 0, 1, 0,
                                0, 1, 0, 0,
                                -1, 0, 0, 0,
                                0, 0, 0, 1])
    x_ro_minus90 = fb.FBMatrix([1, 0, 0, 0,
                                0, 0, 1, 0,
                                0, -1, 0, 0,
                                0, 0, 0, 1])
    z_ro_minus90 = fb.FBMatrix([0, -1, 0, 0,
                                1, 0, 0, 0,
                                0, 0, 1, 0,
                                0, 0, 0, 1])
    fps = fb.FBPlayerControl().GetTransportFpsValue()
    for i, time_matrix in enumerate(data):
        time_matrix = y_ro_minus90 * x_ro_minus90 * time_matrix * z_ro_minus90
        time_matrix[1] *= -1
        time_matrix[4] *= -1
        time_matrix[6] *= -1
        time_matrix[9] *= -1
        fb.FBPlayerControl().Goto(fb.FBTime(0, 0, 0, int(time_step / 1 * i * fps)))
        char_node_root.set_matrix(time_matrix)
        char_node_root.fb_node.Selected = True
        fb.FBPlayerControl().Key()