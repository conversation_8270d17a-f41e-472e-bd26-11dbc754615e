#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试贝塞尔曲线编辑器的键盘快捷键功能
"""

import sys
from PySide2 import QtWidgets, QtCore
from bezier_curve_editor import BezierCurveEditor


def test_keyboard_shortcuts():
    """测试键盘快捷键功能"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建编辑器
    editor = BezierCurveEditor()
    editor.setWindowTitle("测试键盘快捷键 - 贝塞尔曲线编辑器")
    editor.resize(1000, 700)
    
    # 设置样式
    editor.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 11px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 60px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 80px;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QLabel {
            color: #cccccc;
        }
    """)
    
    # 显示窗口
    editor.show()
    
    # 确保曲线编辑器获得焦点
    editor.curve_widget.setFocus()
    
    # 添加快捷键说明
    QtWidgets.QMessageBox.information(
        editor, 
        "键盘快捷键测试", 
        "🎹 新增快捷键功能:\n\n"
        "✅ 已添加的快捷键:\n"
        "• Delete - 删除选中的关键帧\n"
        "• Esc - 取消选择当前关键帧\n"
        "• Ctrl+A - 在时间轴中间添加关键帧\n\n"
        "🎯 测试步骤:\n"
        "1. 点击曲线编辑区域获得焦点（会显示蓝色虚线边框）\n"
        "2. 左键点击选择一个关键帧（红色高亮）\n"
        "3. 按Delete键删除选中的关键帧\n"
        "4. 按Esc键取消选择\n"
        "5. 按Ctrl+A添加新关键帧\n\n"
        "💡 视觉提示:\n"
        "• 蓝色虚线边框 = 编辑器有焦点\n"
        "• 红色关键帧 = 当前选中\n"
        "• 底部显示快捷键提示\n"
        "• 控制台输出操作反馈\n\n"
        "⚠️ 注意:\n"
        "• 至少保留2个关键帧，无法全部删除\n"
        "• 必须先点击编辑区域获得焦点才能使用快捷键"
    )
    
    # 添加一些测试关键帧
    curve_widget = editor.curve_widget
    curve_widget.add_keyframe(2.5, 0.5)
    curve_widget.add_keyframe(7.5, -0.5)
    
    print("=== 键盘快捷键测试开始 ===")
    print("请按照说明进行测试:")
    print("1. 点击曲线编辑区域获得焦点")
    print("2. 选择关键帧后按Delete键删除")
    print("3. 按Esc键取消选择")
    print("4. 按Ctrl+A添加关键帧")
    print("5. 观察控制台输出和视觉反馈")
    print("=" * 40)
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    test_keyboard_shortcuts()
