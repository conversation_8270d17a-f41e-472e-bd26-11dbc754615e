# -*- coding: utf-8 -*-

from Qt import QtWidgets, QtCore, QtGui


class EyeBlinkView(QtWidgets.QWidget):
    """
    Eye Blink View
    """

    def __init__(self, parent=None):
        super(EyeBlinkView, self).__init__(parent)
        self._init_ui()

    def _init_ui(self):
        """Setup UI"""
        self.layout = QtWidgets.QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.setLayout(self.layout)
