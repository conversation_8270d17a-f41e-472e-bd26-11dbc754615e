# -*- coding: utf-8 -*-


from maya import cmds
from maya import OpenMaya


class UndoChunkContext(object):
    """
    The undo context is used to combine a chain of commands into one undo.
    Can be used in combination with the "with" statement.

    with UndoChunkContext():
        # code
    """

    def __enter__(self):
        cmds.undoInfo(openChunk=True)

    def __exit__(self, *exc_info):
        cmds.undoInfo(closeChunk=True)


def importDeepReference():
    """Import Reference deeply"""
    refs = cmds.ls(type='reference')
    top_node = []
    for ref_node in refs:
        if ref_node.find('a_sharedReferenceNode') == -1 and \
            ref_node.find('sharedReferenceNode') == -1 and \
            ref_node != 'UI' and \
            ref_node != 'shared' and \
                (ref_node.find('_UNKNOWN_REF_NODE_') == -1):
            try:
                if cmds.referenceQuery(ref_node, isLoaded=True):
                    top_node.append(
                        cmds.referenceQuery(
                            ref_node,
                            referenceNode=True,
                            topReference=True))
            except BaseException:
                cmds.warning(
                    '%s is not associated with a reference file' %
                    ref_node)
    if top_node:
        top_node = list(set(top_node))
        for tnode in top_node:
            refPath = cmds.referenceQuery(tnode, filename=True)
            cmds.file(refPath, importReference=True)
        importDeepReference()


def lockAndHide(node, attributes=None):
    """lock and hide attrs, attrs is an array possible values: ['t','r','s','v'] """

    if not attributes:
        attributes = ['t', 'r', 's', 'v']
    # attributes = kwargs.get("attributes", kwargs.get('attrs', ['t','r','s','v']))

    for attr in attributes:
        cmds.setAttr(node + "." + attr, k=False, l=True, cb=False)
        if (attr == "r") or (attr == "rotate"):
            cmds.setAttr(node + ".rx", k=False, l=True)
            cmds.setAttr(node + ".ry", k=False, l=True)
            cmds.setAttr(node + ".rz", k=False, l=True)
        if (attr == "t") or (attr == "translate"):
            cmds.setAttr(node + ".tx", k=False, l=True)
            cmds.setAttr(node + ".ty", k=False, l=True)
            cmds.setAttr(node + ".tz", k=False, l=True)
        if (attr == "s") or (attr == "scale"):
            cmds.setAttr(node + ".sx", k=False, l=True)
            cmds.setAttr(node + ".sy", k=False, l=True)
            cmds.setAttr(node + ".sz", k=False, l=True)
        if (attr == "v") or (attr == "visibility"):
            cmds.setAttr(node + ".v", k=False, l=True)
    return True


def unlockAndShow(node, attributes=None):
    if not attributes:
        attributes = ['t', 'r', 's', 'v']

    for attr in attributes:
        cmds.setAttr(node + "." + attr, k=True, l=False, cb=True)
        if (attr == "r") or (attr == "rotate"):
            cmds.setAttr(node + ".rx", k=True, l=False)
            cmds.setAttr(node + ".ry", k=True, l=False)
            cmds.setAttr(node + ".rz", k=True, l=False)
        if (attr == "t") or (attr == "translate"):
            cmds.setAttr(node + ".tx", k=True, l=False)
            cmds.setAttr(node + ".ty", k=True, l=False)
            cmds.setAttr(node + ".tz", k=True, l=False)
        if (attr == "s") or (attr == "scale"):
            cmds.setAttr(node + ".sx", k=True, l=False)
            cmds.setAttr(node + ".sy", k=True, l=False)
            cmds.setAttr(node + ".sz", k=True, l=False)
        if (attr == "v") or (attr == "visibility"):
            cmds.setAttr(node + ".v", k=True, l=False)
    return True


def breakConnections(node, attributes=None):
    if not attributes:
        attributes = ['t', 'r', 's']

    for attr in attributes:
        cmds.setAttr(node + "." + attr, k=True, l=False, cb=True)
        if (attr == "r") or (attr == "rotate"):
            rx_source = cmds.connectionInfo(
                '{0}.rx'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(rx_source, '{0}.{1}'.format(node, 'rx'))
            ry_source = cmds.connectionInfo(
                '{0}.ry'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(ry_source, '{0}.{1}'.format(node, 'ry'))
            rz_source = cmds.connectionInfo(
                '{0}.rz'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(rz_source, '{0}.{1}'.format(node, 'rz'))
        if (attr == "t") or (attr == "translate"):
            tx_source = cmds.connectionInfo(
                '{0}.tx'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(tx_source, '{0}.{1}'.format(node, 'tx'))
            ty_source = cmds.connectionInfo(
                '{0}.ty'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(ty_source, '{0}.{1}'.format(node, 'ty'))
            tz_source = cmds.connectionInfo(
                '{0}.tz'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(tz_source, '{0}.{1}'.format(node, 'tz'))
        if (attr == "s") or (attr == "scale"):
            sx_source = cmds.connectionInfo(
                '{0}.sx'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(sx_source, '{0}.{1}'.format(node, 'sx'))
            sy_source = cmds.connectionInfo(
                '{0}.sy'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(sy_source, '{0}.{1}'.format(node, 'sy'))
            sz_source = cmds.connectionInfo(
                '{0}.sz'.format(node), sourceFromDestination=True)
            cmds.disconnectAttr(sz_source, '{0}.{1}'.format(node, 'sz'))


def removeAllNamespace():
    # set namespace to root namespace
    cmds.namespace(setNamespace="::")
    currentNameSpaces = cmds.namespaceInfo(listOnlyNamespaces=True)

    ignoreNamespaces = ['UI', 'shared']

    for namespace in currentNameSpaces:
        if namespace not in ignoreNamespaces:
            cmds.namespace(
                removeNamespace=namespace,
                mergeNamespaceWithRoot=True)


def bakeKeys(nodeName, minTimeValue, maxTimeValue):
    cmds.bakeResults(nodeName,
                     t=(int(minTimeValue),
                        int(maxTimeValue)),
                     simulation=True,
                     hierarchy='none',
                     sampleBy=1,
                     disableImplicitControl=True,
                     preserveOutsideKeys=True,
                     sparseAnimCurveBake=False,
                     removeBakedAttributeFromLayer=False,
                     removeBakedAnimFromLayer=True,
                     bakeOnOverrideLayer=False,
                     minimizeRotation=True,
                     controlPoints=False,
                     shape=True)


def cutKeys(nodeName, minTimeValue, maxTimeValue):
    time_range = cmds.keyframe(nodeName, query=True, timeChange=True)
    if not time_range:
        return
    keyMinT = min(time_range)
    keyMaxT = max(time_range)

    cmds.cutKey(nodeName, time=(keyMinT - 1, minTimeValue - 1), option="keys")
    cmds.cutKey(nodeName, time=(maxTimeValue + 1, keyMaxT + 1), option="keys")


def GetBlendShape(shape):
    # Create an MDagPath for our shape node:
    selList = OpenMaya.MSelectionList()
    selList.add(shape)
    mDagPath = OpenMaya.MDagPath()
    selList.getDagPath(0, mDagPath)

    # Create iterator.
    mItDependencyGraph = OpenMaya.MItDependencyGraph(
        mDagPath.node(),
        OpenMaya.MItDependencyGraph.kDownstream,
        OpenMaya.MItDependencyGraph.kBreadthFirst,
        OpenMaya.MItDependencyGraph.kPlugLevel)

    while not mItDependencyGraph.isDone():
        mObject = mItDependencyGraph.currentItem()
        if mObject.hasFn(OpenMaya.MFn.kBlendShape):
            # return the MFnSkinCluster object for our MObject:
            return OpenMaya.MFnDependencyNode(mObject).name()
        mItDependencyGraph.next()


def getAnimRange():
    minT = cmds.playbackOptions(min=True, q=True)
    maxT = cmds.playbackOptions(max=True, q=True)
    return (int(minT), int(maxT))
