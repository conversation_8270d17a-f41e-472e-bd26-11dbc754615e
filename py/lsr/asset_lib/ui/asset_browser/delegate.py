import os.path

from Qt import QtWidgets, QtCore, QtGui
from lsr.asset_lib.utility import pyside as uti_pyside
from lsr.asset_lib.data import var_global
from lsr.asset_lib.utility.pyside import resolve_icon_path


class CustomItemDelegate(QtWidgets.QStyledItemDelegate):
    def __init__(self, parent=None):
        super(CustomItemDelegate, self).__init__(parent)
        self.listView = parent

    def paint(self, painter, option, index):
        # get data / base setting
        uti_pyside.copy_maya_font_style(painter, size=9)
        rect = option.rect

        io_data = index.data(QtCore.Qt.UserRole)
        fixed_text = os.path.join(var_global.RESOURCE_ROOT_PATH, io_data["file_path"])
        fixed_text = os.path.normpath(fixed_text)
        rect_name = index.data(QtCore.Qt.DisplayRole)
        icon = index.data(QtCore.Qt.DecorationRole)

        # 设置绘制区域裁剪，确保内容不会超出rect边界
        painter.save()
        painter.setClipRect(rect)

        # pre set rect
        if self.listView.viewMode() == QtWidgets.QListView.ListMode:
            # List模式：图标在左侧，文本在右侧
            icon_size = 50
            icon_margin = 5
            text_margin = 10

            # 图标区域：左侧固定大小
            icon_rect = QtCore.QRect(
                rect.x() + icon_margin,
                rect.y() + (rect.height() - icon_size) // 2,  # 垂直居中
                icon_size,
                icon_size
            )

            # 文本区域：图标右侧，留出足够空间
            text_start_x = rect.x() + icon_size + icon_margin + text_margin
            text_rect = QtCore.QRect(
                text_start_x,
                rect.y() + 2,
                rect.width() - (text_start_x - rect.x()) - 5,  # 确保不超出右边界
                rect.height() // 2 - 2
            )

            # 路径文本区域：在文件名下方
            path_rect = QtCore.QRect(
                text_start_x,
                rect.y() + rect.height() // 2,
                rect.width() - (text_start_x - rect.x()) - 5,
                rect.height() // 2 - 2
            )

            alignment = QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter
        else:
            # Icon模式：图标在上方，文本在下方
            icon_margin = 0
            text_height = 20

            # 图标区域：上方，留出下方文本空间
            icon_rect = QtCore.QRect(
                rect.x() + icon_margin,
                rect.y() + icon_margin,
                rect.width() - 2 * icon_margin,
                rect.height() - text_height - 2 * icon_margin
            )

            # 文本区域：下方
            text_rect = QtCore.QRect(
                rect.x() + 2,
                rect.y() + rect.height() - text_height,
                rect.width() - 4,
                text_height
            )

            # Icon模式下不显示路径
            path_rect = None
            alignment = QtCore.Qt.AlignCenter
            icon_size = min(icon_rect.width(), icon_rect.height())

        # set painter
        if not os.path.exists(fixed_text):
            painter.setPen(QtGui.QPen(QtGui.QColor("red")))
            icon_path = resolve_icon_path(sub_dir='/library/warning.png')
            pixmap = QtGui.QPixmap(icon_path).scaled(icon_size, icon_size)
            icon = QtGui.QIcon(pixmap)
        else:
            painter.setPen(QtGui.QPen(QtGui.QColor("white")))

        # 绘制文件名
        painter.drawText(text_rect, alignment, rect_name)

        # 绘制路径（仅在List模式下）
        if self.listView.viewMode() == QtWidgets.QListView.ListMode and path_rect:
            # 使用较小的字体绘制路径
            painter.save()
            font = painter.font()
            font.setPointSize(7)
            painter.setFont(font)
            painter.setPen(QtGui.QPen(QtGui.QColor("lightgray")))
            painter.drawText(path_rect, QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter, fixed_text)
            painter.restore()

        # 绘制主图标
        icon.paint(painter, icon_rect, alignment)

        # file type icon - 调整位置避免遮挡
        if not any(io_data['icon_path'] == path for path in [
            resolve_icon_path(sub_dir='/library/fbx_icon.png'),
            resolve_icon_path(sub_dir='/library/maya_icon.png'),
            resolve_icon_path(sub_dir='/library/aslib_folder.png')
        ]):
            if io_data['item_tag'] == ['.fbx']:
                pixmap_type = 'fbx_icon'
            elif io_data['item_tag'] == ['.ma'] or io_data['item_tag'] == ['.mb']:
                pixmap_type = 'maya_icon'
            else:
                pixmap_type = 'aslib_folder'
            type_pixmap = QtGui.QPixmap(resolve_icon_path("/library/{}.png".format(pixmap_type)))
            type_pixmap = type_pixmap.scaled(QtCore.QSize(40, 40))  # 缩小类型图标

            # 调整类型图标位置，放在主图标的右下角
            if self.listView.viewMode() == QtWidgets.QListView.ListMode:
                type_rect = QtCore.QRect(
                    icon_rect.right() - 20,
                    icon_rect.bottom() - 20,
                    20, 20
                )
            else:
                type_rect = QtCore.QRect(
                    icon_rect.right() - 50,
                    icon_rect.bottom() - 50,
                    40, 40
                )
            painter.drawPixmap(type_rect, type_pixmap)

        # --------------------------------
        # selection
        border = QtGui.QPainterPath(rect.topLeft())
        border.lineTo(rect.topRight())
        border.lineTo(rect.bottomRight())
        border.lineTo(rect.bottomLeft())
        border.lineTo(rect.topLeft())
        painter.setBrush(QtGui.QBrush(QtCore.Qt.NoBrush))

        # draw hover highlight
        if option.state & QtWidgets.QStyle.State_MouseOver:
            painter.drawPath(border)
            color = QtGui.QColor("#ADD8E6")
            color.setAlpha(60)
            painter.fillRect(rect, color)

        # draw selection highlight
        if option.state & QtWidgets.QStyle.State_Selected:
            color = QtGui.QColor("#36B4EB")
            painter.drawPath(border)
            color.setAlpha(60)
            painter.fillRect(rect, color)

        # 恢复painter状态，移除裁剪区域
        painter.restore()

    def sizeHint(self, option, index):
        size = super(CustomItemDelegate, self).sizeHint(option, index)
        if self.listView.viewMode() == QtWidgets.QListView.ListMode:
            size.setHeight(size.height() + 20)
            return size
        else:
            return size
