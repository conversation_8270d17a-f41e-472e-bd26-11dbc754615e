import json
import requests

request = {
    # 函数参数列表
    'Parameters':
        {
            # 这个一定要有，就是你想要的是什么输入对应的轨迹，比如向前移动+向左移动就是左转，静止不动+向前移动就是静止起步这样，
            # 其实也可以加3段以上的输入，就能出来蛇形什么的
            'InInputLog':
                [
                    {
                        # 输入方向，现在有8方向+静止不动
                        # UENUM(BlueprintType)
                        # enum class EAnimInput : uint8
                        # {
                        #     EAI_None
                        #     EAI_Forward
                        #     EAI_ForwardRight
                        #     EAI_Right
                        #     EAI_BackwardRight
                        #     EAI_Backward
                        #     EAI_BackwardLeft
                        #     EAI_Left
                        #     EAI_ForwardLeft
                        # };
                        'MovementInput': 'EAI_Forward',
                        'RotationInput': 'EAI_Forward',

                        # 输入的持续时间
                        'Duration': 1,

                        # 运动模式(Casual, Combat, Aim, AimFire, HipFire, Cover, Panic)
                        'MovementPattern': 'Veer',

                        # 运动速度(Walk, Run, Sprint)
                        'MovementSpeed': 'Walk',
                    },
                    {
                        'MovementInput': 'EAI_None',
                        'RotationInput': 'EAI_None',

                        # 输入的持续时间
                        'Duration': 1,

                        # 运动模式(Casual, Combat, Aim, AimFire, HipFire, Cover, Panic)
                        'MovementPattern': 'Veer',

                        # 运动速度(Walk, Run, Sprint)
                        'MovementSpeed': 'Walk',
                    },

                ],
            # 返回轨迹的时间间隔，可以不配置，默认0.1
            'InTimeStep': 0.1,
        },
    # UE调用配置，可以保留这样不动
    "GenerateTransaction": False
}

request_json = json.dumps(request)

url = 'http://127.0.0.1:30010/remote/preset/RCP_TrajectoryExporter/function/GenerateMultipleMotionModelTrajectory(Player)'

r = requests.put(url, data=request_json)

if r.status_code == requests.codes.ok:
    print(r.text)
else:
    print(r.status_code)
    print(r.text)

# example output
# {
#         "ReturnedValues":
#         [{
#         "OutTrajectory": [
#                 {
#                         "XPlane":
#                         {
#                                 "W": 0,
#                                 "X": 1,
#                                 "Y": 0,
#                                 "Z": 0
#                         },
#                         "YPlane":
#                         {
#                                 "W": 0,
#                                 "X": 0,
#                                 "Y": 1,
#                                 "Z": 0
#                         },
#                         "ZPlane":
#                         {
#                                 "W": 0,
#                                 "X": 0,
#                                 "Y": 0,
#                                 "Z": 1
#                         },
#                         "WPlane":
#                         {
#                                 "W": 1,
#                                 "X": 0,
#                                 "Y": 0,
#                                 "Z": 0
#                         }
#                 },
#                 {
#                         "XPlane":
#                         {
#                                 "W": 0,
#                                 "X": 1,
#                                 "Y": 0,
#                                 "Z": 0
#                         },
#                         "YPlane":
#                         {
#                                 "W": 0,
#                                 "X": -0,
#                                 "Y": 1,
#                                 "Z": 0
#                         },
#                         "ZPlane":
#                         {
#                                 "W": 0,
#                                 "X": 0,
#                                 "Y": -0,
#                                 "Z": 1
#                         },
#                         "WPlane":
#                         {
#                                 "W": 1,
#                                 "X": 299.9999575316906,
#                                 "Y": 0,
#                                 "Z": 0
#                         }
#                 },
#                 {
#                         "XPlane":
#                         {
#                                 "W": 0,
#                                 "X": -4.4408920985006262e-16,
#                                 "Y": -1.0000000000000002,
#                                 "Z": 0
#                         },
#                         "YPlane":
#                         {
#                                 "W": 0,
#                                 "X": 1.0000000000000002,
#                                 "Y": -4.4408920985006262e-16,
#                                 "Z": 0
#                         },
#                         "ZPlane":
#                         {
#                                 "W": 0,
#                                 "X": 0,
#                                 "Y": 0,
#                                 "Z": 1
#                         },
#                         "WPlane":
#                         {
#                                 "W": 1,
#                                 "X": 323.26624830056284,
#                                 "Y": -290.59994533347816,
#                                 "Z": 0
#                         }
#                 }
#         ]
# }]
# }
