# -*- coding: utf-8 -*-

import re
from Qt import QtWidgets, QtCore, QtGui
from lsr.maya.anim_tools.ikfk_switcher.util import *
from lsr.qt.core.base_main_window import get_window_class


def IKFK(*args):
    """
    IKFK Command
    Args:
        *args ():
    """
    from lsr.maya.anim_tools.ikfk_switcher.util import SK_IKFKSwitchCommand
    SK_IKFKSwitchCommand(False, True)


def filter_curves(*args):
    """
    filter curves
    Args:
        *args ():

    Returns:
        None
    """
    from lsr.maya.anim_tools.ikfk_switcher import FilterAnimCurve
    FilterAnimCurve.filter_curve()


def gimbal(*args):
    """
    Gimbal Lock
    Args:
        *args ():

    Returns:

    """
    from lsr.maya.anim_tools.ikfk_switcher import GimbalLock
    GimbalLock.GimbalLock()


def ikfk_batch(*args):
    """
    ikfk batch
    Args:
        *args ():

    Returns:
        None
    """
    TimeIKFK_window = TimeIKFK_UI()
    TimeIKFK_window.launch()


base_class = get_window_class(app_name='IKFK_Batch_UI')


class TimeIKFK_UI(base_class):
    """
    TimeIKFK UI
    """

    def __init__(self):

        super(TimeIKFK_UI, self).__init__()
        # self.setup_ui()
        self.config_ui()
        self.mode = 'slider'
        self.start = 0
        self.end = 120
        self.state = 'FK'
        self.switch_ctrl = []

    def setup_ui(self):
        """setup ui"""
        self.centralwidget = QtWidgets.QWidget(self)
        self.gridLayout = QtWidgets.QGridLayout(self.centralwidget)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.slider_RB = QtWidgets.QRadioButton()
        self.slider_RB.setChecked(True)
        self.horizontalLayout_4.addWidget(self.slider_RB)
        self.custom_RB = QtWidgets.QRadioButton()
        self.horizontalLayout_4.addWidget(self.custom_RB)
        self.gridLayout.addLayout(self.horizontalLayout_4, 0, 0, 1, 1)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.label = QtWidgets.QLabel()
        self.horizontalLayout.addWidget(self.label)
        self.start_SB = QtWidgets.QSpinBox()
        self.start_SB.setMaximum(9999)
        self.horizontalLayout.addWidget(self.start_SB)
        self.horizontalLayout_3.addLayout(self.horizontalLayout)
        self.line = QtWidgets.QFrame()
        self.line.setMinimumSize(QtCore.QSize(20, 0))
        self.line.setFrameShape(QtWidgets.QFrame.VLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.horizontalLayout_3.addWidget(self.line)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.label_2 = QtWidgets.QLabel()
        self.horizontalLayout_2.addWidget(self.label_2)
        self.end_SB = QtWidgets.QSpinBox()
        self.end_SB.setMaximum(9999)
        self.end_SB.setProperty("value", 120)
        self.horizontalLayout_2.addWidget(self.end_SB)
        self.horizontalLayout_3.addLayout(self.horizontalLayout_2)
        self.gridLayout.addLayout(self.horizontalLayout_3, 1, 0, 1, 1)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.FK_BTN = QtWidgets.QPushButton()
        self.FK_BTN.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(20)
        self.FK_BTN.setFont(font)
        self.FK_BTN.setStyleSheet("color:rgb(255, 0, 127)")
        self.verticalLayout.addWidget(self.FK_BTN)
        self.IK_BTN = QtWidgets.QPushButton()
        self.IK_BTN.setMinimumSize(QtCore.QSize(0, 50))
        font = QtGui.QFont()
        font.setFamily("Arial")
        font.setPointSize(20)
        self.IK_BTN.setFont(font)
        self.IK_BTN.setStyleSheet("color:rgb(255, 255, 127)")
        self.verticalLayout.addWidget(self.IK_BTN)
        self.gridLayout.addLayout(self.verticalLayout, 2, 0, 1, 1)

        self.setWindowTitle(u"批量转IK&FK")
        self.slider_RB.setText(u"时间轴选择范围")
        self.custom_RB.setText(u"自定义范围")
        self.label.setText(u"起始帧")
        self.label_2.setText(u"结束帧")
        self.FK_BTN.setText(u"转为 FK")
        self.IK_BTN.setText(u"转为 IK")

        # self.setLayout(self.gridLayout)

        self.setCentralWidget(self.centralwidget)

    def config_ui(self):
        """config ui"""
        time_range = getAnimRange()
        self.start_SB.setValue(int(time_range[0]))
        self.end_SB.setValue(int(time_range[1]))
        self.setStyleSheet('font-family: Microsoft YaHei UI;font-size : 12px;')
        self.FK_BTN.setStyleSheet('font-size : 20px;color : rgb(0, 150, 255);')
        self.IK_BTN.setStyleSheet('font-size : 20px;color : rgb(255, 255, 0);')

        self.FK_BTN.clicked.connect(self.convert2FK)
        self.IK_BTN.clicked.connect(self.convert2IK)

    def convert2FK(self):
        """convert to FK"""
        self.get_parameter()
        self.state = 'FK'
        self.convert()

    def convert2IK(self):
        """convert to IK"""
        self.get_parameter()
        self.state = 'IK'
        self.convert()

    def getMode(self):
        if self.slider_RB.isChecked():
            return 'slider'
        elif self.custom_RB.isChecked():
            return 'custom'

    def get_parameter(self):
        self.mode = self.getMode()
        if self.mode == 'slider':
            time_range = getTimeRange()
            if time_range:
                self.start = int(time_range[0])
                self.end = int(time_range[1])
            else:
                OpenMaya.MGlobal.displayError(u'请选择时间滑块范围')

        elif self.mode == 'custom':
            self.start = self.start_SB.value()
            self.end = self.end_SB.value()

    def bake_limbs(self, RtArm, RtLeg, LfArm, LfLeg):
        RtLeg_reflist = [
            u'*:*RtLeg_Ankle_FK',
            u'*:*RtLegLeg_ball_FK',
            u'*:*RtLeg_Knee_FK',
            u'*:*RtLeg_Leg_FK',
            u'*:*RtLeg_Pole_ctrl',
            u'*:*RtLeg_Leg_IK']
        RtArm_reflist = [
            u'*:*RtArm_UpArm_FK',
            u'*:*RtArm_Elbow_FK',
            u'*:*RtArm_Wrist_FK',
            u'*:*RtArm_Pole_ctrl',
            u'*:*RtArm_Wrist_IK',
        ]
        LfLeg_reflist = [
            u'*:*LfLeg_Leg_FK',
            u'*:*LfLeg_Knee_FK',
            u'*:*LfLeg_Ankle_FK',
            u'*:*LfLegLeg_ball_FK',
            u'*:*LfLeg_Pole_ctrl',
            u'*:*LfLeg_Leg_IK']
        LfArm_reflist = [
            u'*:*LfArm_UpArm_FK',
            u'*:*LfArm_Elbow_FK',
            u'*:*LfArm_Wrist_FK',
            u'*:*LfArm_Pole_ctrl',
            u'*:*LfArm_Wrist_IK',
        ]

        RtLeg_orilist = [
            u'*RtLeg_Ankle_FK',
            u'*RtLegLeg_ball_FK',
            u'*RtLeg_Knee_FK',
            u'*RtLeg_Leg_FK',
            u'*RtLeg_Pole_ctrl',
            u'*RtLeg_Leg_IK']
        RtArm_orilist = [
            u'*RtArm_UpArm_FK',
            u'*RtArm_Elbow_FK',
            u'*RtArm_Wrist_FK',
            u'*RtArm_Pole_ctrl',
            u'*RtArm_Wrist_IK',
        ]
        LfLeg_orilist = [
            u'*LfLeg_Leg_FK',
            u'*LfLeg_Knee_FK',
            u'*LfLeg_Ankle_FK',
            u'*LfLegLeg_ball_FK',
            u'*LfLeg_Pole_ctrl',
            u'*LfLeg_Leg_IK']
        LfArm_orilist = [
            u'*LfArm_UpArm_FK',
            u'*LfArm_Elbow_FK',
            u'*LfArm_Wrist_FK',
            u'*LfArm_Pole_ctrl',
            u'*LfArm_Wrist_IK',
        ]

        limbs_ctrl = []
        if RtArm:
            ctrls = cmds.ls(RtArm_reflist)
            if not ctrls:
                ctrls = cmds.ls(RtArm_orilist)
            limbs_ctrl.extend(ctrls)

        if RtLeg:
            ctrls = cmds.ls(RtLeg_reflist)
            if not ctrls:
                ctrls = cmds.ls(RtLeg_orilist)
            limbs_ctrl.extend(ctrls)

        if LfArm:
            ctrls = cmds.ls(LfArm_reflist)
            if not ctrls:
                ctrls = cmds.ls(LfArm_orilist)
            limbs_ctrl.extend(ctrls)

        if LfLeg:
            ctrls = cmds.ls(LfLeg_reflist)
            if not ctrls:
                ctrls = cmds.ls(LfLeg_orilist)
            limbs_ctrl.extend(ctrls)

        cmds.bakeResults(limbs_ctrl,
                         t=(int(self.start),
                            int(self.end)),
                         simulation=True,
                         hierarchy='none',
                         sampleBy=1,
                         disableImplicitControl=True,
                         preserveOutsideKeys=True,
                         sparseAnimCurveBake=False,
                         removeBakedAttributeFromLayer=False,
                         removeBakedAnimFromLayer=True,
                         bakeOnOverrideLayer=False,
                         minimizeRotation=True,
                         controlPoints=False,
                         shape=False)

    def bake_switch(self):
        if self.switch_ctrl:
            cmds.bakeResults(self.switch_ctrl,
                             t=(int(self.start),
                                int(self.end)),
                             simulation=True,
                             hierarchy='none',
                             sampleBy=1,
                             disableImplicitControl=True,
                             preserveOutsideKeys=True,
                             sparseAnimCurveBake=False,
                             removeBakedAttributeFromLayer=False,
                             removeBakedAnimFromLayer=True,
                             bakeOnOverrideLayer=False,
                             minimizeRotation=True,
                             controlPoints=False,
                             shape=False)

    def convert(self):
        try:
            RtArm = False
            RtLeg = False
            LfArm = False
            LfLeg = False
            self.switch_ctrl = cmds.ls(sl=True)
            for ctrl in self.switch_ctrl:
                if re.match('.*LfArm_Switch', ctrl):
                    LfArm = True
                if re.match('.*RtLeg_Switch', ctrl):
                    RtLeg = True
                if re.match('.*LfLeg_Switch', ctrl):
                    LfLeg = True
                if re.match('.*RtArm_Switch', ctrl):
                    RtArm = True

            oristate = cmds.autoKeyframe(q=True, state=True)
            cmds.autoKeyframe(state=True)

            # Bake Ori Limb State
            self.bake_limbs(RtArm, RtLeg, LfArm, LfLeg)
            # Bake Switch
            self.bake_switch()

            for x in range(self.start, self.end + 1):
                cmds.currentTime(x)
                if self.state == 'FK':
                    ikswitch = []
                    for ctrl in self.switch_ctrl:
                        if cmds.getAttr('%s.IKFK' % ctrl) == 1:
                            ikswitch.append(ctrl)
                    if ikswitch:
                        cmds.select(ikswitch)
                        # cmd.Command('IKFK')
                        SK_IKFKSwitchCommand(False, True)

                elif self.state == 'IK':
                    ikswitch = []
                    for ctrl in self.switch_ctrl:
                        if cmds.getAttr('%s.IKFK' % ctrl) == 0:
                            ikswitch.append(ctrl)
                    if ikswitch:
                        cmds.select(ikswitch)
                        # cmd.Command('IKFK')
                        SK_IKFKSwitchCommand(False, True)

            cmds.autoKeyframe(state=oristate)
            cmds.select(cl=True)

        except BaseException:
            OpenMaya.MGlobal.displayError(u'没有合适的控制器')


if __name__ == '__main__':
    ikfk_batch()
