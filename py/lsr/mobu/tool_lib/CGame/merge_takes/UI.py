from functools import partial
from Qt import QtWidgets, QtCore
from lsr.qt.core.widgets.drag_drop_widget import DragDropLineEdit

import pyfbsdk as fb
from lsr.qt.core.base_main_window import get_window_class
from lsr.mobu.tool_lib.CGame.merge_takes.merge_from_files import merge_animations


# get the base main window class
base_class = get_window_class(app_name="CGame Batch Merge Tool v1.0")


class CGame_Batch_Merge_UI(base_class):
    """
    The main animation ExportTool_UI UI
    """

    _REUSE_SINGLETON = False

    def __init__(self):

        """ Creates and initializes this window. """
        self.__thread = None
        self.__cur_progress_max = 0
        self.__save_path = None
        super(CGame_Batch_Merge_UI, self).__init__(set_style=False, banner_widget=True, has_art=True)

        self.FBX_FILE_FILTERS = "FBX (*.fbx *.FBX);;FBX (*.FBX);;fbx (*.fbx);;All Files (*.*)"
        self.FBX_selected_filter = "FBX (*.fbx,*.FBX)"

        self.create_connections()

    def setup_ui(self, *args, **kwargs):
        """Creates UI elements."""
        # self.resize(441, 568)

        self.centralwidget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(self.centralwidget)
        vbox.setSpacing(3)
        vbox.setContentsMargins(5, 5, 5, 5)

        center_widget = QtWidgets.QWidget()
        anim_vbox = QtWidgets.QVBoxLayout(center_widget)

        rig_hbox = QtWidgets.QHBoxLayout()
        rig_path_lb = QtWidgets.QLabel("Mobu Rig File")
        rig_hbox.addWidget(rig_path_lb)
        self.rig_line = QtWidgets.QLineEdit(center_widget)
        self.rig_line.setPlaceholderText("Select Rig file")
        rig_hbox.addWidget(self.rig_line)
        self.rig_btn = QtWidgets.QPushButton(center_widget)
        self.rig_btn.setToolTip("Select Rig File")
        self.rig_btn.setText("Browse")

        rig_hbox.addWidget(self.rig_btn)
        rig_hbox.setStretch(0, 2)
        rig_hbox.setStretch(1, 5)
        rig_hbox.setStretch(2, 2)
        rig_hbox.setAlignment(QtCore.Qt.AlignLeft)
        anim_vbox.addLayout(rig_hbox)

        anim_vbox.addWidget(self._add_line_widget(), stretch=1)

        self.merge_checkbox = QtWidgets.QCheckBox("Merge In One File")
        anim_vbox.addWidget(self.merge_checkbox)

        self.opt_widget = self.add_opt_folder_widget()
        anim_vbox.addWidget(self.opt_widget)

        anim_vbox.addWidget(self._add_line_widget(), stretch=1)

        # animation files list
        _hbox = QtWidgets.QHBoxLayout()
        anim_lb = QtWidgets.QLabel("Animation Files")
        anim_lb.setAlignment(QtCore.Qt.AlignLeft)
        _hbox.addWidget(anim_lb)

        self.use_take_name_cb = QtWidgets.QCheckBox("Use Take Name")
        _hbox.addWidget(self.use_take_name_cb)

        anim_in_vbox = QtWidgets.QVBoxLayout()
        anim_in_vbox.addLayout(_hbox)

        gridLayout = QtWidgets.QGridLayout()

        scrollArea = QtWidgets.QScrollArea(center_widget)
        scrollArea.setWidgetResizable(True)

        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 395, 441))

        self.verticalLayout_new = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)

        self.animationFiles_lw = QtWidgets.QListWidget(self.scrollAreaWidgetContents)
        self.animationFiles_lw.setGeometry(QtCore.QRect(10, 10, 371, 400))
        self.animationFiles_lw.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)

        self.verticalLayout_new.addWidget(self.animationFiles_lw)

        scrollArea.setWidget(self.scrollAreaWidgetContents)
        gridLayout.addWidget(scrollArea, 0, 0, 1, 1)
        anim_in_vbox.addLayout(gridLayout)

        animBtn_hbox = QtWidgets.QHBoxLayout()
        self.animAdd_btn = QtWidgets.QPushButton(center_widget)
        self.animAdd_btn.setText("Add Files")
        animBtn_hbox.addWidget(self.animAdd_btn)
        self.animDel_btn = QtWidgets.QPushButton(center_widget)
        self.animDel_btn.setText("Delete Files")
        animBtn_hbox.addWidget(self.animDel_btn)
        self.animclear_btn = QtWidgets.QPushButton(center_widget)
        self.animclear_btn.setText("Clear List")
        animBtn_hbox.addWidget(self.animclear_btn)

        anim_in_vbox.addLayout(animBtn_hbox)
        anim_vbox.addLayout(anim_in_vbox)

        expAnimBtn_hbox = QtWidgets.QHBoxLayout()
        self.exportAnimPath_btn = QtWidgets.QPushButton(center_widget)
        self.exportAnimPath_btn.setStyleSheet("background-color : rgb(37, 138, 137);")
        self.exportAnimPath_btn.setText("Apply")
        expAnimBtn_hbox.addWidget(self.exportAnimPath_btn)
        anim_vbox.addLayout(expAnimBtn_hbox)

        vbox.addWidget(center_widget)

        self.setCentralWidget(self.centralwidget)

    def _add_line_widget(self, *args, **kwargs):
        line = QtWidgets.QFrame(self)
        line.setFrameShape(QtWidgets.QFrame.HLine)
        line.setFrameShadow(QtWidgets.QFrame.Sunken)
        return line

    def add_opt_folder_widget(self, *args, **kwargs):
        """ Add opt folder widget """
        opt_widget = QtWidgets.QWidget(self)
        vbox = QtWidgets.QVBoxLayout(opt_widget)
        self.opt_folder_lab = QtWidgets.QLabel("Output Folder", self)
        self.opt_folder_line = DragDropLineEdit(self)
        self.opt_folder_line.setPlaceholderText("Select Output Folder")
        self.opt_browse_btn = QtWidgets.QPushButton("Browse", self)

        _hbox = QtWidgets.QHBoxLayout()
        _hbox.addWidget(self.opt_folder_lab)
        _hbox.addWidget(self.opt_folder_line)
        _hbox.addWidget(self.opt_browse_btn)
        vbox.addLayout(_hbox)

        _hbox.setStretch(0, 2)
        _hbox.setStretch(1, 5)
        _hbox.setStretch(2, 2)

        return opt_widget

    def create_connections(self, *args, **kwargs):
        """Creates signal/slot connections."""
        self.animAdd_btn.clicked.connect(partial(self.show_anim_fbx_dialog))
        self.animDel_btn.clicked.connect(partial(self.delete_file_lw))
        self.animclear_btn.clicked.connect(partial(self.clear_file_lw))
        self.exportAnimPath_btn.clicked.connect(partial(self.apply))

        self.animationFiles_lw.itemDoubleClicked.connect(partial(self.item_double_clicked))
        self.rig_btn.clicked.connect(partial(self.show_rig_dialog))

        self.opt_browse_btn.clicked.connect(partial(self._browse_get_input))
        self.merge_checkbox.stateChanged.connect(partial(self.merge_checkbox_state_changed))

    def _browse_folder_get(self, *args, **kwargs):
        folder = QtWidgets.QFileDialog.getExistingDirectory(self, "Select Folder")
        return folder

    def _browse_get_input(self, *args, **kwargs):
        folder = self._browse_folder_get()
        if folder:
            self.opt_folder_line.setText(folder)

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

    def save_settings(self, *args, **kwargs):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(CGame_Batch_Merge_UI, self).save_settings()

        settings.beginGroup("export_anim")
        settings.setValue("rig_path", self.rig_line.text())

        fbx_files = []
        count = self.animationFiles_lw.count()
        for i in range(count):
            item = self.animationFiles_lw.item(i)
            fbx_files.append(item.text())
        settings.setValue("animation_file", fbx_files)

        settings.setValue("output_path", self.opt_folder_line.text())
        settings.setValue("merge_checkbox", self.merge_checkbox.isChecked())
        settings.setValue("use_take_name", self.use_take_name_cb.isChecked())

        settings.endGroup()

        settings.sync()
        return settings

    def load_settings(self, *args, **kwargs):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(CGame_Batch_Merge_UI, self).load_settings()

        settings.beginGroup("export_anim")
        self.rig_line.setText(settings.value("rig_path", ""))

        self.animationFiles_lw.clear()
        fbx_files = settings.value("animation_file", [])
        for fbx_file in fbx_files:
            self.animationFiles_lw.addItem(fbx_file)

        self.opt_folder_line.setText(settings.value("output_path", ""))

        merge_checkbox = settings.value('merge_checkbox', 'true')
        merge_checkbox = True if merge_checkbox == 'true' else False
        self.merge_checkbox.setChecked(merge_checkbox)

        use_take_name = settings.value('use_take_name', 'false')
        use_take_name = True if use_take_name == 'true' else False
        self.use_take_name_cb.setChecked(use_take_name)

        settings.endGroup()

        return settings

    def merge_checkbox_state_changed(self, state, *args, **kwargs):
        """ Merge checkbox state changed """

        if state == QtCore.Qt.Checked:
            self.opt_widget.setVisible(False)
        else:
            self.opt_widget.setVisible(True)

    def item_double_clicked(self, *args, **kwargs):
        """ Open file when double clicked """
        item = self.animationFiles_lw.selectedItems()[0]
        file_path = str(item.text())
        app = fb.FBApplication()
        app.FileOpen(file_path, False)

    def show_skin_fbx_dialog(self, *args, **kwargs):
        """ Show skin fbx dialog """
        file_path, self.FBX_selected_filter = QtWidgets.QFileDialog.getSaveFileName(
            self,
            "Save FBX File",
            "",
            self.FBX_FILE_FILTERS,
            self.FBX_selected_filter
        )
        if file_path:
            self.exportSkinPath_le.setText(file_path)

    def show_anim_fbx_dialog(self, *args, **kwargs):
        """ Show animation fbx dialog """
        file_paths, self.FBX_selected_filter = QtWidgets.QFileDialog.getOpenFileNames(
            self,
            "Select Animation Files",
            "",
            self.FBX_FILE_FILTERS,
            self.FBX_selected_filter
        )

        if not file_paths:
            return

        for anim_file in file_paths:
            self.animationFiles_lw.addItem(anim_file)

    def show_rig_dialog(self, *args, **kwargs):
        """ Show json dialog """
        file_path, self.FBX_FILE_FILTERS = \
            QtWidgets.QFileDialog.getOpenFileName(
                self,
                "Select RIG File",
                "",
                self.FBX_FILE_FILTERS)

        if file_path:
            self.rig_line.setText(file_path)

    def delete_file_lw(self, *args, **kwargs):
        """ Delete file from list widget """
        model_index = self.animationFiles_lw.selectedIndexes()
        for item in model_index:
            self.animationFiles_lw.takeItem(item.row())

    def clear_file_lw(self, *args, **kwargs):
        """ Clear all files from list widget """
        self.animationFiles_lw.clear()

    def get_animation_files(self, *args, **kwargs):
        """
        Get animation files from list widget
        """
        fbx_files = []
        count = self.animationFiles_lw.count()
        for i in range(count):
            item = self.animationFiles_lw.item(i)
            fbx_files.append(str(item.text()))
        return fbx_files

    def apply(self, *args, **kwargs):
        """ Apply button clicked"""
        rig_path = str(self.rig_line.text())
        animation_files = self.get_animation_files()
        kwargs = {}
        if not self.merge_checkbox.isChecked():
            kwargs["output_folder_path"] = str(self.opt_folder_line.text())
        else:
            kwargs["output_folder_path"] = ""

        kwargs["use_take_name"] = self.use_take_name_cb.isChecked()
        merge_animations(rig_path, animation_files, **kwargs)
