# -*- coding: utf-8 -*-
import os
from functools import partial

from Qt import QtWidgets, QtCore, QtGui
from lsr.qt.core.widgets.drag_drop_widget import DragDropLineEdit


class IOSelectionWidget(QtWidgets.QWidget):

    def __init__(self, parent=None, *args, **kwargs):
        super(IOSelectionWidget, self).__init__(parent)
        self.browse_fbx_file = False
        self._init_layout()
        self._do_signal_connection()

    def _init_layout(self):
        base_lay = QtWidgets.QHBoxLayout(self)
        base_lay.setAlignment(QtCore.Qt.AlignRight)
        base_lay.setContentsMargins(0, 0, 0, 0)

        self.setLayout(base_lay)

        self.tips_lab = QtWidgets.QLabel(self)
        self.tips_lab.setFixedWidth(200)
        self.line_edit = DragDropLineEdit(self)
        self.browse_btn = QtWidgets.QPushButton("Browse", self)

        base_lay.addWidget(self.tips_lab)
        base_lay.addWidget(self.line_edit)
        base_lay.addWidget(self.browse_btn)

    def _do_signal_connection(self):
        self.browse_btn.clicked.connect(partial(self._browse_get_input))

    def init_widget_msg(self, tips_msg, browse_fbx_file):
        self.tips_lab.setText(tips_msg)
        self.browse_fbx_file = browse_fbx_file

    def _browse_get_input(self, *args, **kwargs):
        if self.browse_fbx_file:
            file_path = QtWidgets.QFileDialog.getOpenFileName(self, "Select File", "", "FBX files (*.fbx)")[0]
        else:
            file_path = QtWidgets.QFileDialog.getExistingDirectory(self, "Select Folder")

        if file_path:
            file_path = os.path.normpath(file_path)
            self.line_edit.setText(file_path)

    def load_settings(self, settings, *args, **kwargs):
        line_text = settings.value(self.tips_lab.text(), '')
        self.line_edit.setText(line_text)

    def save_settings(self, settings, *args, **kwargs):
        settings.setValue(self.tips_lab.text(), self.line_edit.text())


class ManualBatchAnimToolView(QtWidgets.QWidget):

    def __init__(self, parent=None):
        super(ManualBatchAnimToolView, self).__init__(parent)
        self._init_layout()

    def _init_layout(self):
        base_lay = QtWidgets.QVBoxLayout(self)
        base_lay.setAlignment(QtCore.Qt.AlignTop)
        base_lay.setContentsMargins(0, 0, 0, 0)
        base_lay.setSpacing(0)
        self.setLayout(base_lay)

        self.input_fbx_folder_widget = IOSelectionWidget(self)
        self.input_fbx_folder_widget.init_widget_msg("Input FBX Folder", browse_fbx_file=False)
        self.input_fbx_source_rig_widget = IOSelectionWidget(self)
        self.input_fbx_source_rig_widget.init_widget_msg("Input FBX Source FBX", browse_fbx_file=True)
        self.input_fbx_target_rig_widget = IOSelectionWidget(self)
        self.input_fbx_target_rig_widget.init_widget_msg("Retarget Setting Finish FBX", browse_fbx_file=True)
        self.output_fbx_folder_widget = IOSelectionWidget(self)
        self.output_fbx_folder_widget.init_widget_msg("Output FBX Folder", browse_fbx_file=False)

        self.plot_fps_lab = QtWidgets.QLabel("Plot FPS:", self)
        self.plot_fps_spin_box = QtWidgets.QSpinBox(self)
        self.plot_fps_spin_box.setRange(1, 300)
        self.plot_fps_spin_box.setValue(30)
        hbox = QtWidgets.QHBoxLayout()
        hbox.setAlignment(QtCore.Qt.AlignLeft)
        hbox.addWidget(self.plot_fps_lab)
        hbox.addWidget(self.plot_fps_spin_box)

        self.is_exp_mesh_check_box = QtWidgets.QCheckBox('Export Mesh', self)
        self.run_btn = QtWidgets.QPushButton("Run", self)

        base_lay.addWidget(self.input_fbx_folder_widget)
        base_lay.addWidget(self.input_fbx_source_rig_widget)
        base_lay.addWidget(self.input_fbx_target_rig_widget)
        base_lay.addWidget(self.output_fbx_folder_widget)
        base_lay.addSpacing(10)
        base_lay.addLayout(hbox)
        base_lay.addSpacing(10)
        base_lay.addWidget(self.is_exp_mesh_check_box)
        base_lay.addSpacing(50)
        base_lay.addStretch()
        base_lay.addWidget(self.run_btn)

    def load_settings(self, settings, *args, **kwargs):
        self.plot_fps_spin_box.setValue(int(settings.value('plot_fps', 30)))
        self.input_fbx_folder_widget.load_settings(settings)
        self.input_fbx_source_rig_widget.load_settings(settings)
        self.input_fbx_target_rig_widget.load_settings(settings)
        self.output_fbx_folder_widget.load_settings(settings)

    def save_settings(self, settings, *args, **kwargs):
        settings.setValue('plot_fps', self.plot_fps_spin_box.value())
        self.input_fbx_folder_widget.save_settings(settings)
        self.input_fbx_source_rig_widget.save_settings(settings)
        self.input_fbx_target_rig_widget.save_settings(settings)
        self.output_fbx_folder_widget.save_settings(settings)


