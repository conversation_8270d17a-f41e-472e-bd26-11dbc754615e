// qgeorectangle.sip generated by MetaSIP
//
// This file is part of the QtPositioning Python extension module.
//
// Copyright (c) 2021 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_2_0 -)

class QGeoRectangle : QGeoShape
{
%TypeHeaderCode
#include <qgeorectangle.h>
%End

public:
    QGeoRectangle();
    QGeoRectangle(const QGeoCoordinate &center, double degreesWidth, double degreesHeight);
    QGeoRectangle(const QGeoCoordinate &topLeft, const QGeoCoordinate &bottomRight);
%If (Qt_5_3_0 -)
    QGeoRectangle(const QList<QGeoCoordinate> &coordinates);
%End
    QGeoRectangle(const QGeoRectangle &other);
    QGeoRectangle(const QGeoShape &other);
    ~QGeoRectangle();
    bool operator==(const QGeoRectangle &other) const;
    bool operator!=(const QGeoRectangle &other) const;
    void setTopLeft(const QGeoCoordinate &topLeft);
    QGeoCoordinate topLeft() const;
    void setTopRight(const QGeoCoordinate &topRight);
    QGeoCoordinate topRight() const;
    void setBottomLeft(const QGeoCoordinate &bottomLeft);
    QGeoCoordinate bottomLeft() const;
    void setBottomRight(const QGeoCoordinate &bottomRight);
    QGeoCoordinate bottomRight() const;
    void setCenter(const QGeoCoordinate &center);
    QGeoCoordinate center() const;
    void setWidth(double degreesWidth);
    double width() const;
    void setHeight(double degreesHeight);
    double height() const;
    bool contains(const QGeoRectangle &rectangle) const;
    bool intersects(const QGeoRectangle &rectangle) const;
    void translate(double degreesLatitude, double degreesLongitude);
    QGeoRectangle translated(double degreesLatitude, double degreesLongitude) const;
    QGeoRectangle united(const QGeoRectangle &rectangle) const;
    QGeoRectangle &operator|=(const QGeoRectangle &rectangle);
    QGeoRectangle operator|(const QGeoRectangle &rectangle) const;
%If (Qt_5_5_0 -)
    QString toString() const;
%End
%If (Qt_5_9_0 -)
    void extendRectangle(const QGeoCoordinate &coordinate);
%End
};

%End
