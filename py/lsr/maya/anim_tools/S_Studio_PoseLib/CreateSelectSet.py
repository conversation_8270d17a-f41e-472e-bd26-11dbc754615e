# -*- coding: utf-8 -*-

import maya.cmds as cmds

try:
    import cPickle as pickle
except ImportError:
    import pickle


class CreateSelectSet(object):
    """Create Select Set"""

    def __init__(self, path):
        self.path = path
        self.objs = {}
        self.transformType = [
            'hikFKJoint',
            'transform',
            'joint',
            'hikIKEffector']

    def collect(self):
        selection = cmds.ls(sl=True)
        for sel in selection:
            if cmds.nodeType(sel) in self.transformType:
                attrs = cmds.listAttr(sel, l=False, k=True)
                if not attrs:
                    continue
                for attr in attrs:
                    key = '%s.%s' % (sel.split(':')[-1], attr)
                    self.objs[key] = cmds.getAttr('%s.%s' % (sel, attr))
                    # self.objs.append('%s.%s' % (sel.split(':')[-1], attr))

    def save(self):
        self.collect()
        with open(self.path, 'wb') as f:
            pickle.dump(self.objs, f)
        cmds.confirmDialog(title=u'提示', message=u'创建完成', button='OK')
