#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线编辑器使用示例
"""

import sys
from PySide2 import QtWidgets
from bezier_curve_editor import BezierCurveEditor


def main():
    """主函数"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建编辑器窗口
    editor = BezierCurveEditor()
    editor.show()
    
    # 设置窗口样式
    editor.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 12px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
    """)
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
