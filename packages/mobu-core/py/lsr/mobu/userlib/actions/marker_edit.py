"""
MotionBuilder Optimize Fbx File Actions
"""

import os
import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
import lsr.protostar.core.exception as exp
from lsr.mobu.base_actions import RigAction
import lsr.mobu.rig.maker_util as maker_util
from lsr.mobu.rig.constants import LOOK_KEYS


class AddMarkers(RigAction):
    """
    Remove NameSpace Action
    """

    @pa.list_param(item_type='str')
    def joints(self):
        """joints list."""

    @pa.enum_param(items=LOOK_KEYS, default='kFBMarkerLookCube')
    def shape_type(self):
        """shape look type."""

    @pa.bool_param(default=True)
    def set_level(self):
        """If True, will set level by order."""

    def run(self):
        """Executes this action."""
        markers = maker_util.create_markers(
            joints_list=self.joints.value,
            set_level=self.set_level.value,
            shape_type=self.shape_type.enum_value)

        self.info("Added {} markers".format(len(markers)))
