"""
This module contains the AnimTakeEdit class, which is used to edit takes.
"""
import pyfbsdk as fb
import lsr.mobu.utils.FindObjects as FindObjects
from lsr.mobu.nodezoo.node import Character


class TakeEdit(object):
    """ Take Edit Class"""
    def __init__(self, *args, **kwargs):
        self.system = fb.FBSystem()
        self.current_take = self.system.CurrentTake

    def refresh_current_take(self, *args, **kwargs):
        """ Refresh the current take """
        self.current_take = self.system.CurrentTake

    def switch_to_previous_take(self, *args, **kwargs):
        """
        Switch to the previous take.
        Look in the list until we find the index of the current take.

        Returns:
            None
        """
        current_index = 0
        for takeIdx in range(len(self.system.Scene.Takes)):
            if self.system.Scene.Takes[takeIdx].Name == self.current_take.Name:
                current_index = takeIdx
                break

        # Decrement the index to point to the next take.
        # Here we take advantage of <PERSON>'s subscripting access
        # where -1 points to the last object in the list, which is
        # where we want to be if the current take is the first in the list.
        current_index -= 1

        # Set the current take using our new index.
        self.system.CurrentTake = self.system.Scene.Takes[current_index]
        self.refresh_current_take()

    def switch_to_next_take(self, *args, **kwargs):
        """
        Switch to the next take.
        Look in the list until we find the index of the current take.

        Returns:
            None
        """
        current_index = 0
        for takeIdx in range(len(self.system.Scene.Takes)):
            if self.system.Scene.Takes[takeIdx].Name == self.current_take.Name:
                current_index = takeIdx
                break

        # Increment the index to point to the next take.
        current_index += 1

        # Wrap around the end if the current take is the last.
        if current_index == len(self.system.Scene.Takes):
            current_index = 0

        # Set the current take using our new index.
        self.system.CurrentTake = self.system.Scene.Takes[current_index]
        self.refresh_current_take()

    def delete_current_take(self, *args, **kwargs):
        """
        Delete the current take.

        Returns:
            None
        """
        result = fb.FBMessageBox(
            "Warning",
            "Are you sure you want to delete this take?\nWARNING: You cannot undo this operation!",
            "Yes",
            "No")

        if result == 1:
            self.current_take.FBDelete()
        self.refresh_current_take()

        del result

    def copy_current_take(self, take_name="", *args, **kwargs):
        """
        Copy the current take.

        Returns:
            None
        """
        if not take_name or take_name == self.current_take.Name:
            take_name = "Copy_" + self.current_take.Name
        else:
            take_name = take_name

        self.current_take.CopyTake(take_name)

    def crop_current_take(self, start_time, end_time, *args, **kwargs):
        """
        Crop the current take to the specified time range.
        Args:
            start_time (str or int): The start time
            end_time (str or int): The end time

        Returns:
            None
        """
        if not isinstance(start_time, int):
            start_time = int(start_time)
        if not isinstance(end_time, int):
            end_time = int(end_time)

        is_range_valid = self.is_frame_range_valid(start_time, end_time)
        if is_range_valid:
            FindObjects.clear_selection()
            character = fb.FBApplication().CurrentCharacter
            if character:
                char_node = Character(character)
                ori_state = char_node.fb_node.ActiveInput
                char_node.plot_bone_animation()
                char_node.fb_node.ActiveInput = ori_state

            self.current_take.LocalTimeSpan = fb.FBTimeSpan(
                fb.FBTime(0, 0, 0, start_time),
                fb.FBTime(0, 0, 0, end_time)
            )
            # self.current_take.PlotTakeOnSelected(fb.FBTime(0, 0, 0, 1))

            FindObjects.clear_selection()

    def crop_to_new_take(self, take_name, start_time, end_time, is_stay=False, *args, **kwargs):
        """
        Crop the current take to the specified time range and create a new take.
        Args:
            take_name (str): The name of the new take
            start_time (str or int): The start time
            end_time (str or int): The end time
            is_stay (bool): Stay in the new take

        Returns:
            None
        """
        is_range_valid = self.is_frame_range_valid(start_time, end_time)
        if is_range_valid:
            self.current_take.CopyTake(take_name)
            self.crop_current_take(start_time, end_time)
            print(self.current_take)
            if is_stay:
                self.system.CurrentTake = self.current_take
            else:
                self.refresh_current_take()

    @staticmethod
    def is_frame_range_valid(start_time, end_time, *args, **kwargs):
        """
        Check if the frame range is valid
        Args:
            start_time (str or int): The start time
            end_time (str or int): The end time

        Returns:
            bool: True if the frame range is valid
        """

        # Does Start Frame Conflict with End Frame
        if not isinstance(start_time, int):
            start_time = int(start_time)
        if not isinstance(end_time, int):
            end_time = int(end_time)

        if end_time > start_time:
            return True
        else:
            fb.FBMessageBox("Frame Input Error",
                            "    The End Frame needs to be higher than the Start Frame!",
                            "OK")
            return False

    @staticmethod
    def get_current_frame(*args, **kwargs):
        """
        Get the current frame

        Returns:
            frame (int): The current frame
        """
        frame = fb.FBSystem().LocalTime.GetFrame()
        return frame

    def switch_to_take(self, take_name, *args, **kwargs):
        """
        Switch to the specified take.
        Args:
            take_name (str): The name of the take

        Returns:
            None
        """
        for take in self.system.Scene.Takes:
            if take.Name == take_name:
                self.system.CurrentTake = take
                break

    @staticmethod
    def get_take_list(*args, **kwargs):
        """
        Get the list of takes in the scene.

        Returns:
            take_list (list): The list of takes in the scene
        """
        take_list = []
        for take in fb.FBSystem().Scene.Takes:
            take_list.append(take.Name)
        return take_list

    @staticmethod
    def rename_take(old_name, new_name, *args, **kwargs):
        """
        Rename the specified take.
        Args:
            old_name (str): The old name of the take
            new_name (str): The new name of the take

        Returns:
            None
        """
        for take in fb.FBSystem().Scene.Takes:
            if take.Name == old_name:
                take.Name = new_name
                break