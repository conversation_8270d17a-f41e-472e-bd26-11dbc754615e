from lsr.mobu.nodezoo.attribute.attribute_ import Attribute


class EnumAttribute(Attribute):
    """
    This class is a wrapper for FBPropertyEnum
    """

    def add_enum_list(self, value):
        """
        Add enum list to attribute
        """
        if not isinstance(value, list):
            raise ValueError('value must be a list')
        enum_list = self.__plug__.GetEnumStringList(True)
        for item in value:
            enum_list.Add(item)

        # Verify the enum list has changed to update
        self.__plug__.NotifyEnumStringListChanged()
