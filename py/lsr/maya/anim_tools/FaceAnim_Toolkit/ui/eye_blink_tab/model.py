#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线编辑器 - Model层
负责数据管理、业务逻辑和状态管理
"""

import copy
import math
from PySide2 import QtCore


class BezierPoint:
    """贝塞尔曲线关键点数据模型"""
    
    def __init__(self, time=0.0, value=0.0, in_tangent=(0, 0), out_tangent=(0, 0)):
        self.time = time
        self.value = value
        self.in_tangent = in_tangent    # 入切线
        self.out_tangent = out_tangent  # 出切线
        self.selected = False
    
    def __eq__(self, other):
        """比较两个关键点是否相等"""
        if not isinstance(other, BezierPoint):
            return False
        return (self.time == other.time and 
                self.value == other.value and
                self.in_tangent == other.in_tangent and
                self.out_tangent == other.out_tangent)
    
    def copy(self):
        """创建关键点的副本"""
        return BezierPoint(self.time, self.value, self.in_tangent, self.out_tangent)


class BezierCurveModel(QtCore.QObject):
    """贝塞尔曲线数据模型"""
    
    # 信号定义
    keyframes_changed = QtCore.Signal(list)  # 关键帧列表改变
    selected_keyframe_changed = QtCore.Signal(object)  # 选中关键帧改变
    view_range_changed = QtCore.Signal(tuple, tuple)  # 视图范围改变
    undo_redo_state_changed = QtCore.Signal(bool, bool)  # 撤销重做状态改变
    
    def __init__(self, parent=None):
        super(BezierCurveModel, self).__init__(parent)
        
        # 核心数据
        self.keyframes = []
        self.selected_keyframe = None
        
        # 视图参数
        self.time_range = (0.0, 10.0)
        self.value_range = (-1.0, 1.0)
        self.grid_enabled = True
        
        # 撤销/重做系统
        self.undo_stack = []
        self.redo_stack = []
        self.max_undo_steps = 50
        
        # 初始化默认数据
        self._initialize_default_data()
    
    def _initialize_default_data(self):
        """初始化默认关键帧数据"""
        self.add_keyframe(0.0, 0.0, save_state=False)
        self.add_keyframe(5.0, 1.0, save_state=False)
        self.add_keyframe(10.0, 0.0, save_state=False)
        self.save_state("初始状态")
    
    # ==================== 关键帧管理 ====================
    
    def add_keyframe(self, time, value, save_state=True):
        """添加关键帧"""
        if save_state:
            self.save_state(f"添加关键帧 ({time:.3f}, {value:.3f})")
        
        # 计算默认切线
        in_tangent = (-1.0, 0.0)
        out_tangent = (1.0, 0.0)
        
        keyframe = BezierPoint(time, value, in_tangent, out_tangent)
        self.keyframes.append(keyframe)
        self.keyframes.sort(key=lambda k: k.time)
        
        self.keyframes_changed.emit(self.keyframes)
        return keyframe
    
    def remove_keyframe(self, keyframe, save_state=True):
        """删除关键帧"""
        if keyframe in self.keyframes and len(self.keyframes) > 2:
            if save_state:
                self.save_state(f"删除关键帧 ({keyframe.time:.3f}, {keyframe.value:.3f})")
            
            self.keyframes.remove(keyframe)
            if self.selected_keyframe == keyframe:
                self.set_selected_keyframe(None)
            
            self.keyframes_changed.emit(self.keyframes)
            return True
        return False
    
    def update_keyframe(self, keyframe, time=None, value=None, in_tangent=None, out_tangent=None, save_state=True):
        """更新关键帧属性"""
        if keyframe not in self.keyframes:
            return False
        
        changes = []
        if time is not None and time != keyframe.time:
            changes.append(f"时间 {keyframe.time:.3f} -> {time:.3f}")
            keyframe.time = time
        
        if value is not None and value != keyframe.value:
            changes.append(f"值 {keyframe.value:.3f} -> {value:.3f}")
            keyframe.value = value
        
        if in_tangent is not None and in_tangent != keyframe.in_tangent:
            changes.append("入切线")
            keyframe.in_tangent = in_tangent
        
        if out_tangent is not None and out_tangent != keyframe.out_tangent:
            changes.append("出切线")
            keyframe.out_tangent = out_tangent
        
        if changes and save_state:
            self.save_state(f"修改 {', '.join(changes)}")
        
        if changes:
            # 如果时间改变了，重新排序
            if time is not None:
                self.keyframes.sort(key=lambda k: k.time)
            
            self.keyframes_changed.emit(self.keyframes)
            return True
        
        return False
    
    def set_selected_keyframe(self, keyframe):
        """设置选中的关键帧"""
        if self.selected_keyframe != keyframe:
            self.selected_keyframe = keyframe
            self.selected_keyframe_changed.emit(keyframe)
    
    def get_keyframe_index(self, keyframe):
        """获取关键帧在列表中的索引"""
        if keyframe and keyframe in self.keyframes:
            return self.keyframes.index(keyframe)
        return -1
    
    # ==================== 曲线计算 ====================
    
    def evaluate_curve(self, time):
        """计算指定时间的曲线值"""
        if not self.keyframes:
            return 0.0
        
        # 边界情况
        if time <= self.keyframes[0].time:
            return self.keyframes[0].value
        if time >= self.keyframes[-1].time:
            return self.keyframes[-1].value
        
        # 找到相邻的两个关键帧
        for i in range(len(self.keyframes) - 1):
            kf1 = self.keyframes[i]
            kf2 = self.keyframes[i + 1]
            
            if kf1.time <= time <= kf2.time:
                # 使用贝塞尔插值
                t = (time - kf1.time) / (kf2.time - kf1.time)
                
                p0 = (kf1.time, kf1.value)
                p1 = (kf1.time + kf1.out_tangent[0], kf1.value + kf1.out_tangent[1])
                p2 = (kf2.time + kf2.in_tangent[0], kf2.value + kf2.in_tangent[1])
                p3 = (kf2.time, kf2.value)
                
                # 三次贝塞尔曲线
                value = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
                return value
        
        return 0.0
    
    def get_curve_points(self, steps=200):
        """获取曲线上的点列表，用于绘制"""
        if len(self.keyframes) < 2:
            return []
        
        points = []
        start_time = self.time_range[0]
        end_time = self.time_range[1]
        
        for i in range(steps + 1):
            t = start_time + (end_time - start_time) * i / steps
            value = self.evaluate_curve(t)
            points.append((t, value))
        
        return points
    
    # ==================== 视图范围管理 ====================
    
    def set_time_range(self, start, end):
        """设置时间范围"""
        if start < end and (start, end) != self.time_range:
            self.time_range = (start, end)
            self.view_range_changed.emit(self.time_range, self.value_range)
    
    def set_value_range(self, min_val, max_val):
        """设置值范围"""
        if min_val < max_val and (min_val, max_val) != self.value_range:
            self.value_range = (min_val, max_val)
            self.view_range_changed.emit(self.time_range, self.value_range)
    
    def set_grid_enabled(self, enabled):
        """设置网格显示"""
        if self.grid_enabled != enabled:
            self.grid_enabled = enabled
            # 可以发射信号通知视图更新
    
    # ==================== 撤销/重做系统 ====================
    
    def save_state(self, action_name="操作"):
        """保存当前状态到撤销栈"""
        state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': action_name
        }
        
        self.undo_stack.append(state)
        
        # 限制撤销栈大小
        if len(self.undo_stack) > self.max_undo_steps:
            self.undo_stack.pop(0)
        
        # 清空重做栈
        self.redo_stack.clear()
        
        # 更新撤销重做状态
        self._update_undo_redo_state()
    
    def undo(self):
        """撤销操作"""
        if len(self.undo_stack) <= 1:  # 保留初始状态
            return False
        
        # 保存当前状态到重做栈
        current_state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': "当前状态"
        }
        self.redo_stack.append(current_state)
        
        # 恢复上一个状态
        self.undo_stack.pop()  # 移除当前状态
        previous_state = self.undo_stack[-1]  # 获取上一个状态
        
        self._restore_state(previous_state)
        self._update_undo_redo_state()
        return True
    
    def redo(self):
        """重做操作"""
        if not self.redo_stack:
            return False
        
        # 保存当前状态到撤销栈
        current_state = {
            'keyframes': copy.deepcopy(self.keyframes),
            'selected_keyframe_index': self.get_keyframe_index(self.selected_keyframe),
            'action_name': "撤销前状态"
        }
        self.undo_stack.append(current_state)
        
        # 恢复重做状态
        redo_state = self.redo_stack.pop()
        self._restore_state(redo_state)
        self._update_undo_redo_state()
        return True
    
    def _restore_state(self, state):
        """恢复状态"""
        self.keyframes = copy.deepcopy(state['keyframes'])
        
        # 恢复选中的关键帧
        selected_index = state['selected_keyframe_index']
        if 0 <= selected_index < len(self.keyframes):
            self.selected_keyframe = self.keyframes[selected_index]
        else:
            self.selected_keyframe = None
        
        # 发射信号
        self.keyframes_changed.emit(self.keyframes)
        self.selected_keyframe_changed.emit(self.selected_keyframe)
    
    def _update_undo_redo_state(self):
        """更新撤销重做状态"""
        can_undo = len(self.undo_stack) > 1
        can_redo = len(self.redo_stack) > 0
        self.undo_redo_state_changed.emit(can_undo, can_redo)
    
    def can_undo(self):
        """是否可以撤销"""
        return len(self.undo_stack) > 1
    
    def can_redo(self):
        """是否可以重做"""
        return len(self.redo_stack) > 0
    
    # ==================== 数据导入导出 ====================
    
    def export_data(self):
        """导出曲线数据"""
        return {
            'keyframes': [(kf.time, kf.value, kf.in_tangent, kf.out_tangent) for kf in self.keyframes],
            'time_range': self.time_range,
            'value_range': self.value_range
        }
    
    def import_data(self, data):
        """导入曲线数据"""
        self.save_state("导入数据")
        
        self.keyframes.clear()
        for time, value, in_tangent, out_tangent in data.get('keyframes', []):
            keyframe = BezierPoint(time, value, in_tangent, out_tangent)
            self.keyframes.append(keyframe)
        
        if 'time_range' in data:
            self.time_range = data['time_range']
        if 'value_range' in data:
            self.value_range = data['value_range']
        
        self.selected_keyframe = None
        self.keyframes_changed.emit(self.keyframes)
        self.selected_keyframe_changed.emit(None)
        self.view_range_changed.emit(self.time_range, self.value_range)
    
    def clear_all(self):
        """清空所有数据"""
        self.save_state("清空所有数据")
        self.keyframes.clear()
        self.selected_keyframe = None
        self.keyframes_changed.emit(self.keyframes)
        self.selected_keyframe_changed.emit(None)
