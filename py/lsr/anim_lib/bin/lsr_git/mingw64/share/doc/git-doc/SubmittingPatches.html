<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>Submitting Patches</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="article">
<div id="header">
<h1>Submitting Patches</h1>
</div>
<div id="content">
<div class="sect1">
<h2 id="_guidelines">Guidelines</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Here are some guidelines for people who want to contribute their code to this
software. There is also a <a href="MyFirstContribution.html">step-by-step tutorial</a>
available which covers many of these same guidelines.</p>
</div>
<div class="sect2">
<h3 id="base-branch">Decide what to base your work on.</h3>
<div class="paragraph">
<p>In general, always base your work on the oldest branch that your
change is relevant to.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A bugfix should be based on <code>maint</code> in general. If the bug is not
present in <code>maint</code>, base it on <code>master</code>. For a bug that&#8217;s not yet
in <code>master</code>, find the topic that introduces the regression, and
base your work on the tip of the topic.</p>
</li>
<li>
<p>A new feature should be based on <code>master</code> in general. If the new
feature depends on other topics that are in <code>next</code>, but not in
<code>master</code>, fork a branch from the tip of <code>master</code>, merge these topics
to the branch, and work on that branch.  You can remind yourself of
how you prepared the base with <code>git log --first-parent master..</code>.</p>
</li>
<li>
<p>Corrections and enhancements to a topic not yet in <code>master</code> should
be based on the tip of that topic. If the topic has not been merged
to <code>next</code>, it&#8217;s alright to add a note to squash minor corrections
into the series.</p>
</li>
<li>
<p>In the exceptional case that a new feature depends on several topics
not in <code>master</code>, start working on <code>next</code> or <code>seen</code> privately and
send out patches only for discussion. Once your new feature starts
to stabilize, you would have to rebase it (see the "depends on other
topics" above).</p>
</li>
<li>
<p>Some parts of the system have dedicated maintainers with their own
repositories (see the section "Subsystems" below).  Changes to
these parts should be based on their trees.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>To find the tip of a topic branch, run <code>git log --first-parent
master..seen</code> and look for the merge commit. The second parent of this
commit is the tip of the topic branch.</p>
</div>
</div>
<div class="sect2">
<h3 id="separate-commits">Make separate commits for logically separate changes.</h3>
<div class="paragraph">
<p>Unless your patch is really trivial, you should not be sending
out a patch that was generated between your working tree and
your commit head.  Instead, always make a commit with complete
commit message and generate a series of patches from your
repository.  It is a good discipline.</p>
</div>
<div class="paragraph">
<p>Give an explanation for the change(s) that is detailed enough so
that people can judge if it is good thing to do, without reading
the actual patch text to determine how well the code does what
the explanation promises to do.</p>
</div>
<div class="paragraph">
<p>If your description starts to get too long, that&#8217;s a sign that you
probably need to split up your commit to finer grained pieces.
That being said, patches which plainly describe the things that
help reviewers check the patch, and future maintainers understand
the code, are the most beautiful patches.  Descriptions that summarize
the point in the subject well, and describe the motivation for the
change, the approach taken by the change, and if relevant how this
differs substantially from the prior version, are all good things
to have.</p>
</div>
<div class="paragraph">
<p>Make sure that you have tests for the bug you are fixing.  See
<code>t/README</code> for guidance.</p>
</div>
<div id="tests" class="paragraph">
<p>When adding a new feature, make sure that you have new tests to show
the feature triggers the new behavior when it should, and to show the
feature does not trigger when it shouldn&#8217;t.  After any code change,
make sure that the entire test suite passes.  When fixing a bug, make
sure you have new tests that break if somebody else breaks what you
fixed by accident to avoid regression.  Also, try merging your work to
<em>next</em> and <em>seen</em> and make sure the tests still pass; topics by others
that are still in flight may have unexpected interactions with what
you are trying to do in your topic.</p>
</div>
<div class="paragraph">
<p>Pushing to a fork of <a href="https://github.com/git/git" class="bare">https://github.com/git/git</a> will use their CI
integration to test your changes on Linux, Mac and Windows. See the
<a href="#GHCI">GitHub CI</a> section for details.</p>
</div>
<div class="paragraph">
<p>Do not forget to update the documentation to describe the updated
behavior and make sure that the resulting documentation set formats
well (try the Documentation/doc-diff script).</p>
</div>
<div class="paragraph">
<p>We currently have a liberal mixture of US and UK English norms for
spelling and grammar, which is somewhat unfortunate.  A huge patch that
touches the files all over the place only to correct the inconsistency
is not welcome, though.  Potential clashes with other changes that can
result from such a patch are not worth it.  We prefer to gradually
reconcile the inconsistencies in favor of US English, with small and
easily digestible patches, as a side effect of doing some other real
work in the vicinity (e.g. rewriting a paragraph for clarity, while
turning en_UK spelling to en_US).  Obvious typographical fixes are much
more welcomed ("teh &#8594; "the"), preferably submitted as independent
patches separate from other documentation changes.</p>
</div>
<div id="whitespace-check" class="paragraph">
<p>Oh, another thing.  We are picky about whitespaces.  Make sure your
changes do not trigger errors with the sample pre-commit hook shipped
in <code>templates/hooks--pre-commit</code>.  To help ensure this does not happen,
run <code>git diff --check</code> on your changes before you commit.</p>
</div>
</div>
<div class="sect2">
<h3 id="describe-changes">Describe your changes well.</h3>
<div class="paragraph">
<p>The log message that explains your changes is just as important as the
changes themselves.  Your code may be clearly written with in-code
comment to sufficiently explain how it works with the surrounding
code, but those who need to fix or enhance your code in the future
will need to know <em>why</em> your code does what it does, for a few
reasons:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Your code may be doing something differently from what you wanted it
to do.  Writing down what you actually wanted to achieve will help
them fix your code and make it do what it should have been doing
(also, you often discover your own bugs yourself, while writing the
log message to summarize the thought behind it).</p>
</li>
<li>
<p>Your code may be doing things that were only necessary for your
immediate needs (e.g. "do X to directories" without implementing or
even designing what is to be done on files).  Writing down why you
excluded what the code does not do will help guide future developers.
Writing down "we do X to directories, because directories have
characteristic Y" would help them infer "oh, files also have the same
characteristic Y, so perhaps doing X to them would also make sense?".
Saying "we don&#8217;t do the same X to files, because &#8230;&#8203;" will help them
decide if the reasoning is sound (in which case they do not waste
time extending your code to cover files), or reason differently (in
which case, they can explain why they extend your code to cover
files, too).</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>The goal of your log message is to convey the <em>why</em> behind your
change to help future developers.</p>
</div>
<div class="paragraph">
<p>The first line of the commit message should be a short description (50
characters is the soft limit, see DISCUSSION in <a href="git-commit.html">git-commit(1)</a>),
and should skip the full stop.  It is also conventional in most cases to
prefix the first line with "area: " where the area is a filename or
identifier for the general area of the code being modified, e.g.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>doc: clarify distinction between sign-off and pgp-signing</p>
</li>
<li>
<p>githooks.txt: improve the intro section</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>If in doubt which identifier to use, run <code>git log --no-merges</code> on the
files you are modifying to see the current conventions.</p>
</div>
<div id="summary-section" class="paragraph">
<p>The title sentence after the "area:" prefix omits the full stop at the
end, and its first word is not capitalized unless there is a reason to
capitalize it other than because it is the first word in the sentence.
E.g. "doc: clarify&#8230;&#8203;", not "doc: Clarify&#8230;&#8203;", or "githooks.txt:
improve&#8230;&#8203;", not "githooks.txt: Improve&#8230;&#8203;".  But "refs: HEAD is also
treated as a ref" is correct, as we spell <code>HEAD</code> in all caps even when
it appears in the middle of a sentence.</p>
</div>
<div id="meaningful-message" class="paragraph">
<p>The body should provide a meaningful commit message, which:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>explains the problem the change tries to solve, i.e. what is wrong
with the current code without the change.</p>
</li>
<li>
<p>justifies the way the change solves the problem, i.e. why the
result with the change is better.</p>
</li>
<li>
<p>alternate solutions considered but discarded, if any.</p>
</li>
</ol>
</div>
<div id="present-tense" class="paragraph">
<p>The problem statement that describes the status quo is written in the
present tense.  Write "The code does X when it is given input Y",
instead of "The code used to do Y when given input X".  You do not
have to say "Currently"---the status quo in the problem statement is
about the code <em>without</em> your change, by project convention.</p>
</div>
<div id="imperative-mood" class="paragraph">
<p>Describe your changes in imperative mood, e.g. "make xyzzy do frotz"
instead of "[This patch] makes xyzzy do frotz" or "[I] changed xyzzy
to do frotz", as if you are giving orders to the codebase to change
its behavior.  Try to make sure your explanation can be understood
without external resources. Instead of giving a URL to a mailing list
archive, summarize the relevant points of the discussion.</p>
</div>
<div id="commit-reference" class="paragraph">
<p>There are a few reasons why you may want to refer to another commit in
the "more stable" part of the history (i.e. on branches like <code>maint</code>,
<code>master</code>, and <code>next</code>):</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>A commit that introduced the root cause of a bug you are fixing.</p>
</li>
<li>
<p>A commit that introduced a feature that you are enhancing.</p>
</li>
<li>
<p>A commit that conflicts with your work when you made a trial merge
of your work into <code>next</code> and <code>seen</code> for testing.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>When you reference a commit on a more stable branch (like <code>master</code>,
<code>maint</code> and <code>next</code>), use the format "abbreviated hash (subject,
date)", like this:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>        Commit f86a374 (pack-bitmap.c: fix a memleak, 2015-03-30)
        noticed that ...</pre>
</div>
</div>
<div class="paragraph">
<p>The "Copy commit summary" command of gitk can be used to obtain this
format (with the subject enclosed in a pair of double-quotes), or this
invocation of <code>git show</code>:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>        git show -s --pretty=reference &lt;commit&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>or, on an older version of Git without support for --pretty=reference:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>        git show -s --date=short --pretty='format:%h (%s, %ad)' &lt;commit&gt;</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="sign-off">Certify your work by adding your <code>Signed-off-by</code> trailer</h3>
<div class="paragraph">
<p>To improve tracking of who did what, we ask you to certify that you
wrote the patch or have the right to pass it on under the same license
as ours, by "signing off" your patch.  Without sign-off, we cannot
accept your patches.</p>
</div>
<div class="paragraph">
<p>If (and only if) you certify the below D-C-O:</p>
</div>
<div id="dco" class="quoteblock">
<div class="title">Developer&#8217;s Certificate of Origin 1.1</div>
<blockquote>
<div class="paragraph">
<p>By making a contribution to this project, I certify that:</p>
</div>
<div class="olist loweralpha">
<ol class="loweralpha">
<li>
<p>The contribution was created in whole or in part by me and I
have the right to submit it under the open source license
indicated in the file; or</p>
</li>
<li>
<p>The contribution is based upon previous work that, to the best
of my knowledge, is covered under an appropriate open source
license and I have the right under that license to submit that
work with modifications, whether created in whole or in part
by me, under the same open source license (unless I am
permitted to submit under a different license), as indicated
in the file; or</p>
</li>
<li>
<p>The contribution was provided directly to me by some other
person who certified (a), (b) or (c) and I have not modified
it.</p>
</li>
<li>
<p>I understand and agree that this project and the contribution
are public and that a record of the contribution (including all
personal information I submit with it, including my sign-off) is
maintained indefinitely and may be redistributed consistent with
this project or the open source license(s) involved.</p>
</li>
</ol>
</div>
</blockquote>
</div>
<div class="paragraph">
<p>you add a "Signed-off-by" trailer to your commit, that looks like
this:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>        Signed-off-by: Random J Developer &lt;<EMAIL>&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>This line can be added by Git if you run the git-commit command with
the -s option.</p>
</div>
<div class="paragraph">
<p>Notice that you can place your own <code>Signed-off-by</code> trailer when
forwarding somebody else&#8217;s patch with the above rules for
D-C-O.  Indeed you are encouraged to do so.  Do not forget to
place an in-body "From: " line at the beginning to properly attribute
the change to its true author (see (2) above).</p>
</div>
<div class="paragraph">
<p>This procedure originally came from the Linux kernel project, so our
rule is quite similar to theirs, but what exactly it means to sign-off
your patch differs from project to project, so it may be different
from that of the project you are accustomed to.</p>
</div>
<div id="real-name" class="paragraph">
<p>Also notice that a real name is used in the <code>Signed-off-by</code> trailer. Please
don&#8217;t hide your real name.</p>
</div>
<div id="commit-trailers" class="paragraph">
<p>If you like, you can put extra tags at the end:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><code>Reported-by:</code> is used to credit someone who found the bug that
the patch attempts to fix.</p>
</li>
<li>
<p><code>Acked-by:</code> says that the person who is more familiar with the area
the patch attempts to modify liked the patch.</p>
</li>
<li>
<p><code>Reviewed-by:</code>, unlike the other tags, can only be offered by the
reviewers themselves when they are completely satisfied with the
patch after a detailed analysis.</p>
</li>
<li>
<p><code>Tested-by:</code> is used to indicate that the person applied the patch
and found it to have the desired effect.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>You can also create your own tag or use one that&#8217;s in common usage
such as "Thanks-to:", "Based-on-patch-by:", or "Mentored-by:".</p>
</div>
</div>
<div class="sect2">
<h3 id="git-tools">Generate your patch using Git tools out of your commits.</h3>
<div class="paragraph">
<p>Git based diff tools generate unidiff which is the preferred format.</p>
</div>
<div class="paragraph">
<p>You do not have to be afraid to use <code>-M</code> option to <code>git diff</code> or
<code>git format-patch</code>, if your patch involves file renames.  The
receiving end can handle them just fine.</p>
</div>
<div id="review-patch" class="paragraph">
<p>Please make sure your patch does not add commented out debugging code,
or include any extra files which do not relate to what your patch
is trying to achieve. Make sure to review
your patch after generating it, to ensure accuracy.  Before
sending out, please make sure it cleanly applies to the base you
have chosen in the "Decide what to base your work on" section,
and unless it targets the <code>master</code> branch (which is the default),
mark your patches as such.</p>
</div>
</div>
<div class="sect2">
<h3 id="send-patches">Sending your patches.</h3>
<div class="paragraph">
<p>Before sending any patches, please note that patches that may be
security relevant should be submitted privately to the Git Security
mailing list<sup class="footnote" id="_footnote_security-ml">[<a id="_footnoteref_1" class="footnote" href="#_footnotedef_1" title="View footnote.">1</a>]</sup>, instead of the public mailing list.</p>
</div>
<div class="paragraph">
<p>Learn to use format-patch and send-email if possible.  These commands
are optimized for the workflow of sending patches, avoiding many ways
your existing e-mail client that is optimized for "multipart/*" mime
type e-mails to corrupt and render your patches unusable.</p>
</div>
<div class="paragraph">
<p>People on the Git mailing list need to be able to read and
comment on the changes you are submitting.  It is important for
a developer to be able to "quote" your changes, using standard
e-mail tools, so that they may comment on specific portions of
your code.  For this reason, each patch should be submitted
"inline" in a separate message.</p>
</div>
<div class="paragraph">
<p>Multiple related patches should be grouped into their own e-mail
thread to help readers find all parts of the series.  To that end,
send them as replies to either an additional "cover letter" message
(see below), the first patch, or the respective preceding patch.</p>
</div>
<div class="paragraph">
<p>If your log message (including your name on the
<code>Signed-off-by</code> trailer) is not writable in ASCII, make sure that
you send off a message in the correct encoding.</p>
</div>
<div class="admonitionblock warning">
<table>
<tr>
<td class="icon">
<div class="title">Warning</div>
</td>
<td class="content">
Be wary of your MUAs word-wrap
corrupting your patch.  Do not cut-n-paste your patch; you can
lose tabs that way if you are not careful.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It is a common convention to prefix your subject line with
[PATCH].  This lets people easily distinguish patches from other
e-mail discussions.  Use of markers in addition to PATCH within
the brackets to describe the nature of the patch is also
encouraged.  E.g. [RFC PATCH] (where RFC stands for "request for
comments") is often used to indicate a patch needs further
discussion before being accepted, [PATCH v2], [PATCH v3] etc.
are often seen when you are sending an update to what you have
previously sent.</p>
</div>
<div class="paragraph">
<p>The <code>git format-patch</code> command follows the best current practice to
format the body of an e-mail message.  At the beginning of the
patch should come your commit message, ending with the
<code>Signed-off-by</code> trailers, and a line that consists of three dashes,
followed by the diffstat information and the patch itself.  If
you are forwarding a patch from somebody else, optionally, at
the beginning of the e-mail message just before the commit
message starts, you can put a "From: " line to name that person.
To change the default "[PATCH]" in the subject to "[&lt;text&gt;]", use
<code>git format-patch --subject-prefix=&lt;text&gt;</code>.  As a shortcut, you
can use <code>--rfc</code> instead of <code>--subject-prefix="RFC PATCH"</code>, or
<code>-v &lt;n&gt;</code> instead of <code>--subject-prefix="PATCH v&lt;n&gt;"</code>.</p>
</div>
<div class="paragraph">
<p>You often want to add additional explanation about the patch,
other than the commit message itself.  Place such "cover letter"
material between the three-dash line and the diffstat.  For
patches requiring multiple iterations of review and discussion,
an explanation of changes between each iteration can be kept in
Git-notes and inserted automatically following the three-dash
line via <code>git format-patch --notes</code>.</p>
</div>
<div id="attachment" class="paragraph">
<p>Do not attach the patch as a MIME attachment, compressed or not.
Do not let your e-mail client send quoted-printable.  Do not let
your e-mail client send format=flowed which would destroy
whitespaces in your patches. Many
popular e-mail applications will not always transmit a MIME
attachment as plain text, making it impossible to comment on
your code.  A MIME attachment also takes a bit more time to
process.  This does not decrease the likelihood of your
MIME-attached change being accepted, but it makes it more likely
that it will be postponed.</p>
</div>
<div class="paragraph">
<p>Exception:  If your mailer is mangling patches then someone may ask
you to re-send them using MIME, that is OK.</p>
</div>
<div id="pgp-signature" class="paragraph">
<p>Do not PGP sign your patch. Most likely, your maintainer or other people on the
list would not have your PGP key and would not bother obtaining it anyway.
Your patch is not judged by who you are; a good patch from an unknown origin
has a far better chance of being accepted than a patch from a known, respected
origin that is done poorly or does incorrect things.</p>
</div>
<div class="paragraph">
<p>If you really really really really want to do a PGP signed
patch, format it as "multipart/signed", not a text/plain message
that starts with <code>-----BEGIN PGP SIGNED MESSAGE-----</code>.  That is
not a text/plain, it&#8217;s something else.</p>
</div>
<div class="paragraph">
<p>As mentioned at the beginning of the section, patches that may be
security relevant should not be submitted to the public mailing list
mentioned below, but should instead be sent privately to the Git
Security mailing list<sup class="footnoteref">[<a class="footnote" href="#_footnotedef_1" title="View footnote.">1</a>]</sup>.</p>
</div>
<div class="paragraph">
<p>Send your patch with "To:" set to the mailing list, with "cc:" listing
people who are involved in the area you are touching (the <code>git
contacts</code> command in <code>contrib/contacts/</code> can help to
identify them), to solicit comments and reviews.  Also, when you made
trial merges of your topic to <code>next</code> and <code>seen</code>, you may have noticed
work by others conflicting with your changes.  There is a good possibility
that these people may know the area you are touching well.</p>
</div>
<div class="paragraph">
<p>After the list reached a consensus that it is a good idea to apply the
patch, re-send it with "To:" set to the maintainer<sup class="footnote">[<a id="_footnoteref_2" class="footnote" href="#_footnotedef_2" title="View footnote.">2</a>]</sup>
and "cc:" the list<sup class="footnote">[<a id="_footnoteref_3" class="footnote" href="#_footnotedef_3" title="View footnote.">3</a>]</sup> for inclusion.  This is especially relevant
when the maintainer did not heavily participate in the discussion and
instead left the review to trusted others.</p>
</div>
<div class="paragraph">
<p>Do not forget to add trailers such as <code>Acked-by:</code>, <code>Reviewed-by:</code> and
<code>Tested-by:</code> lines as necessary to credit people who helped your
patch, and "cc:" them when sending such a final version for inclusion.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_subsystems_with_dedicated_maintainers">Subsystems with dedicated maintainers</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Some parts of the system have dedicated maintainers with their own
repositories.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>git-gui/</code> comes from git-gui project, maintained by Pratyush Yadav:</p>
<div class="literalblock">
<div class="content">
<pre>https://github.com/prati0100/git-gui.git</pre>
</div>
</div>
</li>
<li>
<p><code>gitk-git/</code> comes from Paul Mackerras&#8217;s gitk project:</p>
<div class="literalblock">
<div class="content">
<pre>git://ozlabs.org/~paulus/gitk</pre>
</div>
</div>
</li>
<li>
<p><code>po/</code> comes from the localization coordinator, Jiang Xin:</p>
<div class="literalblock">
<div class="content">
<pre>https://github.com/git-l10n/git-po/</pre>
</div>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>Patches to these parts should be based on their trees.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="patch-flow">An ideal patch flow</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Here is an ideal patch flow for this project the current maintainer
suggests to the contributors:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>You come up with an itch.  You code it up.</p>
</li>
<li>
<p>Send it to the list and cc people who may need to know about
the change.</p>
<div class="paragraph">
<p>The people who may need to know are the ones whose code you
are butchering.  These people happen to be the ones who are
most likely to be knowledgeable enough to help you, but
they have no obligation to help you (i.e. you ask for help,
don&#8217;t demand).  <code>git log -p &#x2d;&#x2d; <em>$area_you_are_modifying</em></code> would
help you find out who they are.</p>
</div>
</li>
<li>
<p>You get comments and suggestions for improvements.  You may
even get them in an "on top of your change" patch form.</p>
</li>
<li>
<p>Polish, refine, and re-send to the list and the people who
spend their time to improve your patch.  Go back to step (2).</p>
</li>
<li>
<p>The list forms consensus that the last round of your patch is
good.  Send it to the maintainer and cc the list.</p>
</li>
<li>
<p>A topic branch is created with the patch and is merged to <code>next</code>,
and cooked further and eventually graduates to <code>master</code>.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>In any time between the (2)-(3) cycle, the maintainer may pick it up
from the list and queue it to <code>seen</code>, in order to make it easier for
people play with it without having to pick up and apply the patch to
their trees themselves.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="patch-status">Know the status of your patch after submission</h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>You can use Git itself to find out when your patch is merged in
master. <code>git pull --rebase</code> will automatically skip already-applied
patches, and will let you know. This works only if you rebase on top
of the branch in which your patch has been merged (i.e. it will not
tell you if your patch is merged in <code>seen</code> if you rebase on top of
master).</p>
</li>
<li>
<p>Read the Git mailing list, the maintainer regularly posts messages
entitled "What&#8217;s cooking in git.git" and "What&#8217;s in git.git" giving
the status of various proposed changes.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_github_ci">GitHub CI<a id="GHCI"></a></h2>
<div class="sectionbody">
<div class="paragraph">
<p>With an account at GitHub, you can use GitHub CI to test your changes
on Linux, Mac and Windows. See
<a href="https://github.com/git/git/actions/workflows/main.yml" class="bare">https://github.com/git/git/actions/workflows/main.yml</a> for examples of
recent CI runs.</p>
</div>
<div class="paragraph">
<p>Follow these steps for the initial setup:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Fork <a href="https://github.com/git/git" class="bare">https://github.com/git/git</a> to your GitHub account.
You can find detailed instructions how to fork here:
<a href="https://help.github.com/articles/fork-a-repo/" class="bare">https://help.github.com/articles/fork-a-repo/</a></p>
</li>
</ol>
</div>
<div class="paragraph">
<p>After the initial setup, CI will run whenever you push new changes
to your fork of Git on GitHub.  You can monitor the test state of all your
branches here: <code>https://github.com/&lt;Your GitHub handle&gt;/git/actions/workflows/main.yml</code></p>
</div>
<div class="paragraph">
<p>If a branch did not pass all test cases then it is marked with a red
cross. In that case you can click on the failing job and navigate to
"ci/run-build-and-tests.sh" and/or "ci/print-test-failures.sh". You
can also download "Artifacts" which are tarred (or zipped) archives
with test data relevant for debugging.</p>
</div>
<div class="paragraph">
<p>Then fix the problem and push your fix to your GitHub fork. This will
trigger a new CI build to ensure all tests pass.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="mua">MUA specific hints</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Some of patches I receive or pick up from the list share common
patterns of breakage.  Please make sure your MUA is set up
properly not to corrupt whitespaces.</p>
</div>
<div class="paragraph">
<p>See the DISCUSSION section of <a href="git-format-patch.html">git-format-patch(1)</a> for hints on
checking your patch by mailing it to yourself and applying with
<a href="git-am.html">git-am(1)</a>.</p>
</div>
<div class="paragraph">
<p>While you are at it, check the resulting commit log message from
a trial run of applying the patch.  If what is in the resulting
commit is not exactly what you would want to see, it is very
likely that your maintainer would end up hand editing the log
message when he applies your patch.  Things like "Hi, this is my
first patch.\n", if you really want to put in the patch e-mail,
should come after the three-dash line that signals the end of the
commit message.</p>
</div>
<div class="sect2">
<h3 id="_pine">Pine</h3>
<div class="paragraph">
<p>(Johannes Schindelin)</p>
</div>
<div class="literalblock">
<div class="content">
<pre>I don't know how many people still use pine, but for those poor
souls it may be good to mention that the quell-flowed-text is
needed for recent versions.

... the "no-strip-whitespace-before-send" option, too. AFAIK it
was introduced in 4.60.</pre>
</div>
</div>
<div class="paragraph">
<p>(Linus Torvalds)</p>
</div>
<div class="literalblock">
<div class="content">
<pre>And 4.58 needs at least this.

diff-tree 8326dd8350be64ac7fc805f6563a1d61ad10d32c (from e886a61f76edf5410573e92e38ce22974f9c40f1)
Author: Linus Torvalds &lt;<EMAIL>&gt;
Date:   Mon Aug 15 17:23:51 2005 -0700

    Fix pine whitespace-corruption bug

    There's no excuse for unconditionally removing whitespace from
    the pico buffers on close.

diff --git a/pico/pico.c b/pico/pico.c
--- a/pico/pico.c
+++ b/pico/pico.c
@@ -219,7 +219,9 @@ PICO *pm;
            switch(pico_all_done){      /* prepare for/handle final events */
              case COMP_EXIT :          /* already confirmed */
                packheader();
+#if 0
                stripwhitespace();
+#endif
                c |= COMP_EXIT;
                break;</pre>
</div>
</div>
<div class="paragraph">
<p>(Daniel Barkalow)</p>
</div>
<div class="literalblock">
<div class="content">
<pre>&gt; A patch to SubmittingPatches, MUA specific help section for
&gt; users of Pine 4.63 would be very much appreciated.

Ah, it looks like a recent version changed the default behavior to do the
right thing, and inverted the sense of the configuration option. (Either
that or Gentoo did it.) So you need to set the
"no-strip-whitespace-before-send" option, unless the option you have is
"strip-whitespace-before-send", in which case you should avoid checking
it.</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_thunderbird_kmail_gmail">Thunderbird, KMail, GMail</h3>
<div class="paragraph">
<p>See the MUA-SPECIFIC HINTS section of <a href="git-format-patch.html">git-format-patch(1)</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_gnus">Gnus</h3>
<div class="paragraph">
<p>"|" in the <code>*Summary*</code> buffer can be used to pipe the current
message to an external program, and this is a handy way to drive
<code>git am</code>.  However, if the message is MIME encoded, what is
piped into the program is the representation you see in your
<code>*Article*</code> buffer after unwrapping MIME.  This is often not what
you would want for two reasons.  It tends to screw up non ASCII
characters (most notably in people&#8217;s names), and also
whitespaces (fatal in patches).  Running "C-u g" to display the
message in raw form before using "|" to run the pipe can work
this problem around.</p>
</div>
</div>
</div>
</div>
</div>
<div id="footnotes">
<hr/>
<div class="footnote" id="_footnotedef_1">
<a href="#_footnoteref_1">1</a>. The Git Security mailing list: <a href="mailto:<EMAIL>"><EMAIL></a>
</div>
<div class="footnote" id="_footnotedef_2">
<a href="#_footnoteref_2">2</a>. The current maintainer: <a href="mailto:<EMAIL>"><EMAIL></a>
</div>
<div class="footnote" id="_footnotedef_3">
<a href="#_footnoteref_3">3</a>. The mailing list: <a href="mailto:*******************">*******************</a>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>