#!/usr/bin/env ptuyhon
# -*- coding: utf-8 -*-

from lsr.fbxsdk.FBX_Scene import FBX_Class
from lsr.fbxsdk import fbx


class FBX_SDK_Extend(FBX_Class):
    """
    Extend fbx remove frame
    """

    def __init__(self, filename):
        """
        FBX Scene Object
        """
        super(FBX_SDK_Extend, self).__init__(filename)
    def get_anim_stace_count(self):
        """
        get anim stack count
        Returns: anim stack count

        """
        return self.scene.GetSrcObjectCount(fbx.FbxCriteria.ObjectType(fbx.FbxAnimStack.ClassId))
    def get_anim_GetCurve(self, node, curve_type=1, curves_name='X'):
        """

        Args:
            node: FbxNode
            curve_type: default value is [1,2,3]  value 1 return the dataType is LclTranslation,
                        value 2 return the dataType is LclRotation,
                        value 3 return the dataType is LclScaling
            curves_name: default value is "X"

        Returns:

        """

        KFCURVENODE_T_X = curves_name
        lAnimCurve = None

        for i in range(self.get_anim_stace_count()):
            lAnimStack = self.scene.GetSrcObject(fbx.FbxCriteria.ObjectType(fbx.FbxAnimStack.ClassId), i)
            nbAnimLayers = lAnimStack.GetSrcObjectCount(fbx.FbxCriteria.ObjectType(fbx.FbxAnimLayer.ClassId))
            # Loop AnimLayers
            # for ii in range(nbAnimLayers):
            #     lAnimLayer = lAnimStack.GetSrcObject(FbxCriteria.ObjectType(FbxAnimLayer.ClassId), ii)

            lAnimLayer = lAnimStack.GetSrcObject(fbx.FbxCriteria.ObjectType(fbx.FbxAnimLayer.ClassId), 0)
            if curve_type == 1:
                lAnimCurve = node.LclTranslation.GetCurve(lAnimLayer, KFCURVENODE_T_X)

            if curve_type == 2:
                lAnimCurve = node.LclRotation.GetCurve(lAnimLayer, KFCURVENODE_T_X)

            if curve_type == 3:
                lAnimCurve = node.LclScaling.GetCurve(lAnimLayer, KFCURVENODE_T_X)
        return lAnimCurve

    def delete_keyframe(self, lAnimCurve, frame=0, eMode=fbx.FbxTime.eFrames60):
        """
        delete_keyframe
        Args:
            lAnimCurve: FbxAnimCurve
            frame: the delete frame
            eMode: FbxTime.eMode, default value is FbxTime.eFrames60.

        Returns:Frame value

        """
        if lAnimCurve:
            keyCount = lAnimCurve.KeyGetCount()
            for lCount in range(keyCount):
                lkeyVale = lAnimCurve.KeyGetValue(lCount)
                lKeyTime = lAnimCurve.KeyGetTime(lCount)
                Frame = lKeyTime.GetFrameRate(eMode) * lKeyTime.GetSecondDouble()
                if Frame == float(frame):
                    lAnimCurve.KeyRemove(lCount)
                    break

    def __batch_delete_keyframe(self, node, frame, curve_type=[1, 2, 3], attribute=["X", "Y", "Z"],
                                eMode=fbx.FbxTime.eFrames60):
        """
        __batch_delete_keyframe
        Args:
            node: FbxNode
            frame: frame
            curve_type: default value is [1,2,3]  value 1 return the dataType is LclTranslation,
                        value 2 return the dataType is LclRotation,
                        value 3 return the dataType is LclScaling
            attribute: default value is ["X", "Y", "Z"]
            eMode: Type FbxTime.eMode

        Returns:

        """
        for i in curve_type:
            for x in attribute:
                curveNode = self.get_anim_GetCurve(node, i, x)
                self.delete_keyframe(curveNode, frame, eMode)

    def __get_all_children(self, node, dataList=[]):
        """
        get all children
        Args:
            node: FbxNode
            dataList: type List

        Returns:dataList

        """
        dataList.append(node)
        for i in range(node.GetChildCount()):
            self.__get_all_children(node.GetChild(i), dataList)
        return dataList

    def batch_delete_keyframe(self, node, frame, curve_type=[1, 2, 3], attribute=["X", "Y", "Z"], ifChildren=True,
                              eMode=fbx.FbxTime.eFrames60):
        """
        batch delete keyframe
        Args:
            node: FbxNode
            frame: time frame
            curve_type: default value is [1,2,3]  value 1 return the dataType is LclTranslation,
                        value 2 return the dataType is LclRotation,
                        value 3 return the dataType is LclScaling
            attribute: default value is ["X", "Y", "Z"]
            ifChildren: if children delete frame key with all children
            eMode: Type FbxTime.eMode

        Returns:

        """
        children_list = []
        if ifChildren:
            self.__get_all_children(node, children_list)
        if children_list:
            for children in children_list:
                self.__batch_delete_keyframe(children, frame, curve_type, attribute, eMode)
            return 0
        else:
            self.__batch_delete_keyframe(node, frame, curve_type, attribute, eMode)
            return 0

    def set_startTime_to_zero(self):
        """
        Set startTime to 0
        Returns:

        """
        anim_stack = self.scene.GetSrcObject(fbx.FbxCriteria.ObjectType(fbx.FbxAnimStack.ClassId))
        time_span = anim_stack.GetLocalTimeSpan()
        time_span.SetStart(fbx.FbxTime(0))

        global_settings = self.scene.GetGlobalSettings()
        time_span = global_settings.GetTimelineDefaultTimeSpan()
        time_span.SetStart(fbx.FbxTime(0))
        global_settings.SetTimelineDefaultTimeSpan(time_span)

        time_span_start = global_settings.GetTimelineDefaultTimeSpan().GetStart()
        print("Set global_settings start time is {}".format(time_span_start.GetSecondDouble()))
        for i in range(self.get_anim_stace_count()):
            anim_stack = self.scene.GetSrcObject(fbx.FbxCriteria.ObjectType(fbx.FbxAnimStack.ClassId), i)
            time_span = anim_stack.GetLocalTimeSpan()
            time_span.SetStart(fbx.FbxTime(0))
            anim_stack.SetLocalTimeSpan(time_span)
            start_time = time_span.GetStart()
            print("Set {} anim_stack start time is {}".format(anim_stack.GetName(), start_time.GetSecondDouble()))

