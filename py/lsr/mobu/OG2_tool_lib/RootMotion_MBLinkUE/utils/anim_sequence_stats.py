#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Unreal Engine AnimSequence统计工具
统计选中资产中被引用的AnimSequence数量和特定字段的资产数量
"""

import unreal


class AnimSequenceStatsCollector:
    """AnimSequence统计收集器"""
    
    def __init__(self):
        self.asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        self.editor_utility = unreal.EditorUtilityLibrary()
        
        # 定义要统计的字段
        self.target_keywords = [
            'P_dflt',
            'LQCUSTOM_',
            'A_Vehicles',
            'NPC_Attack',
            'Meleecombat',
            'A_N'
        ]
    
    def is_asset_referenced(self, asset_path):
        """检查资产是否被引用"""
        try:
            asset_data = self.asset_registry.get_asset_by_object_path(asset_path)
            if not asset_data:
                return False
            
            referencers = self.asset_registry.get_referencers(
                asset_data.package_name,
                unreal.AssetRegistryDependencyOptions()
            )
            
            return len(referencers) > 0
        except:
            return False
    
    def is_anim_sequence(self, asset):
        """检查资产是否为AnimSequence"""
        try:
            return isinstance(asset, unreal.AnimSequence)
        except:
            return False
    
    def contains_keyword(self, asset_name, keyword):
        """检查资产名称是否包含指定关键字"""
        return keyword.lower() in asset_name.lower()
    
    def analyze_selected_assets(self):
        """分析当前选中的资产"""
        try:
            # 获取选中的资产
            selected_assets = self.editor_utility.get_selected_assets()
            
            if not selected_assets:
                print("没有选中任何资产")
                return None
            
            # 初始化统计数据
            stats = {
                'total_assets': len(selected_assets),
                'total_anim_sequences': 0,
                'referenced_anim_sequences': 0,
                'unreferenced_anim_sequences': 0,
                'keyword_stats': {keyword: 0 for keyword in self.target_keywords},
                'keyword_details': {keyword: [] for keyword in self.target_keywords},
                'referenced_details': [],
                'unreferenced_details': []
            }
            
            print(f"开始分析 {len(selected_assets)} 个选中的资产...")
            
            # 遍历所有选中的资产
            for i, asset in enumerate(selected_assets):
                asset_name = asset.get_name()
                asset_path = asset.get_path_name()
                
                print(f"处理资产 {i+1}/{len(selected_assets)}: {asset_name}")
                
                # 检查是否为AnimSequence
                if self.is_anim_sequence(asset):
                    stats['total_anim_sequences'] += 1
                    
                    # 检查是否被引用
                    is_referenced = self.is_asset_referenced(asset_path)
                    
                    if is_referenced:
                        stats['referenced_anim_sequences'] += 1
                        stats['referenced_details'].append({
                            'name': asset_name,
                            'path': asset_path
                        })
                    else:
                        stats['unreferenced_anim_sequences'] += 1
                        stats['unreferenced_details'].append({
                            'name': asset_name,
                            'path': asset_path
                        })
                
                # 检查关键字
                for keyword in self.target_keywords:
                    if self.contains_keyword(asset_name, keyword):
                        stats['keyword_stats'][keyword] += 1
                        stats['keyword_details'][keyword].append({
                            'name': asset_name,
                            'path': asset_path,
                            'is_anim_sequence': self.is_anim_sequence(asset),
                            'is_referenced': self.is_asset_referenced(asset_path) if self.is_anim_sequence(asset) else None
                        })
            
            return stats
            
        except Exception as e:
            print(f"分析资产时出错: {e}")
            return None
    
    def print_statistics(self, stats):
        """打印统计结果"""
        if not stats:
            return
        
        print("\n" + "="*60)
        print("资产统计结果")
        print("="*60)
        
        # 基本统计
        print(f"总资产数量: {stats['total_assets']}")
        print(f"AnimSequence总数: {stats['total_anim_sequences']}")
        print(f"被引用的AnimSequence: {stats['referenced_anim_sequences']}")
        print(f"未被引用的AnimSequence: {stats['unreferenced_anim_sequences']}")
        
        if stats['total_anim_sequences'] > 0:
            ref_percentage = (stats['referenced_anim_sequences'] / stats['total_anim_sequences']) * 100
            print(f"引用率: {ref_percentage:.1f}%")
        
        # 关键字统计
        print("\n" + "-"*40)
        print("关键字统计:")
        print("-"*40)
        
        for keyword, count in stats['keyword_stats'].items():
            print(f"包含 '{keyword}' 的资产: {count}")
            
            # 显示详细信息（前5个）
            if count > 0:
                details = stats['keyword_details'][keyword]
                print(f"  详细列表 (显示前5个):")
                for i, detail in enumerate(details[:5]):
                    ref_status = ""
                    if detail['is_anim_sequence']:
                        ref_status = " [被引用]" if detail['is_referenced'] else " [未引用]"
                    print(f"    {i+1}. {detail['name']}{ref_status}")
                
                if count > 5:
                    print(f"    ... 还有 {count - 5} 个资产")
                print()
    
    def export_detailed_report(self, stats, output_path=None):
        """导出详细报告到文件"""
        if not stats:
            return
        
        if not output_path:
            import os
            output_path = os.path.join(os.path.expanduser("~"), "anim_sequence_stats.txt")
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("AnimSequence统计详细报告\n")
                f.write("="*60 + "\n\n")
                
                # 基本统计
                f.write(f"总资产数量: {stats['total_assets']}\n")
                f.write(f"AnimSequence总数: {stats['total_anim_sequences']}\n")
                f.write(f"被引用的AnimSequence: {stats['referenced_anim_sequences']}\n")
                f.write(f"未被引用的AnimSequence: {stats['unreferenced_anim_sequences']}\n\n")
                
                # 被引用的AnimSequence详细列表
                f.write("被引用的AnimSequence详细列表:\n")
                f.write("-"*40 + "\n")
                for detail in stats['referenced_details']:
                    f.write(f"{detail['name']} - {detail['path']}\n")
                
                f.write("\n未被引用的AnimSequence详细列表:\n")
                f.write("-"*40 + "\n")
                for detail in stats['unreferenced_details']:
                    f.write(f"{detail['name']} - {detail['path']}\n")
                
                # 关键字详细统计
                f.write("\n关键字详细统计:\n")
                f.write("-"*40 + "\n")
                for keyword, details in stats['keyword_details'].items():
                    f.write(f"\n包含 '{keyword}' 的资产 ({len(details)} 个):\n")
                    for detail in details:
                        ref_status = ""
                        if detail['is_anim_sequence']:
                            ref_status = " [被引用]" if detail['is_referenced'] else " [未引用]"
                        f.write(f"  {detail['name']}{ref_status} - {detail['path']}\n")
            
            print(f"\n详细报告已导出到: {output_path}")
            
        except Exception as e:
            print(f"导出报告时出错: {e}")


def run_anim_sequence_analysis():
    """运行AnimSequence分析的主函数"""
    collector = AnimSequenceStatsCollector()
    
    print("开始分析选中的资产...")
    stats = collector.analyze_selected_assets()
    
    if stats:
        collector.print_statistics(stats)
        
        # 询问是否导出详细报告
        try:
            export = input("\n是否导出详细报告到文件? (y/n): ").lower().strip()
            if export in ['y', 'yes', '是']:
                collector.export_detailed_report(stats)
        except:
            # 在Unreal编辑器中运行时可能无法使用input
            collector.export_detailed_report(stats)


# 简化的快速统计函数
def quick_stats():
    """快速统计函数"""
    collector = AnimSequenceStatsCollector()
    stats = collector.analyze_selected_assets()
    
    if stats:
        print(f"\n快速统计结果:")
        print(f"总资产: {stats['total_assets']}")
        print(f"AnimSequence: {stats['total_anim_sequences']} (被引用: {stats['referenced_anim_sequences']}, 未引用: {stats['unreferenced_anim_sequences']})")
        
        for keyword, count in stats['keyword_stats'].items():
            if count > 0:
                print(f"包含 '{keyword}': {count}")



run_anim_sequence_analysis()
