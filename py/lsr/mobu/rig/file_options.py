"""File Options"""

import os

import pyfbsdk as fb

__all__ = ['SaveOption', 'RetargetAnimationOption', 'PlotRetargetOption',
           'CharPlotToSkeletonOption', 'CharPlotToRigOption', 'LoadOption',
           'BakeOption', 'SaveAllOption','PoseOption']


class SaveOption(fb.FBFbxOptions):
    """FBK Save Selected Option"""

    def __init__(self, p_object, *args, **kwargs):
        super(SaveOption, self).__init__(p_object, *args, **kwargs)
        self.custom_setting()

    def custom_setting(self):
        """custom setting"""
        self.SaveCharacter = True
        self.SaveCharacterExtensions = True
        self.UseASCIIFormat = False
        self.FileFormatAndVersion = fb.FBFileFormatAndVersion.kFBFBX2019
        self.SaveSelectedModelsOnly = True
        self.BaseCameras = False
        self.CurrentCameraSettings = False
        self.GlobalLightingSettings = False
        self.TransportSettings = False


class SaveAllOption(fb.FBFbxOptions):
    """FBK Save All Option"""
    def __init__(self, p_object, *args, **kwargs):
        super(SaveAllOption, self).__init__(p_object, *args, **kwargs)
        self.custom_setting()

    def custom_setting(self):
        self.SaveCharacter = True
        self.SaveCharacterExtensions = True
        self.UseASCIIFormat = False
        self.FileFormatAndVersion = fb.FBFileFormatAndVersion.kFBFBX2019
        self.SaveSelectedModelsOnly = False
        self.BaseCameras = False
        self.CurrentCameraSettings = False
        self.GlobalLightingSettings = False
        self.TransportSettings = True


class LoadOption(fb.FBFbxOptions):

    def __init__(self, p_object, *args, **kwargs):
        super(LoadOption, self).__init__(p_object, *args, **kwargs)
        self.custom_setting()

    def custom_setting(self):
        self.SetAll(fb.FBElementAction.kFBElementActionMerge, True)
        for takeIndex in range(0, self.GetTakeCount()):
            self.SetTakeSelect(takeIndex, True)


class RetargetAnimationOption(fb.FBFbxOptions):
    """FBK Load Option"""

    def __init__(self, p_object, *args, **kwargs):
        super(RetargetAnimationOption, self).__init__(p_object, *args, **kwargs)
        self.custom_setting()

    def custom_setting(self):
        """custom setting"""
        self.TransferMethod = fb.FBCharacterLoadAnimationMethod.kFBCharacterLoadPlot


class PlotRetargetOption(fb.FBPlotOptions):
    """FBK Plot Option"""

    def __init__(self, *args, **kwargs):
        super(PlotRetargetOption, self).__init__(*args, **kwargs)
        self.custom_setting()

    def custom_setting(self):
        """custom setting"""
        self.ConstantKeyReducerKeepOneKey = False
        self.PlotAllTakes = False
        self.PlotOnFrame = True
        self.PlotPeriod = fb.FBTime(0, 0, 0, 1)
        self.PlotTranslationOnRootOnly = False
        self.PreciseTimeDiscontinuities = False
        self.RotationFilterToApply = fb.FBRotationFilter.kFBRotationFilterUnroll
        self.UseConstantKeyReducer = False
        self.PlotAuxEffectors = True


class CharPlotToSkeletonOption(fb.FBPlotOptions):
    """Character Plot To Skeleton Option"""

    def __init__(self, is_plot_all, *args, **kwargs):
        super(CharPlotToSkeletonOption, self).__init__(*args, **kwargs)
        self.is_plot_all = is_plot_all
        self.custom_setting()

    def custom_setting(self):
        """custom setting"""
        self.ConstantKeyReducerKeepOneKey = False
        self.PlotAllTakes = self.is_plot_all
        self.PlotOnFrame = True
        self.PlotPeriod = fb.FBTime(0, 0, 0, 1)
        self.PlotTranslationOnRootOnly = False
        self.PreciseTimeDiscontinuities = False
        self.RotationFilterToApply = fb.FBRotationFilter.kFBRotationFilterUnroll
        self.UseConstantKeyReducer = False
        self.PlotAuxEffectors = True


class CharPlotToRigOption(fb.FBPlotOptions):
    """Character Plot To Control Rig Option"""

    def __init__(self, is_plot_all, *args, **kwargs):
        super(CharPlotToRigOption, self).__init__(*args, **kwargs)
        self.is_plot_all = is_plot_all
        self.custom_setting()

    def custom_setting(self):
        """custom setting"""
        self.ConstantKeyReducerKeepOneKey = False
        self.PlotAllTakes = self.is_plot_all
        self.PlotOnFrame = True
        self.PlotPeriod = fb.FBTime(0, 0, 0, 1)
        self.PlotTranslationOnRootOnly = False
        self.PreciseTimeDiscontinuities = False
        self.RotationFilterToApply = fb.FBRotationFilter.kFBRotationFilterUnroll
        self.UseConstantKeyReducer = False


class BakeOption(fb.FBPlotOptions):
    """Bake animation option class"""
    def __init__(self):
        super(BakeOption, self).__init__()
        self.custom_setting()

    def custom_setting(self):
        self.PlotAllTakes = False
        self.PlotOnFrame = True
        self.PlotPeriod = fb.FBTime(0, 0, 0, 1)
        self.PlotLockedProperties = True
        self.UseConstantKeyReducer = False
        self.ConstantKeyReducerKeepOneKey = False



class PoseOption(fb.FBCharacterPoseOptions):
    """
    Pose option class
    """
    def __init__(self):
        super(PoseOption, self).__init__()
        self.custom_setting()

    def custom_setting(self):
        pass
