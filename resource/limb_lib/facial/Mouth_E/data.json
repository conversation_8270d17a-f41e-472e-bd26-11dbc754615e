{"MOUTH_M_Limb": {"name": "MOUTH_M_Limb", "type": "LSRLimbTransform", "attributes": [{"value": true, "name": "enabled"}, {"value": "mouth", "name": "part"}, {"value": false, "name": "mirror"}, {"value": "['MOUTH_JAW_00_M_RIGJNT', 'MOUTH_JAW_01_M_RIGJNT']", "name": "input_skeleton"}, {"value": 0, "name": "side"}, {"value": "['Root:root_M_CTRL', 'Master:Root_M_RIGJNT']", "name": "follow_refs"}, {"value": "['PLC', 'SDK', 'OFFSET', 'FOLLOWA', 'FOLLOWB']", "name": "group_exts"}, {"value": "lsr:Mouth_E_Limb", "name": "limb_type"}, {"value": "single_chain", "name": "skeleton_type"}], "creation": {"name": "MOUTH_M_Limb", "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "uuid": "E572AD8D-4B51-92CE-FCA4-3FB44DB42F08", "limb_type": "lsr:Mouth_E_Limb", "source_joints": ["10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "991279C0-467C-9182-CFF7-2D8E9C14FB90", "F146EC71-4AAA-EFBC-8369-5EB84A30E0EC", "76745A2A-43DB-90C0-869A-3FB72EF5A2ED", "992491EF-4E20-06AE-6101-BD9375F06269", "2AF17427-4A72-1B8A-9DB4-75A815E506EC"], "limb_data": {"enabled": true, "part": "mouth", "mirror": false, "input_skeleton": "['MOUTH_JAW_00_M_RIGJNT', 'MOUTH_JAW_01_M_RIGJNT']", "side": 0, "follow_refs": "['Root:root_M_CTRL', 'Master:Root_M_RIGJNT']", "group_exts": "['PLC', 'SDK', 'OFFSET', 'FOLLOWA', 'FOLLOWB']", "skeleton_type": "single_chain"}, "input_joint_data": {"start_joint": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "end_joint": "2AF17427-4A72-1B8A-9DB4-75A815E506EC"}}, "shape_data": {"localPositionX": 0.0, "localPositionY": 0.0, "localPositionZ": 0.0, "localScaleX": 5.0, "localScaleY": 5.0, "localScaleZ": 5.0, "localRotateX": 90.0, "localRotateY": 0.0, "localRotateZ": 0.0, "colorR": 1.0, "colorG": 1.0, "colorB": 0.0, "textPositionX": 0.0, "textPositionY": 0.0, "textPositionZ": 0.0, "label": "", "lsRig": "", "shapeType": 31, "controllerType": 0, "xrayMode": false, "drawIt": true}}, "MOUTH_JAW_00_M_RIGJNT": {"name": "MOUTH_JAW_00_M_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": -0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "<PERSON><PERSON>", "name": "otherType"}, {"value": 17, "name": "overrideColor"}], "creation": {"name": "MOUTH_JAW_00_M_RIGJNT", "parent": "MOUTH_M_Limb", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "overrideEnabled": true, "uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "parent_uuid": "E572AD8D-4B51-92CE-FCA4-3FB44DB42F08", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}, "MOUTH_LowLip_01_L_RIGJNT": {"name": "MOUTH_LowLip_01_L_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": -0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "Left_LowLip", "name": "otherType"}, {"value": 18, "name": "overrideColor"}], "creation": {"name": "MOUTH_LowLip_01_L_RIGJNT", "parent": "MOUTH_JAW_00_M_RIGJNT", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.7, -1.3, 2.0, 1.0], "overrideEnabled": true, "uuid": "992491EF-4E20-06AE-6101-BD9375F06269", "parent_uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}, "MOUTH_LowLip_00_M_RIGJNT": {"name": "MOUTH_LowLip_00_M_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": -0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "LowLip", "name": "otherType"}, {"value": 18, "name": "overrideColor"}], "creation": {"name": "MOUTH_LowLip_00_M_RIGJNT", "parent": "MOUTH_JAW_00_M_RIGJNT", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, -2.0, 2.0, 1.0], "overrideEnabled": true, "uuid": "76745A2A-43DB-90C0-869A-3FB72EF5A2ED", "parent_uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}, "MOUTH_UpLip_01_L_RIGJNT": {"name": "MOUTH_UpLip_01_L_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": -0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "Left_UpLip", "name": "otherType"}, {"value": 18, "name": "overrideColor"}], "creation": {"name": "MOUTH_UpLip_01_L_RIGJNT", "parent": "MOUTH_JAW_00_M_RIGJNT", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.7, 1.3, 2.0, 1.0], "overrideEnabled": true, "uuid": "F146EC71-4AAA-EFBC-8369-5EB84A30E0EC", "parent_uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}, "MOUTH_UpLip_00_M_RIGJNT": {"name": "MOUTH_UpLip_00_M_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": -0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "UpLip", "name": "otherType"}, {"value": 18, "name": "overrideColor"}], "creation": {"name": "MOUTH_UpLip_00_M_RIGJNT", "parent": "MOUTH_JAW_00_M_RIGJNT", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 2.0, 2.0, 1.0], "overrideEnabled": true, "uuid": "991279C0-467C-9182-CFF7-2D8E9C14FB90", "parent_uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}, "MOUTH_JAW_01_M_RIGJNT": {"name": "MOUTH_JAW_01_M_RIGJNT", "type": "joint", "attributes": [{"children": [{"value": 0.0, "name": "jointOrientX"}, {"value": 0.0, "name": "jointOrientY"}, {"value": 0.0, "name": "jointOrientZ"}], "name": "jointOrient"}, {"value": 1.0, "name": "radius"}, {"value": 0, "name": "drawStyle"}, {"value": true, "name": "drawLabel"}, {"value": 0, "name": "side"}, {"value": 18, "name": "type"}, {"value": "Jaw_End", "name": "otherType"}, {"value": 17, "name": "overrideColor"}], "creation": {"name": "MOUTH_JAW_01_M_RIGJNT", "parent": "MOUTH_JAW_00_M_RIGJNT", "parentMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0], "worldMatrix": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 5.0, 1.0], "overrideEnabled": true, "uuid": "2AF17427-4A72-1B8A-9DB4-75A815E506EC", "parent_uuid": "10521419-4FA8-7B77-2ED2-8EBF1C84CC69", "attrs_locked": {"tx": false, "ty": false, "tz": false, "rx": false, "ry": false, "rz": false, "sx": false, "sy": false, "sz": false}}}}