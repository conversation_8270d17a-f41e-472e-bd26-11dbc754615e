# -*- coding: utf-8 -*-

""" a dictionary of animLib's string in UI"""

translate_language_category = {
  "中文(简体)" : "zh_CN",
  "English(US)" : "en_US"
}

translate_mapping_dict = {
    # Animation Edit
    "Merge anim and apply": {"zh_CN": "合并动画并应用", "en_US": "Merge anim and apply"},
    "Clean anim and apply": {"zh_CN": "清空动画并应用", "en_US": "Clean anim and apply"},
    "Apply": {"zh_CN": "应用", "en_US": "Apply"},
    "Current Frame": {"zh_CN": "当前帧", "en_US": "Current Frame"},
    "Custom Frame": {"zh_CN": "自定义帧", "en_US": "Custom Frame"},
    "Options": {"zh_CN": "应用设置", "en_US": "Options"},

    # Apply setting
    "Apply Setting": {"zh_CN": "应用设定", "en_US": "Apply Setting"},
    "Animation": {"zh_CN": "动画", "en_US": "Animation"},
    "Set Key : ": {"zh_CN": "设置关键帧 : ", "en_US": "Set Key : "},
    "Controller : ": {"zh_CN": "控制器模式 : ", "en_US": "Controller : "},
    "Use Object Name : ": {"zh_CN": "使用 物体名称 重定向 : ", "en_US": "Use Object Name : "},
    "Use HIK Retarget : ": {"zh_CN": "使用 humanIK 重定向 : ", "en_US": "Use HIK Retarget : "},
    "Use Fbx Constraint : ": {"zh_CN": "使用 FBX约束 重定向 : ", "en_US": "Use Fbx Constraint : "},
    "One To One(FBX)": {"zh_CN": "点对点模式（FBX）: ", "en_US": "One To One(FBX)"},
    "Use One To One Constraint : ": {"zh_CN": "使用FBX 点对点 约束 : ", "en_US": "Use One To One Constraint : "},
    "Config File:": {"zh_CN": "配置文件:", "en_US": "Config File:"},
    "Enable Apply Filter : ": {"zh_CN": "启用控制器过滤 : ", "en_US": "Enable Apply Filter : "},
    "Filter Method : ": {"zh_CN": "过滤基于 : ", "en_US": "Filter Method : "},
    "refresh": {"zh_CN": "刷新列表", "en_US": "refresh"},
    "Cancel": {"zh_CN": "取消", "en_US": "Cancel"},
    "Current Selection": {"zh_CN": "当前选择", "en_US": "Current Selection"},
    "LSR Limb": {"zh_CN": "LSR肢体", "en_US": "LSR Limb"},
    "User Defined": {"zh_CN": "用户自定义", "en_US": "User Defined"},
    "Warning": {"zh_CN": "警告", "en_US": "Warning"},

    "You have selected: Options->Replace.\n"
    "Replace will empty All animation on current timeline or animation layer.\n\n"
    "Are you sure to apply?": {
        "zh_CN": "当前应用设置->清空。\n"
                 "接下来将会清空当前时间线和动画层上所有的动画数据。\n\n"
                 "确定要应用吗？",
        "en_US": "You have selected: Options->Replace.\n"
                 "Replace will empty All animation on current timeline or animation layer.\n\n"
                 "Are you sure to apply?"},

    # AnimLibMenuController
    "File": {"zh_CN": "文件", "en_US": "File"},
    "Set  Local  Data  Folder": {"zh_CN": "设置本地数据文件夹", "en_US": "Set  Local  Data  Folder"},
    "Create": {"zh_CN": "创建", "en_US": "Create"},
    "Reset": {"zh_CN": "重置", "en_US": "Reset"},
    "Reset  Tool  Setting": {"zh_CN": "重置工具设置", "en_US": "Reset  Tool  Setting"},
    "Reset  Layout": {"zh_CN": "重置布局", "en_US": "Reset  Layout"},

    # file listview
    "Search By Name..Support *": {"zh_CN": "按名字搜索..支持 * 模糊搜索", "en_US": "Search By Name..Support *"},
    "Sort By : ": {"zh_CN": "排列方式 ：", "en_US": "Sort By : "},

    # right click menu
    "Refresh": {"zh_CN": "刷新", "en_US": "Refresh"},
    "Delete": {"zh_CN": "删除", "en_US": "Delete"},
    "Open Source File": {"zh_CN": "打开源文件", "en_US": "Open Source File"},
    "Show In Explorer": {"zh_CN": "打开文件所在位置", "en_US": "Show In Explorer"},
    "Select Saved Controls": {"zh_CN": "选中保存的控制器", "en_US": "Select Saved Controls"},
    "Copy": {"zh_CN": "拷贝", "en_US": "Copy"},
    "Paste": {"zh_CN": "粘贴", "en_US": "Paste"},
    "Cut": {"zh_CN": "剪切", "en_US": "Cut"},
    "Pull" : {"zh_CN": "更新", "en_US": "Pull"},
    "Push" : {"zh_CN": "推送", "en_US": "Push"},
    "Create Folder": {"zh_CN": "创建文件夹", "en_US": "Create Folder"},

    # Pose create dialog
    "create new pose": {"zh_CN": "新建Pose", "en_US": "create new pose"},
    "Type in comment for this pose": {"zh_CN": "为该pose输入备注", "en_US": "Type in comment for this pose"},
    "Type in comment for this animation": {"zh_CN": "为该动画输入备注", "en_US": "Type in comment for this animation"},
    "Start Frame": {"zh_CN": "起始帧", "en_US": "Start Frame"},
    "End Frame": {"zh_CN": "结束帧", "en_US": "End Frame"},
    "Comment: ": {"zh_CN": "备注: ", "en_US": "Comment: "},
    "Data Name: ": {"zh_CN": "名称: ", "en_US": "Data Name: "},
    "Data Type: ": {"zh_CN": "类型: ", "en_US": "Data Type: "},
    "animation": {"zh_CN": "动画", "en_US": "animation"},
    "English Only": {"zh_CN": "请使用英文", "en_US": "English Only"},

    # pose creation dialog
    "Select all desired animatable object first.": {
        "zh_CN": "场景内选中需要保存的动画物体.",
        "en_US": "Select all desired animatable object first."},

    # git_folder_pull_dialog
    "Add From Remote": {"zh_CN": "从云端添加", "en_US": "Add From Remote"},
    "Add From Local": {"zh_CN": "从本地添加", "en_US": "Add From Local"},
    "User:": {"zh_CN": "用户名:", "en_US": "User:"},
    "Password:": {"zh_CN": "密码:", "en_US": "Password:"},
    "<a href='https://www.baidu.com' style='color: rgb(52, 173, 224)'>Forget Password?</a>":
    {"zh_CN": "<a href='https://git.tencent.com' style='color: rgb(52, 173, 224)'>忘记密码?</a>",
     "en_US": "<a href='https://git.tencent.com' style='color: rgb(52, 173, 224)'>Forget Password?</a>"},
    "Local Path:": {"zh_CN": "本地路径:", "en_US": "Local Path:"},
    "Add  Local  Git  Folder": {"zh_CN": "添加本地Git仓库", "en_US": "Add  Local  Git  Folder"},
    "Add  Remote  Git  Folder": {"zh_CN": "添加云端Git仓库", "en_US": "Add  Remote  Git  Folder"},
    "Enter your account": {"zh_CN": "输入您的账号", "en_US": "Enter your account"},
    "Enter your password": {"zh_CN": "输入您的密码", "en_US": "Enter your password"},
    "Enter Git HTTPS": {"zh_CN": "输入Git HTTPS", "en_US": "Enter Git HTTPS"},
    "Click button to browser folder": {"zh_CN": "点击按钮选择路径", "en_US": "Click button to browser folder"},
    "Git folder create": {"zh_CN": "创建Git仓库", "en_US": "Git folder create"},
    "Please select a empty folder!": {"zh_CN": "请选择一个空的文件夹", "en_US": "Please select a empty folder!"},
    "Confirm": {"zh_CN": "确认", "en_US": "Confirm"},

    # git view
    "No Git Data": {"zh_CN": "无 Git 仓库", "en_US": "No Git Data"},
    "Detected local change,  do you want to keep them?": {"zh_CN": "检测到本地变更, 要保留他们吗？", "en_US": "Detected local change,  do you want to keep them?"},
    "Are you sure to overwrite the remote change?": {"zh_CN": "确定覆盖云端变更吗？", "en_US": "Are you sure to overwrite the remote change?"},
    "Are you sure to remove selected local edit?": {"zh_CN": "确定移除本地变更吗？", "en_US": "Are you sure to remove selected local edit?"},
    "Not pull updates": {"zh_CN": "没有远端更新", "en_US": "Not pull updates"},
    "Not push updates": {"zh_CN": "没有本地更新", "en_US": "Not push updates"},
    "Not local edit Found": {"zh_CN": "没有本地修改", "en_US": "Not local edit Found"},
    "Remove Local Edit": {"zh_CN": "移除本地修改", "en_US": "Remove Local Edit"},
    "Are you sure to PULL?": {"zh_CN": "确定拉取以下路径吗", "en_US": "Are you sure to PULL?"},
    "found remote updates\n please pull first": {"zh_CN": "检测到远端更新, 请先拉取更新", "en_US": "found remote updates\n please pull first"},

    # AnimLibTableController
    "Information": {"zh_CN": "数据详情", "en_US": "Information"},
    "Preview": {"zh_CN": "预览", "en_US": "Preview"},
    "Controller Filter": {"zh_CN": "过滤控制器", "en_US": "Controller Filter"},
    "name": {"zh_CN": "数据名", "en_US": "name"},
    "user_created": {"zh_CN": "创建用户", "en_US": "user_created"},
    "date_created": {"zh_CN": "创建日期", "en_US": "date_created"},
    "comment": {"zh_CN": "备注", "en_US": "comment"},
    "type": {"zh_CN": "类型", "en_US": "type"},
    "num_nodes": {"zh_CN": "记录数量", "en_US": "num_nodes"},
    "source_file": {"zh_CN": "源文件", "en_US": "source_file"},
    "start_frame": {"zh_CN": "起始帧", "en_US": "start_frame"},
    "end_frame": {"zh_CN": "结束帧", "en_US": "end_frame"},


    # filter menu bar
    "Display": {"zh_CN": "显示", "en_US": "Display"},


    # bottom status
    "Space  ": {"zh_CN": "间距  ", "en_US": "Space  "},
    "Size  ": {"zh_CN": "大小  ", "en_US": "Size  "},

    # ask dialog
    "Data already exist. Do Replace?": {"zh_CN": "数据已经存在，要替换吗？", "en_US": "Data already exist. Do Replace?"},
    "Deletion is non-revertible. Are you sure?" : {"zh_CN": "确定要删除吗？删除操作不可撤销！", "en_US": "Deletion is non-revertible. Are you sure?"},
    "Object exists" : {"zh_CN": "对象已存在", "en_US": "Object exists"},
    "Exists object:" : {"zh_CN": "已存在对象：", "en_US": "Exists object:"},
    "Would you like to override it?" : {"zh_CN": "您想要覆盖它吗？", "en_US": "Would you like to override it?"},

    # animToast
    "can't not add data to a footage folder": {"zh_CN": "无法添加数据到素材文件夹", "en_US": "can't not add data to a footage folder"},
    "you haven't select anything in scene": {"zh_CN": "未选择场景中的任何物体", "en_US": "you haven't select anything in scene"},
    "empty -- animation range": {"zh_CN": "请输入动画范围", "en_US": "empty -- animation range"},
    "empty -- data name": {"zh_CN": "请输入数据名称", "en_US": "empty -- data name"},
    "Not Item Selected" : {"zh_CN": "未选择任何数据", "en_US": "Not Item Selected"},
    "Error Reading Data\\nPlease Contact TA" : {"zh_CN": "读取数据时发生错误\n请联系TA", "en_US": "Error Reading Data\nPlease Contact TA"},
    "Apply [%s/%s] Done!" : {"zh_CN": "套用控制器 [%s/%s] 成功!", "en_US": "Apply [%s/%s] Done!"},
    "Apply Finished!" : {"zh_CN": "应用完成！", "en_US": "Apply Finished!"},
    "No Match Found In Scene" : {"zh_CN": "场景内未找到匹配控制器", "en_US": "No Match Found In Scene"},
    "Please Enter Start Frame" : {"zh_CN": "请输入初始帧", "en_US": "Please Enter Start Frame"},
    "Animation Data Broken\\nXX [%s] XX\\nPlease Contact TA" : {"zh_CN": "动画数据损坏\nXX [%s] XX\n请联系TA", "en_US": "Animation Data Broken\nXX [%s] XX\nPlease Contact TA"},
    "Wrong One to One FBX Config": {"zh_CN": "错误的FBX one to one json配置", "en_US": "Wrong One to One FBX Config"},
    "Item contains 0 animation data\\Use HIK or Contact TA" : {"zh_CN": "没有存控制器数据\n使用HIK或者联系TA", "en_US": "Item contains 0 animation data\nUse HIK or Contact TA"},
    "Select [1] Animation Layer for apply": {"zh_CN": "请先选择一个动画层应用", "en_US": "Select [1] Animation Layer for apply"},
    "Only Select [1] Animation Layer for apply": {"zh_CN": "只能选一个动画层应用","en_US": "Only Select [1] Animation Layer for apply"},
    "Selected Animation Layer is Locked!": {"zh_CN": "选中动画层已锁!","en_US": "Selected Animation Layer is Locked!"},
    "current dcc doesn't support controller data\\n please use skeleton": {"zh_CN": "当前DCC不支持控制器数据\n请使用骨骼数据", "en_US": "current dcc doesn't support controller data\n please use skeleton"},
    "Can't find any HIK node in scene": {"zh_CN": "场景内找不到任何HIK节点", "en_US": "Can't find any HIK node in scene"},
    "Item contains 0 HIK data\\nUse Ctrl Data or Contact TA": {"zh_CN": "没存HIK数据\n使用控制器动画或者联系TA", "en_US": "Item contains 0 HIK data\nUse Ctrl Data or Contact TA"},



    #fix name mapping
    "fix name mapping": {"zh_CN": "数据重定向", "en_US": "fix name mapping"},
    "Missing": {"zh_CN": "丢失", "en_US": "Missing"},
    "Continue": {"zh_CN": "继续", "en_US": "Continue"},

    # window title
    "Windows" : {"zh_CN": "窗口", "en_US": "Windows"},

}