#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线编辑器 - Controller层
负责协调Model和View，处理用户交互逻辑
"""

from PySide2 import QtCore, QtGui
from model import BezierCurveModel
from view import BezierCurveWidget, KeyframePropertiesWidget


class BezierCurveController(QtCore.QObject):
    """贝塞尔曲线编辑器控制器"""
    
    def __init__(self, parent=None):
        super(BezierCurveController, self).__init__(parent)
        
        # 创建Model和View
        self.model = BezierCurveModel()
        self.curve_view = BezierCurveWidget()
        self.properties_view = KeyframePropertiesWidget()
        
        # 交互状态
        self.drag_start_pos = None
        self.drag_start_state = None
        self.dragging_keyframe = None
        self.dragging_tangent = None
        
        # 连接信号
        self._connect_signals()
        
        # 初始化视图
        self._update_curve_view()
    
    def _connect_signals(self):
        """连接Model和View的信号"""
        # Model -> View 信号连接
        self.model.keyframes_changed.connect(self._on_keyframes_changed)
        self.model.selected_keyframe_changed.connect(self._on_selected_keyframe_changed)
        self.model.view_range_changed.connect(self._on_view_range_changed)
        
        # View -> Controller 信号连接
        self.curve_view.mouse_pressed.connect(self._on_mouse_pressed)
        self.curve_view.mouse_moved.connect(self._on_mouse_moved)
        self.curve_view.mouse_released.connect(self._on_mouse_released)
        self.curve_view.key_pressed.connect(self._on_key_pressed)
        
        # 属性编辑器信号连接
        self.properties_view.time_changed.connect(self._on_property_time_changed)
        self.properties_view.value_changed.connect(self._on_property_value_changed)
        self.properties_view.in_tangent_changed.connect(self._on_property_in_tangent_changed)
        self.properties_view.out_tangent_changed.connect(self._on_property_out_tangent_changed)
    
    # ==================== Model事件处理 ====================
    
    def _on_keyframes_changed(self, keyframes):
        """关键帧数据改变"""
        self.curve_view.update_keyframes(keyframes)
        # 更新曲线点
        curve_points = self.model.get_curve_points()
        self.curve_view.update_curve_points(curve_points)
    
    def _on_selected_keyframe_changed(self, keyframe):
        """选中关键帧改变"""
        self.curve_view.update_selected_keyframe(keyframe)
        self.properties_view.set_keyframe(keyframe)
    
    def _on_view_range_changed(self, time_range, value_range):
        """视图范围改变"""
        self.curve_view.update_view_range(time_range, value_range)
        # 重新计算曲线点
        curve_points = self.model.get_curve_points()
        self.curve_view.update_curve_points(curve_points)
    
    # ==================== View事件处理 ====================
    
    def _on_mouse_pressed(self, pos, button):
        """鼠标按下处理"""
        if button == QtCore.Qt.LeftButton:
            self._handle_left_click(pos)
        elif button == QtCore.Qt.RightButton:
            self._handle_right_click(pos)
    
    def _handle_left_click(self, pos):
        """处理左键点击"""
        # 检查是否点击了切线控制点
        tangent_kf, tangent_type = self.curve_view.get_tangent_at_pos(pos)
        if tangent_kf:
            self.model.set_selected_keyframe(tangent_kf)
            self.dragging_tangent = (tangent_kf, tangent_type)
            self.drag_start_pos = pos
            self._save_drag_start_state()
            self.curve_view.set_dragging_state(tangent=self.dragging_tangent)
            return
        
        # 检查是否点击了关键帧
        keyframe = self.curve_view.get_keyframe_at_pos(pos)
        if keyframe:
            self.model.set_selected_keyframe(keyframe)
            self.dragging_keyframe = keyframe
            self.drag_start_pos = pos
            self._save_drag_start_state()
            self.curve_view.set_dragging_state(keyframe=keyframe)
            print(f"选中关键帧: 时间={keyframe.time:.3f}, 值={keyframe.value:.3f}")
        else:
            # 只有在曲线编辑区域内才添加新关键帧
            if self.curve_view.is_pos_in_curve_area(pos):
                time, value = self.curve_view.screen_to_world(pos)
                new_keyframe = self.model.add_keyframe(time, value)
                self.model.set_selected_keyframe(new_keyframe)
                print(f"创建关键帧: 时间={time:.3f}, 值={value:.3f}")
            else:
                # 点击在区域外，取消选择
                self.model.set_selected_keyframe(None)
                print("点击在编辑区域外，已取消选择")
    
    def _handle_right_click(self, pos):
        """处理右键点击"""
        if self.curve_view.is_pos_in_curve_area(pos):
            keyframe = self.curve_view.get_keyframe_at_pos(pos)
            if keyframe:
                if self.model.remove_keyframe(keyframe):
                    print(f"右键删除关键帧: 时间={keyframe.time:.3f}, 值={keyframe.value:.3f}")
    
    def _on_mouse_moved(self, pos):
        """鼠标移动处理"""
        if self.dragging_keyframe:
            self._handle_keyframe_drag(pos)
        elif self.dragging_tangent:
            self._handle_tangent_drag(pos)
    
    def _handle_keyframe_drag(self, pos):
        """处理关键帧拖拽"""
        if self.curve_view.is_pos_in_curve_area(pos):
            time, value = self.curve_view.screen_to_world(pos)
            # 限制在视图范围内
            time = max(self.model.time_range[0], min(self.model.time_range[1], time))
            value = max(self.model.value_range[0], min(self.model.value_range[1], value))
            
            # 直接更新关键帧，不保存状态（拖拽结束时统一保存）
            self.model.update_keyframe(self.dragging_keyframe, time=time, value=value, save_state=False)
    
    def _handle_tangent_drag(self, pos):
        """处理切线拖拽"""
        keyframe, tangent_type = self.dragging_tangent
        kf_pos = self.curve_view.world_to_screen(keyframe.time, keyframe.value)
        delta = pos - kf_pos
        
        # 限制切线长度
        max_tangent_length = 100.0
        if delta.manhattanLength() > max_tangent_length:
            length = (delta.x() ** 2 + delta.y() ** 2) ** 0.5
            if length > 0:
                scale = max_tangent_length / length
                delta = QtCore.QPointF(delta.x() * scale, delta.y() * scale)
        
        tangent_x = delta.x() / 30.0
        tangent_y = -delta.y() / 30.0
        new_tangent = (tangent_x, tangent_y)
        
        # 更新切线
        if tangent_type == 'in':
            self.model.update_keyframe(keyframe, in_tangent=new_tangent, save_state=False)
        else:
            self.model.update_keyframe(keyframe, out_tangent=new_tangent, save_state=False)
    
    def _on_mouse_released(self, pos):
        """鼠标释放处理"""
        # 如果进行了拖拽操作，保存状态
        if self.dragging_keyframe or self.dragging_tangent:
            if self.drag_start_state and self._has_state_changed():
                if self.dragging_keyframe:
                    self.model.save_state("移动关键帧")
                elif self.dragging_tangent:
                    self.model.save_state("调整切线")
        
        # 清理拖拽状态
        self.dragging_keyframe = None
        self.dragging_tangent = None
        self.drag_start_pos = None
        self.drag_start_state = None
        self.curve_view.set_dragging_state()
    
    def _on_key_pressed(self, key, modifiers):
        """键盘按下处理"""
        if key == QtCore.Qt.Key_Delete:
            self.delete_selected_keyframe()
        elif key == QtCore.Qt.Key_Escape:
            self.model.set_selected_keyframe(None)
        elif key == QtCore.Qt.Key_A and modifiers == QtCore.Qt.ControlModifier:
            self.add_keyframe_at_center()
        elif key == QtCore.Qt.Key_Z and modifiers == QtCore.Qt.ControlModifier:
            if self.model.undo():
                print("撤销操作")
        elif key == QtCore.Qt.Key_Y and modifiers == QtCore.Qt.ControlModifier:
            if self.model.redo():
                print("重做操作")
    
    # ==================== 属性编辑器事件处理 ====================
    
    def _on_property_time_changed(self, time):
        """属性面板时间改变"""
        if self.model.selected_keyframe:
            self.model.update_keyframe(self.model.selected_keyframe, time=time)
    
    def _on_property_value_changed(self, value):
        """属性面板值改变"""
        if self.model.selected_keyframe:
            self.model.update_keyframe(self.model.selected_keyframe, value=value)
    
    def _on_property_in_tangent_changed(self, tangent):
        """属性面板入切线改变"""
        if self.model.selected_keyframe:
            self.model.update_keyframe(self.model.selected_keyframe, in_tangent=tangent)
    
    def _on_property_out_tangent_changed(self, tangent):
        """属性面板出切线改变"""
        if self.model.selected_keyframe:
            self.model.update_keyframe(self.model.selected_keyframe, out_tangent=tangent)
    
    # ==================== 公共接口 ====================
    
    def add_keyframe_at_center(self):
        """在时间轴中间添加关键帧"""
        mid_time = (self.model.time_range[0] + self.model.time_range[1]) / 2
        mid_value = (self.model.value_range[0] + self.model.value_range[1]) / 2
        new_keyframe = self.model.add_keyframe(mid_time, mid_value)
        self.model.set_selected_keyframe(new_keyframe)
        print(f"已添加关键帧: 时间={mid_time:.3f}, 值={mid_value:.3f}")
    
    def delete_selected_keyframe(self):
        """删除选中的关键帧"""
        if self.model.selected_keyframe:
            if len(self.model.keyframes) > 2:
                keyframe = self.model.selected_keyframe
                if self.model.remove_keyframe(keyframe):
                    print(f"已删除关键帧: 时间={keyframe.time:.3f}, 值={keyframe.value:.3f}")
            else:
                print("至少需要保留2个关键帧，无法删除")
        else:
            print("没有选中的关键帧")
    
    def set_time_range(self, start, end):
        """设置时间范围"""
        self.model.set_time_range(start, end)
    
    def set_value_range(self, min_val, max_val):
        """设置值范围"""
        self.model.set_value_range(min_val, max_val)
    
    def set_grid_enabled(self, enabled):
        """设置网格显示"""
        self.model.set_grid_enabled(enabled)
        self.curve_view.set_grid_enabled(enabled)
    
    def undo(self):
        """撤销操作"""
        return self.model.undo()
    
    def redo(self):
        """重做操作"""
        return self.model.redo()
    
    def can_undo(self):
        """是否可以撤销"""
        return self.model.can_undo()
    
    def can_redo(self):
        """是否可以重做"""
        return self.model.can_redo()
    
    def apply_preset(self, preset_name):
        """应用预设曲线"""
        self.model.clear_all()
        
        if preset_name == "linear":
            self.model.add_keyframe(0.0, 0.0, save_state=False)
            self.model.add_keyframe(10.0, 1.0, save_state=False)
        elif preset_name == "ease_in":
            kf1 = self.model.add_keyframe(0.0, 0.0, save_state=False)
            kf2 = self.model.add_keyframe(10.0, 1.0, save_state=False)
            self.model.update_keyframe(kf1, out_tangent=(3.0, 0.0), save_state=False)
            self.model.update_keyframe(kf2, in_tangent=(-1.0, 0.0), save_state=False)
        elif preset_name == "ease_out":
            kf1 = self.model.add_keyframe(0.0, 0.0, save_state=False)
            kf2 = self.model.add_keyframe(10.0, 1.0, save_state=False)
            self.model.update_keyframe(kf1, out_tangent=(1.0, 0.0), save_state=False)
            self.model.update_keyframe(kf2, in_tangent=(-3.0, 0.0), save_state=False)
        elif preset_name == "ease_in_out":
            kf1 = self.model.add_keyframe(0.0, 0.0, save_state=False)
            kf2 = self.model.add_keyframe(10.0, 1.0, save_state=False)
            self.model.update_keyframe(kf1, out_tangent=(2.0, 0.0), save_state=False)
            self.model.update_keyframe(kf2, in_tangent=(-2.0, 0.0), save_state=False)
        elif preset_name == "bounce":
            self.model.add_keyframe(0.0, 0.0, save_state=False)
            self.model.add_keyframe(3.0, 1.2, save_state=False)
            self.model.add_keyframe(6.0, 0.8, save_state=False)
            self.model.add_keyframe(10.0, 1.0, save_state=False)
        
        self.model.save_state(f"应用预设: {preset_name}")
    
    def export_data(self):
        """导出曲线数据"""
        return self.model.export_data()
    
    def import_data(self, data):
        """导入曲线数据"""
        self.model.import_data(data)
    
    # ==================== 辅助方法 ====================
    
    def _save_drag_start_state(self):
        """保存拖拽开始时的状态"""
        self.drag_start_state = [kf.copy() for kf in self.model.keyframes]
    
    def _has_state_changed(self):
        """检查状态是否改变"""
        if not self.drag_start_state or len(self.drag_start_state) != len(self.model.keyframes):
            return True
        
        for i, keyframe in enumerate(self.model.keyframes):
            if keyframe != self.drag_start_state[i]:
                return True
        
        return False
    
    def _update_curve_view(self):
        """更新曲线视图"""
        self.curve_view.update_keyframes(self.model.keyframes)
        self.curve_view.update_selected_keyframe(self.model.selected_keyframe)
        self.curve_view.update_view_range(self.model.time_range, self.model.value_range)
        curve_points = self.model.get_curve_points()
        self.curve_view.update_curve_points(curve_points)
    
    # ==================== 属性访问 ====================
    
    def get_curve_widget(self):
        """获取曲线绘制控件"""
        return self.curve_view
    
    def get_properties_widget(self):
        """获取属性编辑控件"""
        return self.properties_view
    
    def get_model(self):
        """获取数据模型"""
        return self.model


class BezierCurveMainWindow(QtWidgets.QWidget):
    """贝塞尔曲线编辑器主窗口 - 整合MVC架构"""

    def __init__(self, parent=None):
        super(BezierCurveMainWindow, self).__init__(parent)
        self.setWindowTitle("贝塞尔曲线K帧编辑器 (MVC架构)")
        self.setMinimumSize(800, 600)

        # 创建控制器（自动创建Model和View）
        self.controller = BezierCurveController()

        # 初始化UI
        self.init_ui()
        self.setup_shortcuts()
        self.connect_signals()

    def init_ui(self):
        """初始化UI"""
        main_layout = QtWidgets.QHBoxLayout()

        # 左侧：曲线编辑器
        left_widget = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout()

        # 工具栏
        toolbar = self.create_toolbar()
        left_layout.addWidget(toolbar)

        # 曲线编辑器（从控制器获取）
        self.curve_widget = self.controller.get_curve_widget()
        left_layout.addWidget(self.curve_widget)

        # 时间轴控制
        time_control = self.create_time_control()
        left_layout.addWidget(time_control)

        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget, 3)

        # 右侧：属性面板
        right_widget = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout()

        # 关键帧属性（从控制器获取）
        properties_group = QtWidgets.QGroupBox("关键帧属性")
        self.properties_widget = self.controller.get_properties_widget()
        properties_layout = QtWidgets.QVBoxLayout()
        properties_layout.addWidget(self.properties_widget)
        properties_group.setLayout(properties_layout)
        right_layout.addWidget(properties_group)

        # 曲线设置
        settings_group = self.create_settings_group()
        right_layout.addWidget(settings_group)

        # 预设按钮
        presets_group = self.create_presets_group()
        right_layout.addWidget(presets_group)

        right_layout.addStretch()
        right_widget.setLayout(right_layout)
        main_layout.addWidget(right_widget, 1)

        self.setLayout(main_layout)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        # 添加关键帧按钮
        self.add_keyframe_btn = QtWidgets.QPushButton("添加关键帧 (Ctrl+A)")
        self.add_keyframe_btn.setToolTip("在时间轴中间添加关键帧\n快捷键: Ctrl+A")
        self.add_keyframe_btn.clicked.connect(self.controller.add_keyframe_at_center)
        layout.addWidget(self.add_keyframe_btn)

        # 删除关键帧按钮
        self.delete_keyframe_btn = QtWidgets.QPushButton("删除关键帧 (Del)")
        self.delete_keyframe_btn.setToolTip("删除选中的关键帧\n快捷键: Delete")
        self.delete_keyframe_btn.clicked.connect(self.controller.delete_selected_keyframe)
        layout.addWidget(self.delete_keyframe_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 撤销按钮
        self.undo_btn = QtWidgets.QPushButton("撤销 (Ctrl+Z)")
        self.undo_btn.setToolTip("撤销上一步操作\n快捷键: Ctrl+Z")
        self.undo_btn.clicked.connect(self.controller.undo)
        layout.addWidget(self.undo_btn)

        # 重做按钮
        self.redo_btn = QtWidgets.QPushButton("重做 (Ctrl+Y)")
        self.redo_btn.setToolTip("重做上一步操作\n快捷键: Ctrl+Y")
        self.redo_btn.clicked.connect(self.controller.redo)
        layout.addWidget(self.redo_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 网格开关
        self.grid_checkbox = QtWidgets.QCheckBox("显示网格")
        self.grid_checkbox.setChecked(True)
        self.grid_checkbox.setToolTip("显示/隐藏网格线")
        self.grid_checkbox.toggled.connect(self.controller.set_grid_enabled)
        layout.addWidget(self.grid_checkbox)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 快捷键提示标签
        shortcut_label = QtWidgets.QLabel("快捷键: Del删除 | Esc取消 | Ctrl+A添加 | Ctrl+Z撤销 | Ctrl+Y重做")
        shortcut_label.setStyleSheet("color: #888888; font-size: 10px;")
        layout.addWidget(shortcut_label)

        layout.addStretch()
        toolbar.setLayout(layout)
        return toolbar

    def create_time_control(self):
        """创建时间轴控制"""
        widget = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        layout.addWidget(QtWidgets.QLabel("时间范围:"))

        self.time_start_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_start_spinbox.setRange(-999.0, 999.0)
        self.time_start_spinbox.setValue(0.0)
        self.time_start_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_start_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.time_end_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_end_spinbox.setRange(-999.0, 999.0)
        self.time_end_spinbox.setValue(10.0)
        self.time_end_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_end_spinbox)

        layout.addWidget(QtWidgets.QLabel("|"))
        layout.addWidget(QtWidgets.QLabel("值范围:"))

        self.value_min_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_min_spinbox.setRange(-999.0, 999.0)
        self.value_min_spinbox.setValue(-1.0)
        self.value_min_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_min_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.value_max_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_max_spinbox.setRange(-999.0, 999.0)
        self.value_max_spinbox.setValue(1.0)
        self.value_max_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_max_spinbox)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_settings_group(self):
        """创建设置组"""
        group = QtWidgets.QGroupBox("曲线设置")
        layout = QtWidgets.QVBoxLayout()

        # 插值类型
        interp_layout = QtWidgets.QHBoxLayout()
        interp_layout.addWidget(QtWidgets.QLabel("插值类型:"))
        self.interp_combo = QtWidgets.QComboBox()
        self.interp_combo.addItems(["贝塞尔", "线性", "常量"])
        interp_layout.addWidget(self.interp_combo)
        layout.addLayout(interp_layout)

        # 切线模式
        tangent_layout = QtWidgets.QHBoxLayout()
        tangent_layout.addWidget(QtWidgets.QLabel("切线模式:"))
        self.tangent_combo = QtWidgets.QComboBox()
        self.tangent_combo.addItems(["自由", "对称", "平滑"])
        tangent_layout.addWidget(self.tangent_combo)
        layout.addLayout(tangent_layout)

        group.setLayout(layout)
        return group

    def create_presets_group(self):
        """创建预设组"""
        group = QtWidgets.QGroupBox("预设曲线")
        layout = QtWidgets.QVBoxLayout()

        # 预设按钮
        presets = [
            ("线性", "linear"),
            ("缓入", "ease_in"),
            ("缓出", "ease_out"),
            ("缓入缓出", "ease_in_out"),
            ("弹跳", "bounce"),
        ]

        for name, preset_id in presets:
            btn = QtWidgets.QPushButton(name)
            btn.clicked.connect(lambda checked, p=preset_id: self.controller.apply_preset(p))
            layout.addWidget(btn)

        group.setLayout(layout)
        return group

    def setup_shortcuts(self):
        """设置快捷键"""
        # Delete键删除关键帧
        delete_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Delete, self)
        delete_shortcut.activated.connect(self.controller.delete_selected_keyframe)

        # Ctrl+A添加关键帧
        add_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+A"), self)
        add_shortcut.activated.connect(self.controller.add_keyframe_at_center)

        # Esc取消选择
        esc_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence(QtCore.Qt.Key_Escape), self)
        esc_shortcut.activated.connect(lambda: self.controller.model.set_selected_keyframe(None))

        # Ctrl+Z撤销
        undo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Undo, self)
        undo_shortcut.activated.connect(self.controller.undo)

        # Ctrl+Y重做
        redo_shortcut = QtWidgets.QShortcut(QtGui.QKeySequence.Redo, self)
        redo_shortcut.activated.connect(self.controller.redo)

    def connect_signals(self):
        """连接信号"""
        # 监听撤销重做状态变化，更新按钮状态
        self.controller.model.undo_redo_state_changed.connect(self.update_undo_redo_buttons)

    def update_undo_redo_buttons(self, can_undo, can_redo):
        """更新撤销重做按钮状态"""
        self.undo_btn.setEnabled(can_undo)
        self.redo_btn.setEnabled(can_redo)

    def update_time_range(self):
        """更新时间范围"""
        start = self.time_start_spinbox.value()
        end = self.time_end_spinbox.value()
        if start < end:
            self.controller.set_time_range(start, end)

    def update_value_range(self):
        """更新值范围"""
        min_val = self.value_min_spinbox.value()
        max_val = self.value_max_spinbox.value()
        if min_val < max_val:
            self.controller.set_value_range(min_val, max_val)
