#!/usr/bin/env python
# -*- coding: utf-8 -*-
###################################################################
# Author: <PERSON> yanru
# Date  : 2019.3
# Email : <EMAIL>
###################################################################
"""MDockWidget"""
from lsr.maya.raid_game.animtools.dayu_widgets.qt import QDockWidget,Qt


class MDockWidget(QDockWidget):
    """
    Just apply the qss. No more extend.
    """

    def __init__(self, title='', parent=None, flags=Qt.Widget):
        super(MDockWidget, self).__init__(title, parent=parent, flags=flags)
