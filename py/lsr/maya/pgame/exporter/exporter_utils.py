import maya.cmds as cmds
from lsr.qt.core.widgets.qr_widgets.qr_anim_clip_editor_widget import QRAnimClipData
import json
from Qt import QtWidgets
from pathlib import Path
import lsr.python.core.logger as logger


def get_maya_file():
    """
    Returns the current maya file.
    """
    return cmds.file(q=True, sn=True)


def get_config_file():
    """
    Returns the config file.
    """
    maya_file = Path(get_maya_file())
    if not maya_file.exists():
        logger.error('Please save the file first.')
        return None

    maya_path = maya_file.parent
    json_file_name = maya_file.stem + '_Split.json'
    full_json_file = maya_path / json_file_name
    return full_json_file


def load_file_data():
    """
    Loads the file data.
    """
    maya_file = Path(get_maya_file())
    name = maya_file.stem

    if name is None or name.strip() == "":
        name = 'None Name'
        
    start_frame = int(cmds.playbackOptions(q=True, min=True))
    end_frame = int(cmds.playbackOptions(q=True, max=True))
    return QRAnimClipData(name, start_frame, end_frame, True)


def load_config_data(file):
    """
    Returns the config data.
    """
    with open(file, 'r') as f:
        clip_data = json.load(f)
        return [QRAnimClipData.from_dict(data) for data in clip_data]

    return None


def save_config_data(file, data):
    """
    Saves the config data.
    """
    with open(file, "w") as f:
        json.dump([data.to_dict() for data in data], f, indent=4)


def set_anim_clip_data(anim_clip_editor):
    """
    Sets the anim clip data.
    """
    anim_clip_editor.remove_all_anim_clip()
    config_file = get_config_file()
    load_file_success = False
    if config_file.exists():
        data = load_config_data(str(config_file))
        if data:
            for clip_data in data:
                anim_clip_editor.add_anim_clip_by_data(clip_data)
                load_file_success = True
        else:
            logger.error('Failed to load config data.')

    if not load_file_success:
        clip_data = load_file_data()
        anim_clip_editor.add_anim_clip_by_data(clip_data)
