
from functools import partial
from Qt import QtWidgets, QtCore
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.utils.mobu_utils import get_current_character_longname


class CollectCharacterUI(QtWidgets.QWidget):
    def __init__(self, parent=None, *args, **kwargs):
        super(CollectCharacterUI, self).__init__(parent)
        self._init_layout()
        self._preset_widget()
        self._init_signal()

    def _init_layout(self, *args, **kwargs):
        base_lay = QtWidgets.QHBoxLayout(self)
        self.setLayout(base_lay)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.layout().setAlignment(QtCore.Qt.AlignTop | QtCore.Qt.AlignCenter)

        self.char_name_line = QtWidgets.QLineEdit(self)
        self.load_char_btn = QtWidgets.QPushButton("Load Character", self)

        base_lay.addWidget(self.char_name_line)
        base_lay.addWidget(self.load_char_btn)

    def _preset_widget(self, *args, **kwargs):
        self.layout().setAlignment(QtCore.Qt.AlignRight)

        self.char_name_line.setReadOnly(True)
        self.load_char_btn.setFixedWidth(100)


    def _init_signal(self, *args, **kwargs):
        self.load_char_btn.clicked.connect(partial(self._on_collect_btn_clicked))

    def _on_collect_btn_clicked(self, *args, **kwargs):
        self.set_long_name()

    def set_long_name(self, *args, **kwargs):
        char_long_name = get_current_character_longname()
        self.char_name_line.setText(char_long_name)

    def get_long_name(self, *args, **kwargs):
        return self.char_name_line.text()
