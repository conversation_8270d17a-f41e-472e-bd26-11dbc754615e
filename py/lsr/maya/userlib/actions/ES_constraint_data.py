import json

import maya.OpenMaya as OpenMaya
import maya.cmds as cmds
from maya import mel

from lsr.maya.nodezoo.node import Node
from lsr.maya.nodezoo.node import Joint
from lsr.maya.nodezoo.node import Transform
from lsr.maya.rig.base_actions import BaseRigUtilAction

import lsr.protostar.core.parameter as pa
import lsr.protostar.core.exception as exp


class ES_Save_Constraints(BaseRigUtilAction):
    """
    ES Save Constraints Action
    """

    _UI_ICON = 'data_exchange'

    @pa.str_param(default='dy_*_pendantRoot???_???')
    def search_string(self):
        """Search String"""

    @pa.list_param(item_type='str')
    def extra_bones(self):
        """list of extra bones"""

    @pa.file_param(ext='json')
    def export_path(self):
        """The json data file path."""

    def run(self):
        """Executes this action."""
        if not self.export_path.value:
            raise exp.ActionError('export path not defined!')

        data = []
        save_path = self.export_path.value

        pendant_list = cmds.ls(self.search_string.value, dag=True, exactType='joint')
        pendant_list = [Joint(pendant) for pendant in pendant_list]

        if self.extra_bones.value:
            for bone_name in self.extra_bones.value:
                if Node.object_exist(bone_name):
                    pendant_list.append(Node(bone_name))

        for pendant in pendant_list:
            pendant_data = pendant.export()
            cns = cmds.parentConstraint(pendant, query=True)
            if cns:
                pendant_data['cns'] = cns
                cns_target_list = cmds.parentConstraint(pendant, query=True, targetList=True)
                pendant_data['cns_target_list'] = cns_target_list
                cns = Transform(cns)
                weights_attr_list = cns.list_attr(userDefined=True)
                weights_list = []
                for w_attr in weights_attr_list:
                    weights_list.append(w_attr.value)
                pendant_data['weights_list'] = weights_list
            data.append(pendant_data)

        with open(save_path, 'w') as data_file:
            json.dump(data, data_file, indent=4, encoding='utf-8')


class ES_Import_Constraints(BaseRigUtilAction):
    """
    ES Import Constraints Action
    """

    _UI_ICON = 'data_exchange'

    @pa.file_param(ext='json')
    def import_path(self):
        """The json data file path."""

    def run(self):
        """Executes this action."""
        if not self.import_path.value:
            raise exp.ActionError('import path not defined!')

        data_path = self.import_path.value

        with open(data_path, 'r') as json_file:
            data = json.load(json_file)

        for pendant_data in data:
            jnt = Joint.create(name=pendant_data['name'])
            cmds.select(cl=True)
            jnt.set_matrix(pendant_data['creation']['worldMatrix'], space='world')
            jnt.set_scale([1, 1, 1])
            jnt.useOutlinerColor.value = True
            jnt.outlinerColor.value = [1, 0, 0]

        all_new_joints = []
        for pendant_data in data:
            if Node.object_exist(pendant_data['name']):
                jnt = Joint(pendant_data['name'])
                all_new_joints.append(jnt)
                parent_name = pendant_data['creation']['parent']
                if Node.object_exist(parent_name):
                    parent_node = Node(parent_name)
                    jnt.set_parent(parent_node)
                    cmds.select(cl=True)

        for jnt in all_new_joints:
            jnt.make_identity(apply=True, rotate=True, scale=True)

        for pendant_data in data:
            if 'cns' in pendant_data:
                jnt = Joint(pendant_data['name'])
                target_list = pendant_data['cns_target_list']

                const = jnt.constrain('parent', target_list, maintainOffset=True)
                const.interpType.value = 2
                weights_list = pendant_data['weights_list']
                weights_attr_list = const.list_attr(ud=True)
                for i, w_attr in enumerate(weights_attr_list):
                    w_attr.value = weights_list[i]
                    w_attr.locked = True

                const.nds.locked = True
                const.int.locked = True
                const.rdtx.locked = True
                const.rdty.locked = True
                const.rdtz.locked = True
                const.hiddenInOutliner.value = True
        try:
            mel.eval('AEdagNodeCommonRefreshOutliners()')
        except RuntimeError:
            pass

        OpenMaya.MGlobal.displayInfo(
            'Imported constraints data from: {}'.format(data_path))