
from functools import partial
from Qt import QtWidgets, QtCore
from lsr.mobu.OG2_tool_lib.RootMotion_MBLinkUE.utils.mobu_utils import get_current_character_longname


class CollectNameSpaceUI(QtWidgets.QWidget):
    def __init__(self, parent=None, *args, **kwargs):
        super(CollectNameSpaceUI, self).__init__(parent)
        self._init_layout()
        self._preset_widget()
        self._init_signal()

    def _init_layout(self, *args, **kwargs):
        base_lay = QtWidgets.QHBoxLayout(self)
        self.setLayout(base_lay)
        self.layout().setContentsMargins(0, 0, 0, 0)
        self.layout().setSpacing(0)
        self.layout().setAlignment(QtCore.Qt.AlignTop | QtCore.Qt.AlignCenter)

        self.name_space_line = QtWidgets.QLineEdit(self)
        self.name_space_btn = QtWidgets.QPushButton("Load Character", self)

        base_lay.addWidget(self.name_space_line)
        base_lay.addWidget(self.name_space_btn)

    def _preset_widget(self, *args, **kwargs):
        self.layout().setAlignment(QtCore.Qt.AlignRight)

        self.name_space_line.setReadOnly(True)
        self.name_space_btn.setFixedWidth(100)


    def _init_signal(self, *args, **kwargs):
        self.name_space_btn.clicked.connect(partial(self._on_namespace_btn_clicked))

    def _on_namespace_btn_clicked(self, *args, **kwargs):
        self.set_name_space()

    def set_name_space(self, *args, **kwargs):
        name_space = get_current_character_longname()
        self.name_space_line.setText(name_space)

    def get_name_space(self, *args, **kwargs):
        return self.name_space_line.text()
