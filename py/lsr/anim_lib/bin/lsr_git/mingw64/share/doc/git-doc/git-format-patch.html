<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>git-format-patch(1)</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="manpage">
<div id="header">
<h1>git-format-patch(1) Manual Page</h1>
<h2 id="_name">NAME</h2>
<div class="sectionbody">
<p>git-format-patch - Prepare patches for e-mail submission</p>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="_synopsis">SYNOPSIS</h2>
<div class="sectionbody">
<div class="verseblock">
<pre class="content"><em>git format-patch</em> [-k] [(-o|--output-directory) &lt;dir&gt; | --stdout]
                   [--no-thread | --thread[=&lt;style&gt;]]
                   [(--attach|--inline)[=&lt;boundary&gt;] | --no-attach]
                   [-s | --signoff]
                   [--signature=&lt;signature&gt; | --no-signature]
                   [--signature-file=&lt;file&gt;]
                   [-n | --numbered | -N | --no-numbered]
                   [--start-number &lt;n&gt;] [--numbered-files]
                   [--in-reply-to=&lt;message id&gt;] [--suffix=.&lt;sfx&gt;]
                   [--ignore-if-in-upstream] [--always]
                   [--cover-from-description=&lt;mode&gt;]
                   [--rfc] [--subject-prefix=&lt;subject prefix&gt;]
                   [(--reroll-count|-v) &lt;n&gt;]
                   [--to=&lt;email&gt;] [--cc=&lt;email&gt;]
                   [--[no-]cover-letter] [--quiet]
                   [--[no-]encode-email-headers]
                   [--no-notes | --notes[=&lt;ref&gt;]]
                   [--interdiff=&lt;previous&gt;]
                   [--range-diff=&lt;previous&gt; [--creation-factor=&lt;percent&gt;]]
                   [--filename-max-length=&lt;n&gt;]
                   [--progress]
                   [&lt;common diff options&gt;]
                   [ &lt;since&gt; | &lt;revision range&gt; ]</pre>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_description">DESCRIPTION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Prepare each non-merge commit with its "patch" in
one "message" per commit, formatted to resemble a UNIX mailbox.
The output of this command is convenient for e-mail submission or
for use with <em>git am</em>.</p>
</div>
<div class="paragraph">
<p>A "message" generated by the command consists of three parts:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A brief metadata header that begins with <code>From &lt;commit&gt;</code>
with a fixed <code>Mon Sep 17 00:00:00 2001</code> datestamp to help programs
like "file(1)" to recognize that the file is an output from this
command, fields that record the author identity, the author date,
and the title of the change (taken from the first paragraph of the
commit log message).</p>
</li>
<li>
<p>The second and subsequent paragraphs of the commit log message.</p>
</li>
<li>
<p>The "patch", which is the "diff -p --stat" output (see
<a href="git-diff.html">git-diff(1)</a>) between the commit and its parent.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The log message and the patch is separated by a line with a
three-dash line.</p>
</div>
<div class="paragraph">
<p>There are two ways to specify which commits to operate on.</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>A single commit, &lt;since&gt;, specifies that the commits leading
to the tip of the current branch that are not in the history
that leads to the &lt;since&gt; to be output.</p>
</li>
<li>
<p>Generic &lt;revision range&gt; expression (see "SPECIFYING
REVISIONS" section in <a href="gitrevisions.html">gitrevisions(7)</a>) means the
commits in the specified range.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>The first rule takes precedence in the case of a single &lt;commit&gt;.  To
apply the second rule, i.e., format everything since the beginning of
history up until &lt;commit&gt;, use the <code>--root</code> option: <code>git format-patch
--root &lt;commit&gt;</code>.  If you want to format only &lt;commit&gt; itself, you
can do this with <code>git format-patch -1 &lt;commit&gt;</code>.</p>
</div>
<div class="paragraph">
<p>By default, each output file is numbered sequentially from 1, and uses the
first line of the commit message (massaged for pathname safety) as
the filename. With the <code>--numbered-files</code> option, the output file names
will only be numbers, without the first line of the commit appended.
The names of the output files are printed to standard
output, unless the <code>--stdout</code> option is specified.</p>
</div>
<div class="paragraph">
<p>If <code>-o</code> is specified, output files are created in &lt;dir&gt;.  Otherwise
they are created in the current working directory. The default path
can be set with the <code>format.outputDirectory</code> configuration option.
The <code>-o</code> option takes precedence over <code>format.outputDirectory</code>.
To store patches in the current working directory even when
<code>format.outputDirectory</code> points elsewhere, use <code>-o .</code>. All directory
components will be created.</p>
</div>
<div class="paragraph">
<p>By default, the subject of a single patch is "[PATCH] " followed by
the concatenation of lines from the commit message up to the first blank
line (see the DISCUSSION section of <a href="git-commit.html">git-commit(1)</a>).</p>
</div>
<div class="paragraph">
<p>When multiple patches are output, the subject prefix will instead be
"[PATCH n/m] ".  To force 1/1 to be added for a single patch, use <code>-n</code>.
To omit patch numbers from the subject, use <code>-N</code>.</p>
</div>
<div class="paragraph">
<p>If given <code>--thread</code>, <code>git-format-patch</code> will generate <code>In-Reply-To</code> and
<code>References</code> headers to make the second and subsequent patch mails appear
as replies to the first mail; this also generates a <code>Message-Id</code> header to
reference.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_options">OPTIONS</h2>
<div class="sectionbody">
<div class="dlist">
<dl>
<dt class="hdlist1">-p</dt>
<dt class="hdlist1">--no-stat</dt>
<dd>
<p>Generate plain patches without any diffstats.</p>
</dd>
<dt class="hdlist1">-U&lt;n&gt;</dt>
<dt class="hdlist1">--unified=&lt;n&gt;</dt>
<dd>
<p>Generate diffs with &lt;n&gt; lines of context instead of
the usual three.</p>
</dd>
<dt class="hdlist1">--output=&lt;file&gt;</dt>
<dd>
<p>Output to a specific file instead of stdout.</p>
</dd>
<dt class="hdlist1">--output-indicator-new=&lt;char&gt;</dt>
<dt class="hdlist1">--output-indicator-old=&lt;char&gt;</dt>
<dt class="hdlist1">--output-indicator-context=&lt;char&gt;</dt>
<dd>
<p>Specify the character used to indicate new, old or context
lines in the generated patch. Normally they are <em>+</em>, <em>-</em> and
' ' respectively.</p>
</dd>
<dt class="hdlist1">--indent-heuristic</dt>
<dd>
<p>Enable the heuristic that shifts diff hunk boundaries to make patches
easier to read. This is the default.</p>
</dd>
<dt class="hdlist1">--no-indent-heuristic</dt>
<dd>
<p>Disable the indent heuristic.</p>
</dd>
<dt class="hdlist1">--minimal</dt>
<dd>
<p>Spend extra time to make sure the smallest possible
diff is produced.</p>
</dd>
<dt class="hdlist1">--patience</dt>
<dd>
<p>Generate a diff using the "patience diff" algorithm.</p>
</dd>
<dt class="hdlist1">--histogram</dt>
<dd>
<p>Generate a diff using the "histogram diff" algorithm.</p>
</dd>
<dt class="hdlist1">--anchored=&lt;text&gt;</dt>
<dd>
<p>Generate a diff using the "anchored diff" algorithm.</p>
<div class="paragraph">
<p>This option may be specified more than once.</p>
</div>
<div class="paragraph">
<p>If a line exists in both the source and destination, exists only once,
and starts with this text, this algorithm attempts to prevent it from
appearing as a deletion or addition in the output. It uses the "patience
diff" algorithm internally.</p>
</div>
</dd>
<dt class="hdlist1">--diff-algorithm={patience|minimal|histogram|myers}</dt>
<dd>
<p>Choose a diff algorithm. The variants are as follows:</p>
<div class="openblock">
<div class="content">
<div class="dlist">
<dl>
<dt class="hdlist1"><code>default</code>, <code>myers</code></dt>
<dd>
<p>The basic greedy diff algorithm. Currently, this is the default.</p>
</dd>
<dt class="hdlist1"><code>minimal</code></dt>
<dd>
<p>Spend extra time to make sure the smallest possible diff is
produced.</p>
</dd>
<dt class="hdlist1"><code>patience</code></dt>
<dd>
<p>Use "patience diff" algorithm when generating patches.</p>
</dd>
<dt class="hdlist1"><code>histogram</code></dt>
<dd>
<p>This algorithm extends the patience algorithm to "support
low-occurrence common elements".</p>
</dd>
</dl>
</div>
</div>
</div>
<div class="paragraph">
<p>For instance, if you configured the <code>diff.algorithm</code> variable to a
non-default value and want to use the default one, then you
have to use <code>--diff-algorithm=default</code> option.</p>
</div>
</dd>
<dt class="hdlist1">--stat[=&lt;width&gt;[,&lt;name-width&gt;[,&lt;count&gt;]]]</dt>
<dd>
<p>Generate a diffstat. By default, as much space as necessary
will be used for the filename part, and the rest for the graph
part. Maximum width defaults to terminal width, or 80 columns
if not connected to a terminal, and can be overridden by
<code>&lt;width&gt;</code>. The width of the filename part can be limited by
giving another width <code>&lt;name-width&gt;</code> after a comma. The width
of the graph part can be limited by using
<code>--stat-graph-width=&lt;width&gt;</code> (affects all commands generating
a stat graph) or by setting <code>diff.statGraphWidth=&lt;width&gt;</code>
(does not affect <code>git format-patch</code>).
By giving a third parameter <code>&lt;count&gt;</code>, you can limit the
output to the first <code>&lt;count&gt;</code> lines, followed by <code>...</code> if
there are more.</p>
<div class="paragraph">
<p>These parameters can also be set individually with <code>--stat-width=&lt;width&gt;</code>,
<code>--stat-name-width=&lt;name-width&gt;</code> and <code>--stat-count=&lt;count&gt;</code>.</p>
</div>
</dd>
<dt class="hdlist1">--compact-summary</dt>
<dd>
<p>Output a condensed summary of extended header information such
as file creations or deletions ("new" or "gone", optionally "+l"
if it&#8217;s a symlink) and mode changes ("+x" or "-x" for adding
or removing executable bit respectively) in diffstat. The
information is put between the filename part and the graph
part. Implies <code>--stat</code>.</p>
</dd>
<dt class="hdlist1">--numstat</dt>
<dd>
<p>Similar to <code>--stat</code>, but shows number of added and
deleted lines in decimal notation and pathname without
abbreviation, to make it more machine friendly.  For
binary files, outputs two <code>-</code> instead of saying
<code>0 0</code>.</p>
</dd>
<dt class="hdlist1">--shortstat</dt>
<dd>
<p>Output only the last line of the <code>--stat</code> format containing total
number of modified files, as well as number of added and deleted
lines.</p>
</dd>
<dt class="hdlist1">-X[&lt;param1,param2,&#8230;&#8203;&gt;]</dt>
<dt class="hdlist1">--dirstat[=&lt;param1,param2,&#8230;&#8203;&gt;]</dt>
<dd>
<p>Output the distribution of relative amount of changes for each
sub-directory. The behavior of <code>--dirstat</code> can be customized by
passing it a comma separated list of parameters.
The defaults are controlled by the <code>diff.dirstat</code> configuration
variable (see <a href="git-config.html">git-config(1)</a>).
The following parameters are available:</p>
<div class="openblock">
<div class="content">
<div class="dlist">
<dl>
<dt class="hdlist1"><code>changes</code></dt>
<dd>
<p>Compute the dirstat numbers by counting the lines that have been
removed from the source, or added to the destination. This ignores
the amount of pure code movements within a file.  In other words,
rearranging lines in a file is not counted as much as other changes.
This is the default behavior when no parameter is given.</p>
</dd>
<dt class="hdlist1"><code>lines</code></dt>
<dd>
<p>Compute the dirstat numbers by doing the regular line-based diff
analysis, and summing the removed/added line counts. (For binary
files, count 64-byte chunks instead, since binary files have no
natural concept of lines). This is a more expensive <code>--dirstat</code>
behavior than the <code>changes</code> behavior, but it does count rearranged
lines within a file as much as other changes. The resulting output
is consistent with what you get from the other <code>--*stat</code> options.</p>
</dd>
<dt class="hdlist1"><code>files</code></dt>
<dd>
<p>Compute the dirstat numbers by counting the number of files changed.
Each changed file counts equally in the dirstat analysis. This is
the computationally cheapest <code>--dirstat</code> behavior, since it does
not have to look at the file contents at all.</p>
</dd>
<dt class="hdlist1"><code>cumulative</code></dt>
<dd>
<p>Count changes in a child directory for the parent directory as well.
Note that when using <code>cumulative</code>, the sum of the percentages
reported may exceed 100%. The default (non-cumulative) behavior can
be specified with the <code>noncumulative</code> parameter.</p>
</dd>
<dt class="hdlist1">&lt;limit&gt;</dt>
<dd>
<p>An integer parameter specifies a cut-off percent (3% by default).
Directories contributing less than this percentage of the changes
are not shown in the output.</p>
</dd>
</dl>
</div>
</div>
</div>
<div class="paragraph">
<p>Example: The following will count changed files, while ignoring
directories with less than 10% of the total amount of changed files,
and accumulating child directory counts in the parent directories:
<code>--dirstat=files,10,cumulative</code>.</p>
</div>
</dd>
<dt class="hdlist1">--cumulative</dt>
<dd>
<p>Synonym for --dirstat=cumulative</p>
</dd>
<dt class="hdlist1">--dirstat-by-file[=&lt;param1,param2&gt;&#8230;&#8203;]</dt>
<dd>
<p>Synonym for --dirstat=files,param1,param2&#8230;&#8203;</p>
</dd>
<dt class="hdlist1">--summary</dt>
<dd>
<p>Output a condensed summary of extended header information
such as creations, renames and mode changes.</p>
</dd>
<dt class="hdlist1">--no-renames</dt>
<dd>
<p>Turn off rename detection, even when the configuration
file gives the default to do so.</p>
</dd>
<dt class="hdlist1">--[no-]rename-empty</dt>
<dd>
<p>Whether to use empty blobs as rename source.</p>
</dd>
<dt class="hdlist1">--full-index</dt>
<dd>
<p>Instead of the first handful of characters, show the full
pre- and post-image blob object names on the "index"
line when generating patch format output.</p>
</dd>
<dt class="hdlist1">--binary</dt>
<dd>
<p>In addition to <code>--full-index</code>, output a binary diff that
can be applied with <code>git-apply</code>.</p>
</dd>
<dt class="hdlist1">--abbrev[=&lt;n&gt;]</dt>
<dd>
<p>Instead of showing the full 40-byte hexadecimal object
name in diff-raw format output and diff-tree header
lines, show the shortest prefix that is at least <em>&lt;n&gt;</em>
hexdigits long that uniquely refers the object.
In diff-patch output format, <code>--full-index</code> takes higher
precedence, i.e. if <code>--full-index</code> is specified, full blob
names will be shown regardless of <code>--abbrev</code>.
Non default number of digits can be specified with <code>--abbrev=&lt;n&gt;</code>.</p>
</dd>
<dt class="hdlist1">-B[&lt;n&gt;][/&lt;m&gt;]</dt>
<dt class="hdlist1">--break-rewrites[=[&lt;n&gt;][/&lt;m&gt;]]</dt>
<dd>
<p>Break complete rewrite changes into pairs of delete and
create. This serves two purposes:</p>
<div class="paragraph">
<p>It affects the way a change that amounts to a total rewrite of a file
not as a series of deletion and insertion mixed together with a very
few lines that happen to match textually as the context, but as a
single deletion of everything old followed by a single insertion of
everything new, and the number <code>m</code> controls this aspect of the -B
option (defaults to 60%). <code>-B/70%</code> specifies that less than 30% of the
original should remain in the result for Git to consider it a total
rewrite (i.e. otherwise the resulting patch will be a series of
deletion and insertion mixed together with context lines).</p>
</div>
<div class="paragraph">
<p>When used with -M, a totally-rewritten file is also considered as the
source of a rename (usually -M only considers a file that disappeared
as the source of a rename), and the number <code>n</code> controls this aspect of
the -B option (defaults to 50%). <code>-B20%</code> specifies that a change with
addition and deletion compared to 20% or more of the file&#8217;s size are
eligible for being picked up as a possible source of a rename to
another file.</p>
</div>
</dd>
<dt class="hdlist1">-M[&lt;n&gt;]</dt>
<dt class="hdlist1">--find-renames[=&lt;n&gt;]</dt>
<dd>
<p>Detect renames.
If <code>n</code> is specified, it is a threshold on the similarity
index (i.e. amount of addition/deletions compared to the
file&#8217;s size). For example, <code>-M90%</code> means Git should consider a
delete/add pair to be a rename if more than 90% of the file
hasn&#8217;t changed.  Without a <code>%</code> sign, the number is to be read as
a fraction, with a decimal point before it.  I.e., <code>-M5</code> becomes
0.5, and is thus the same as <code>-M50%</code>.  Similarly, <code>-M05</code> is
the same as <code>-M5%</code>.  To limit detection to exact renames, use
<code>-M100%</code>.  The default similarity index is 50%.</p>
</dd>
<dt class="hdlist1">-C[&lt;n&gt;]</dt>
<dt class="hdlist1">--find-copies[=&lt;n&gt;]</dt>
<dd>
<p>Detect copies as well as renames.  See also <code>--find-copies-harder</code>.
If <code>n</code> is specified, it has the same meaning as for <code>-M&lt;n&gt;</code>.</p>
</dd>
<dt class="hdlist1">--find-copies-harder</dt>
<dd>
<p>For performance reasons, by default, <code>-C</code> option finds copies only
if the original file of the copy was modified in the same
changeset.  This flag makes the command
inspect unmodified files as candidates for the source of
copy.  This is a very expensive operation for large
projects, so use it with caution.  Giving more than one
<code>-C</code> option has the same effect.</p>
</dd>
<dt class="hdlist1">-D</dt>
<dt class="hdlist1">--irreversible-delete</dt>
<dd>
<p>Omit the preimage for deletes, i.e. print only the header but not
the diff between the preimage and <code>/dev/null</code>. The resulting patch
is not meant to be applied with <code>patch</code> or <code>git apply</code>; this is
solely for people who want to just concentrate on reviewing the
text after the change. In addition, the output obviously lacks
enough information to apply such a patch in reverse, even manually,
hence the name of the option.</p>
<div class="paragraph">
<p>When used together with <code>-B</code>, omit also the preimage in the deletion part
of a delete/create pair.</p>
</div>
</dd>
<dt class="hdlist1">-l&lt;num&gt;</dt>
<dd>
<p>The <code>-M</code> and <code>-C</code> options involve some preliminary steps that
can detect subsets of renames/copies cheaply, followed by an
exhaustive fallback portion that compares all remaining
unpaired destinations to all relevant sources.  (For renames,
only remaining unpaired sources are relevant; for copies, all
original sources are relevant.)  For N sources and
destinations, this exhaustive check is O(N^2).  This option
prevents the exhaustive portion of rename/copy detection from
running if the number of source/destination files involved
exceeds the specified number.  Defaults to diff.renameLimit.
Note that a value of 0 is treated as unlimited.</p>
</dd>
<dt class="hdlist1">-O&lt;orderfile&gt;</dt>
<dd>
<p>Control the order in which files appear in the output.
This overrides the <code>diff.orderFile</code> configuration variable
(see <a href="git-config.html">git-config(1)</a>).  To cancel <code>diff.orderFile</code>,
use <code>-O/dev/null</code>.</p>
<div class="paragraph">
<p>The output order is determined by the order of glob patterns in
&lt;orderfile&gt;.
All files with pathnames that match the first pattern are output
first, all files with pathnames that match the second pattern (but not
the first) are output next, and so on.
All files with pathnames that do not match any pattern are output
last, as if there was an implicit match-all pattern at the end of the
file.
If multiple pathnames have the same rank (they match the same pattern
but no earlier patterns), their output order relative to each other is
the normal order.</p>
</div>
<div class="paragraph">
<p>&lt;orderfile&gt; is parsed as follows:</p>
</div>
<div class="openblock">
<div class="content">
<div class="ulist">
<ul>
<li>
<p>Blank lines are ignored, so they can be used as separators for
readability.</p>
</li>
<li>
<p>Lines starting with a hash ("<code>#</code>") are ignored, so they can be used
for comments.  Add a backslash ("<code>\</code>") to the beginning of the
pattern if it starts with a hash.</p>
</li>
<li>
<p>Each other line contains a single pattern.</p>
</li>
</ul>
</div>
</div>
</div>
<div class="paragraph">
<p>Patterns have the same syntax and semantics as patterns used for
fnmatch(3) without the FNM_PATHNAME flag, except a pathname also
matches a pattern if removing any number of the final pathname
components matches the pattern.  For example, the pattern "<code>foo*bar</code>"
matches "<code>fooasdfbar</code>" and "<code>foo/bar/baz/asdf</code>" but not "<code>foobarx</code>".</p>
</div>
</dd>
<dt class="hdlist1">--skip-to=&lt;file&gt;</dt>
<dt class="hdlist1">--rotate-to=&lt;file&gt;</dt>
<dd>
<p>Discard the files before the named &lt;file&gt; from the output
(i.e. <em>skip to</em>), or move them to the end of the output
(i.e. <em>rotate to</em>).  These were invented primarily for use
of the <code>git difftool</code> command, and may not be very useful
otherwise.</p>
</dd>
<dt class="hdlist1">--relative[=&lt;path&gt;]</dt>
<dt class="hdlist1">--no-relative</dt>
<dd>
<p>When run from a subdirectory of the project, it can be
told to exclude changes outside the directory and show
pathnames relative to it with this option.  When you are
not in a subdirectory (e.g. in a bare repository), you
can name which subdirectory to make the output relative
to by giving a &lt;path&gt; as an argument.
<code>--no-relative</code> can be used to countermand both <code>diff.relative</code> config
option and previous <code>--relative</code>.</p>
</dd>
<dt class="hdlist1">-a</dt>
<dt class="hdlist1">--text</dt>
<dd>
<p>Treat all files as text.</p>
</dd>
<dt class="hdlist1">--ignore-cr-at-eol</dt>
<dd>
<p>Ignore carriage-return at the end of line when doing a comparison.</p>
</dd>
<dt class="hdlist1">--ignore-space-at-eol</dt>
<dd>
<p>Ignore changes in whitespace at EOL.</p>
</dd>
<dt class="hdlist1">-b</dt>
<dt class="hdlist1">--ignore-space-change</dt>
<dd>
<p>Ignore changes in amount of whitespace.  This ignores whitespace
at line end, and considers all other sequences of one or
more whitespace characters to be equivalent.</p>
</dd>
<dt class="hdlist1">-w</dt>
<dt class="hdlist1">--ignore-all-space</dt>
<dd>
<p>Ignore whitespace when comparing lines.  This ignores
differences even if one line has whitespace where the other
line has none.</p>
</dd>
<dt class="hdlist1">--ignore-blank-lines</dt>
<dd>
<p>Ignore changes whose lines are all blank.</p>
</dd>
<dt class="hdlist1">-I&lt;regex&gt;</dt>
<dt class="hdlist1">--ignore-matching-lines=&lt;regex&gt;</dt>
<dd>
<p>Ignore changes whose all lines match &lt;regex&gt;.  This option may
be specified more than once.</p>
</dd>
<dt class="hdlist1">--inter-hunk-context=&lt;lines&gt;</dt>
<dd>
<p>Show the context between diff hunks, up to the specified number
of lines, thereby fusing hunks that are close to each other.
Defaults to <code>diff.interHunkContext</code> or 0 if the config option
is unset.</p>
</dd>
<dt class="hdlist1">-W</dt>
<dt class="hdlist1">--function-context</dt>
<dd>
<p>Show whole function as context lines for each change.
The function names are determined in the same way as
<code>git diff</code> works out patch hunk headers (see <em>Defining a
custom hunk-header</em> in <a href="gitattributes.html">gitattributes(5)</a>).</p>
</dd>
<dt class="hdlist1">--ext-diff</dt>
<dd>
<p>Allow an external diff helper to be executed. If you set an
external diff driver with <a href="gitattributes.html">gitattributes(5)</a>, you need
to use this option with <a href="git-log.html">git-log(1)</a> and friends.</p>
</dd>
<dt class="hdlist1">--no-ext-diff</dt>
<dd>
<p>Disallow external diff drivers.</p>
</dd>
<dt class="hdlist1">--textconv</dt>
<dt class="hdlist1">--no-textconv</dt>
<dd>
<p>Allow (or disallow) external text conversion filters to be run
when comparing binary files. See <a href="gitattributes.html">gitattributes(5)</a> for
details. Because textconv filters are typically a one-way
conversion, the resulting diff is suitable for human
consumption, but cannot be applied. For this reason, textconv
filters are enabled by default only for <a href="git-diff.html">git-diff(1)</a> and
<a href="git-log.html">git-log(1)</a>, but not for <a href="git-format-patch.html">git-format-patch(1)</a> or
diff plumbing commands.</p>
</dd>
<dt class="hdlist1">--ignore-submodules[=&lt;when&gt;]</dt>
<dd>
<p>Ignore changes to submodules in the diff generation. &lt;when&gt; can be
either "none", "untracked", "dirty" or "all", which is the default.
Using "none" will consider the submodule modified when it either contains
untracked or modified files or its HEAD differs from the commit recorded
in the superproject and can be used to override any settings of the
<em>ignore</em> option in <a href="git-config.html">git-config(1)</a> or <a href="gitmodules.html">gitmodules(5)</a>. When
"untracked" is used submodules are not considered dirty when they only
contain untracked content (but they are still scanned for modified
content). Using "dirty" ignores all changes to the work tree of submodules,
only changes to the commits stored in the superproject are shown (this was
the behavior until 1.7.0). Using "all" hides all changes to submodules.</p>
</dd>
<dt class="hdlist1">--src-prefix=&lt;prefix&gt;</dt>
<dd>
<p>Show the given source prefix instead of "a/".</p>
</dd>
<dt class="hdlist1">--dst-prefix=&lt;prefix&gt;</dt>
<dd>
<p>Show the given destination prefix instead of "b/".</p>
</dd>
<dt class="hdlist1">--no-prefix</dt>
<dd>
<p>Do not show any source or destination prefix.</p>
</dd>
<dt class="hdlist1">--line-prefix=&lt;prefix&gt;</dt>
<dd>
<p>Prepend an additional prefix to every line of output.</p>
</dd>
<dt class="hdlist1">--ita-invisible-in-index</dt>
<dd>
<p>By default entries added by "git add -N" appear as an existing
empty file in "git diff" and a new file in "git diff --cached".
This option makes the entry appear as a new file in "git diff"
and non-existent in "git diff --cached". This option could be
reverted with <code>--ita-visible-in-index</code>. Both options are
experimental and could be removed in future.</p>
</dd>
</dl>
</div>
<div class="paragraph">
<p>For more detailed explanation on these common options, see also
<a href="gitdiffcore.html">gitdiffcore(7)</a>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">-&lt;n&gt;</dt>
<dd>
<p>Prepare patches from the topmost &lt;n&gt; commits.</p>
</dd>
<dt class="hdlist1">-o &lt;dir&gt;</dt>
<dt class="hdlist1">--output-directory &lt;dir&gt;</dt>
<dd>
<p>Use &lt;dir&gt; to store the resulting files, instead of the
current working directory.</p>
</dd>
<dt class="hdlist1">-n</dt>
<dt class="hdlist1">--numbered</dt>
<dd>
<p>Name output in <em>[PATCH n/m]</em> format, even with a single patch.</p>
</dd>
<dt class="hdlist1">-N</dt>
<dt class="hdlist1">--no-numbered</dt>
<dd>
<p>Name output in <em>[PATCH]</em> format.</p>
</dd>
<dt class="hdlist1">--start-number &lt;n&gt;</dt>
<dd>
<p>Start numbering the patches at &lt;n&gt; instead of 1.</p>
</dd>
<dt class="hdlist1">--numbered-files</dt>
<dd>
<p>Output file names will be a simple number sequence
without the default first line of the commit appended.</p>
</dd>
<dt class="hdlist1">-k</dt>
<dt class="hdlist1">--keep-subject</dt>
<dd>
<p>Do not strip/add <em>[PATCH]</em> from the first line of the
commit log message.</p>
</dd>
<dt class="hdlist1">-s</dt>
<dt class="hdlist1">--signoff</dt>
<dd>
<p>Add a <code>Signed-off-by</code> trailer to the commit message, using
the committer identity of yourself.
See the signoff option in <a href="git-commit.html">git-commit(1)</a> for more information.</p>
</dd>
<dt class="hdlist1">--stdout</dt>
<dd>
<p>Print all commits to the standard output in mbox format,
instead of creating a file for each one.</p>
</dd>
<dt class="hdlist1">--attach[=&lt;boundary&gt;]</dt>
<dd>
<p>Create multipart/mixed attachment, the first part of
which is the commit message and the patch itself in the
second part, with <code>Content-Disposition: attachment</code>.</p>
</dd>
<dt class="hdlist1">--no-attach</dt>
<dd>
<p>Disable the creation of an attachment, overriding the
configuration setting.</p>
</dd>
<dt class="hdlist1">--inline[=&lt;boundary&gt;]</dt>
<dd>
<p>Create multipart/mixed attachment, the first part of
which is the commit message and the patch itself in the
second part, with <code>Content-Disposition: inline</code>.</p>
</dd>
<dt class="hdlist1">--thread[=&lt;style&gt;]</dt>
<dt class="hdlist1">--no-thread</dt>
<dd>
<p>Controls addition of <code>In-Reply-To</code> and <code>References</code> headers to
make the second and subsequent mails appear as replies to the
first.  Also controls generation of the <code>Message-Id</code> header to
reference.</p>
<div class="paragraph">
<p>The optional &lt;style&gt; argument can be either <code>shallow</code> or <code>deep</code>.
<em>shallow</em> threading makes every mail a reply to the head of the
series, where the head is chosen from the cover letter, the
<code>--in-reply-to</code>, and the first patch mail, in this order.  <em>deep</em>
threading makes every mail a reply to the previous one.</p>
</div>
<div class="paragraph">
<p>The default is <code>--no-thread</code>, unless the <code>format.thread</code> configuration
is set.  If <code>--thread</code> is specified without a style, it defaults to the
style specified by <code>format.thread</code> if any, or else <code>shallow</code>.</p>
</div>
<div class="paragraph">
<p>Beware that the default for <em>git send-email</em> is to thread emails
itself.  If you want <code>git format-patch</code> to take care of threading, you
will want to ensure that threading is disabled for <code>git send-email</code>.</p>
</div>
</dd>
<dt class="hdlist1">--in-reply-to=&lt;message id&gt;</dt>
<dd>
<p>Make the first mail (or all the mails with <code>--no-thread</code>) appear as a
reply to the given &lt;message id&gt;, which avoids breaking threads to
provide a new patch series.</p>
</dd>
<dt class="hdlist1">--ignore-if-in-upstream</dt>
<dd>
<p>Do not include a patch that matches a commit in
&lt;until&gt;..&lt;since&gt;.  This will examine all patches reachable
from &lt;since&gt; but not from &lt;until&gt; and compare them with the
patches being generated, and any patch that matches is
ignored.</p>
</dd>
<dt class="hdlist1">--always</dt>
<dd>
<p>Include patches for commits that do not introduce any change,
which are omitted by default.</p>
</dd>
<dt class="hdlist1">--cover-from-description=&lt;mode&gt;</dt>
<dd>
<p>Controls which parts of the cover letter will be automatically
populated using the branch&#8217;s description.</p>
<div class="paragraph">
<p>If <code>&lt;mode&gt;</code> is <code>message</code> or <code>default</code>, the cover letter subject will be
populated with placeholder text. The body of the cover letter will be
populated with the branch&#8217;s description. This is the default mode when
no configuration nor command line option is specified.</p>
</div>
<div class="paragraph">
<p>If <code>&lt;mode&gt;</code> is <code>subject</code>, the first paragraph of the branch description will
populate the cover letter subject. The remainder of the description will
populate the body of the cover letter.</p>
</div>
<div class="paragraph">
<p>If <code>&lt;mode&gt;</code> is <code>auto</code>, if the first paragraph of the branch description
is greater than 100 bytes, then the mode will be <code>message</code>, otherwise
<code>subject</code> will be used.</p>
</div>
<div class="paragraph">
<p>If <code>&lt;mode&gt;</code> is <code>none</code>, both the cover letter subject and body will be
populated with placeholder text.</p>
</div>
</dd>
<dt class="hdlist1">--subject-prefix=&lt;subject prefix&gt;</dt>
<dd>
<p>Instead of the standard <em>[PATCH]</em> prefix in the subject
line, instead use <em>[&lt;subject prefix&gt;]</em>. This
allows for useful naming of a patch series, and can be
combined with the <code>--numbered</code> option.</p>
</dd>
<dt class="hdlist1">--filename-max-length=&lt;n&gt;</dt>
<dd>
<p>Instead of the standard 64 bytes, chomp the generated output
filenames at around <em>&lt;n&gt;</em> bytes (too short a value will be
silently raised to a reasonable length).  Defaults to the
value of the <code>format.filenameMaxLength</code> configuration
variable, or 64 if unconfigured.</p>
</dd>
<dt class="hdlist1">--rfc</dt>
<dd>
<p>Alias for <code>--subject-prefix="RFC PATCH"</code>. RFC means "Request For
Comments"; use this when sending an experimental patch for
discussion rather than application.</p>
</dd>
<dt class="hdlist1">-v &lt;n&gt;</dt>
<dt class="hdlist1">--reroll-count=&lt;n&gt;</dt>
<dd>
<p>Mark the series as the &lt;n&gt;-th iteration of the topic. The
output filenames have <code>v&lt;n&gt;</code> prepended to them, and the
subject prefix ("PATCH" by default, but configurable via the
<code>--subject-prefix</code> option) has ` v&lt;n&gt;` appended to it.  E.g.
<code>--reroll-count=4</code> may produce <code>v4-0001-add-makefile.patch</code>
file that has "Subject: [PATCH v4 1/20] Add makefile" in it.
<code>&lt;n&gt;</code> does not have to be an integer (e.g. "--reroll-count=4.4",
or "--reroll-count=4rev2" are allowed), but the downside of
using such a reroll-count is that the range-diff/interdiff
with the previous version does not state exactly which
version the new interation is compared against.</p>
</dd>
<dt class="hdlist1">--to=&lt;email&gt;</dt>
<dd>
<p>Add a <code>To:</code> header to the email headers. This is in addition
to any configured headers, and may be used multiple times.
The negated form <code>--no-to</code> discards all <code>To:</code> headers added so
far (from config or command line).</p>
</dd>
<dt class="hdlist1">--cc=&lt;email&gt;</dt>
<dd>
<p>Add a <code>Cc:</code> header to the email headers. This is in addition
to any configured headers, and may be used multiple times.
The negated form <code>--no-cc</code> discards all <code>Cc:</code> headers added so
far (from config or command line).</p>
</dd>
<dt class="hdlist1">--from</dt>
<dt class="hdlist1">--from=&lt;ident&gt;</dt>
<dd>
<p>Use <code>ident</code> in the <code>From:</code> header of each commit email. If the
author ident of the commit is not textually identical to the
provided <code>ident</code>, place a <code>From:</code> header in the body of the
message with the original author. If no <code>ident</code> is given, use
the committer ident.</p>
<div class="paragraph">
<p>Note that this option is only useful if you are actually sending the
emails and want to identify yourself as the sender, but retain the
original author (and <code>git am</code> will correctly pick up the in-body
header). Note also that <code>git send-email</code> already handles this
transformation for you, and this option should not be used if you are
feeding the result to <code>git send-email</code>.</p>
</div>
</dd>
<dt class="hdlist1">--add-header=&lt;header&gt;</dt>
<dd>
<p>Add an arbitrary header to the email headers.  This is in addition
to any configured headers, and may be used multiple times.
For example, <code>--add-header="Organization: git-foo"</code>.
The negated form <code>--no-add-header</code> discards <strong>all</strong> (<code>To:</code>,
<code>Cc:</code>, and custom) headers added so far from config or command
line.</p>
</dd>
<dt class="hdlist1">--[no-]cover-letter</dt>
<dd>
<p>In addition to the patches, generate a cover letter file
containing the branch description, shortlog and the overall diffstat.  You can
fill in a description in the file before sending it out.</p>
</dd>
<dt class="hdlist1">--encode-email-headers</dt>
<dt class="hdlist1">--no-encode-email-headers</dt>
<dd>
<p>Encode email headers that have non-ASCII characters with
"Q-encoding" (described in RFC 2047), instead of outputting the
headers verbatim. Defaults to the value of the
<code>format.encodeEmailHeaders</code> configuration variable.</p>
</dd>
<dt class="hdlist1">--interdiff=&lt;previous&gt;</dt>
<dd>
<p>As a reviewer aid, insert an interdiff into the cover letter,
or as commentary of the lone patch of a 1-patch series, showing
the differences between the previous version of the patch series and
the series currently being formatted. <code>previous</code> is a single revision
naming the tip of the previous series which shares a common base with
the series being formatted (for example <code>git format-patch
--cover-letter --interdiff=feature/v1 -3 feature/v2</code>).</p>
</dd>
<dt class="hdlist1">--range-diff=&lt;previous&gt;</dt>
<dd>
<p>As a reviewer aid, insert a range-diff (see <a href="git-range-diff.html">git-range-diff(1)</a>)
into the cover letter, or as commentary of the lone patch of a
1-patch series, showing the differences between the previous
version of the patch series and the series currently being formatted.
<code>previous</code> can be a single revision naming the tip of the previous
series if it shares a common base with the series being formatted (for
example <code>git format-patch --cover-letter --range-diff=feature/v1 -3
feature/v2</code>), or a revision range if the two versions of the series are
disjoint (for example <code>git format-patch --cover-letter
--range-diff=feature/v1~3..feature/v1 -3 feature/v2</code>).</p>
<div class="paragraph">
<p>Note that diff options passed to the command affect how the primary
product of <code>format-patch</code> is generated, and they are not passed to
the underlying <code>range-diff</code> machinery used to generate the cover-letter
material (this may change in the future).</p>
</div>
</dd>
<dt class="hdlist1">--creation-factor=&lt;percent&gt;</dt>
<dd>
<p>Used with <code>--range-diff</code>, tweak the heuristic which matches up commits
between the previous and current series of patches by adjusting the
creation/deletion cost fudge factor. See <a href="git-range-diff.html">git-range-diff(1)</a>)
for details.</p>
</dd>
<dt class="hdlist1">--notes[=&lt;ref&gt;]</dt>
<dt class="hdlist1">--no-notes</dt>
<dd>
<p>Append the notes (see <a href="git-notes.html">git-notes(1)</a>) for the commit
after the three-dash line.</p>
<div class="paragraph">
<p>The expected use case of this is to write supporting explanation for
the commit that does not belong to the commit log message proper,
and include it with the patch submission. While one can simply write
these explanations after <code>format-patch</code> has run but before sending,
keeping them as Git notes allows them to be maintained between versions
of the patch series (but see the discussion of the <code>notes.rewrite</code>
configuration options in <a href="git-notes.html">git-notes(1)</a> to use this workflow).</p>
</div>
<div class="paragraph">
<p>The default is <code>--no-notes</code>, unless the <code>format.notes</code> configuration is
set.</p>
</div>
</dd>
<dt class="hdlist1">--[no-]signature=&lt;signature&gt;</dt>
<dd>
<p>Add a signature to each message produced. Per RFC 3676 the signature
is separated from the body by a line with '-- ' on it. If the
signature option is omitted the signature defaults to the Git version
number.</p>
</dd>
<dt class="hdlist1">--signature-file=&lt;file&gt;</dt>
<dd>
<p>Works just like --signature except the signature is read from a file.</p>
</dd>
<dt class="hdlist1">--suffix=.&lt;sfx&gt;</dt>
<dd>
<p>Instead of using <code>.patch</code> as the suffix for generated
filenames, use specified suffix.  A common alternative is
<code>--suffix=.txt</code>.  Leaving this empty will remove the <code>.patch</code>
suffix.</p>
<div class="paragraph">
<p>Note that the leading character does not have to be a dot; for example,
you can use <code>--suffix=-patch</code> to get <code>0001-description-of-my-change-patch</code>.</p>
</div>
</dd>
<dt class="hdlist1">-q</dt>
<dt class="hdlist1">--quiet</dt>
<dd>
<p>Do not print the names of the generated files to standard output.</p>
</dd>
<dt class="hdlist1">--no-binary</dt>
<dd>
<p>Do not output contents of changes in binary files, instead
display a notice that those files changed.  Patches generated
using this option cannot be applied properly, but they are
still useful for code review.</p>
</dd>
<dt class="hdlist1">--zero-commit</dt>
<dd>
<p>Output an all-zero hash in each patch&#8217;s From header instead
of the hash of the commit.</p>
</dd>
<dt class="hdlist1">--[no-]base[=&lt;commit&gt;]</dt>
<dd>
<p>Record the base tree information to identify the state the
patch series applies to.  See the BASE TREE INFORMATION section
below for details. If &lt;commit&gt; is "auto", a base commit is
automatically chosen. The <code>--no-base</code> option overrides a
<code>format.useAutoBase</code> configuration.</p>
</dd>
<dt class="hdlist1">--root</dt>
<dd>
<p>Treat the revision argument as a &lt;revision range&gt;, even if it
is just a single commit (that would normally be treated as a
&lt;since&gt;).  Note that root commits included in the specified
range are always formatted as creation patches, independently
of this flag.</p>
</dd>
<dt class="hdlist1">--progress</dt>
<dd>
<p>Show progress reports on stderr as patches are generated.</p>
</dd>
</dl>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_configuration">CONFIGURATION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can specify extra mail header lines to be added to each message,
defaults for the subject prefix and file suffix, number patches when
outputting more than one patch, add "To:" or "Cc:" headers, configure
attachments, change the patch output directory, and sign off patches
with configuration variables.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>[format]
        headers = "Organization: git-foo\n"
        subjectPrefix = CHANGE
        suffix = .txt
        numbered = auto
        to = &lt;email&gt;
        cc = &lt;email&gt;
        attach [ = mime-boundary-string ]
        signOff = true
        outputDirectory = &lt;directory&gt;
        coverLetter = auto
        coverFromDescription = auto</pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_discussion">DISCUSSION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The patch produced by <em>git format-patch</em> is in UNIX mailbox format,
with a fixed "magic" time stamp to indicate that the file is output
from format-patch rather than a real mailbox, like so:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>From 8f72bad1baf19a53459661343e21d6491c3908d3 Mon Sep 17 00:00:00 2001
From: Tony Luck &lt;<EMAIL>&gt;
Date: Tue, 13 Jul 2010 11:42:54 -0700
Subject: [PATCH] =?UTF-8?q?[IA64]=20Put=20ia64=20config=20files=20on=20the=20?=
 =?UTF-8?q?Uwe=20Kleine-K=C3=B6nig=20diet?=
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

arch/arm config files were slimmed down using a python script
(See commit c2330e286f68f1c408b4aa6515ba49d57f05beae comment)

Do the same for ia64 so we can have sleek &amp; trim looking
...</pre>
</div>
</div>
<div class="paragraph">
<p>Typically it will be placed in a MUA&#8217;s drafts folder, edited to add
timely commentary that should not go in the changelog after the three
dashes, and then sent as a message whose body, in our example, starts
with "arch/arm config files were&#8230;&#8203;".  On the receiving end, readers
can save interesting patches in a UNIX mailbox and apply them with
<a href="git-am.html">git-am(1)</a>.</p>
</div>
<div class="paragraph">
<p>When a patch is part of an ongoing discussion, the patch generated by
<em>git format-patch</em> can be tweaked to take advantage of the <em>git am
--scissors</em> feature.  After your response to the discussion comes a
line that consists solely of "<code>-- &gt;8 --</code>" (scissors and perforation),
followed by the patch with unnecessary header fields removed:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>...
&gt; So we should do such-and-such.

Makes sense to me.  How about this patch?

-- &gt;8 --
Subject: [IA64] Put ia64 config files on the Uwe Kleine-König diet

arch/arm config files were slimmed down using a python script
...</pre>
</div>
</div>
<div class="paragraph">
<p>When sending a patch this way, most often you are sending your own
patch, so in addition to the "<code>From $SHA1 $magic_timestamp</code>" marker you
should omit <code>From:</code> and <code>Date:</code> lines from the patch file.  The patch
title is likely to be different from the subject of the discussion the
patch is in response to, so it is likely that you would want to keep
the Subject: line, like the example above.</p>
</div>
<div class="sect2">
<h3 id="_checking_for_patch_corruption">Checking for patch corruption</h3>
<div class="paragraph">
<p>Many mailers if not set up properly will corrupt whitespace.  Here are
two common types of corruption:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Empty context lines that do not have <em>any</em> whitespace.</p>
</li>
<li>
<p>Non-empty context lines that have one extra whitespace at the
beginning.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>One way to test if your MUA is set up correctly is:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Send the patch to yourself, exactly the way you would, except
with To: and Cc: lines that do not contain the list and
maintainer address.</p>
</li>
<li>
<p>Save that patch to a file in UNIX mailbox format.  Call it a.patch,
say.</p>
</li>
<li>
<p>Apply it:</p>
<div class="literalblock">
<div class="content">
<pre>$ git fetch &lt;project&gt; master:test-apply
$ git switch test-apply
$ git restore --source=HEAD --staged --worktree :/
$ git am a.patch</pre>
</div>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>If it does not apply correctly, there can be various reasons.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>The patch itself does not apply cleanly.  That is <em>bad</em> but
does not have much to do with your MUA.  You might want to rebase
the patch with <a href="git-rebase.html">git-rebase(1)</a> before regenerating it in
this case.</p>
</li>
<li>
<p>The MUA corrupted your patch; "am" would complain that
the patch does not apply.  Look in the .git/rebase-apply/ subdirectory and
see what <em>patch</em> file contains and check for the common
corruption patterns mentioned above.</p>
</li>
<li>
<p>While at it, check the <em>info</em> and <em>final-commit</em> files as well.
If what is in <em>final-commit</em> is not exactly what you would want to
see in the commit log message, it is very likely that the
receiver would end up hand editing the log message when applying
your patch.  Things like "Hi, this is my first patch.\n" in the
patch e-mail should come after the three-dash line that signals
the end of the commit message.</p>
</li>
</ul>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_mua_specific_hints">MUA-SPECIFIC HINTS</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Here are some hints on how to successfully submit patches inline using
various mailers.</p>
</div>
<div class="sect2">
<h3 id="_gmail">GMail</h3>
<div class="paragraph">
<p>GMail does not have any way to turn off line wrapping in the web
interface, so it will mangle any emails that you send.  You can however
use "git send-email" and send your patches through the GMail SMTP server, or
use any IMAP email client to connect to the google IMAP server and forward
the emails through that.</p>
</div>
<div class="paragraph">
<p>For hints on using <em>git send-email</em> to send your patches through the
GMail SMTP server, see the EXAMPLE section of <a href="git-send-email.html">git-send-email(1)</a>.</p>
</div>
<div class="paragraph">
<p>For hints on submission using the IMAP interface, see the EXAMPLE
section of <a href="git-imap-send.html">git-imap-send(1)</a>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_thunderbird">Thunderbird</h3>
<div class="paragraph">
<p>By default, Thunderbird will both wrap emails as well as flag
them as being <em>format=flowed</em>, both of which will make the
resulting email unusable by Git.</p>
</div>
<div class="paragraph">
<p>There are three different approaches: use an add-on to turn off line wraps,
configure Thunderbird to not mangle patches, or use
an external editor to keep Thunderbird from mangling the patches.</p>
</div>
<div class="sect3">
<h4 id="_approach_1_add_on">Approach #1 (add-on)</h4>
<div class="paragraph">
<p>Install the Toggle Word Wrap add-on that is available from
<a href="https://addons.mozilla.org/thunderbird/addon/toggle-word-wrap/" class="bare">https://addons.mozilla.org/thunderbird/addon/toggle-word-wrap/</a>
It adds a menu entry "Enable Word Wrap" in the composer&#8217;s "Options" menu
that you can tick off. Now you can compose the message as you otherwise do
(cut + paste, <em>git format-patch</em> | <em>git imap-send</em>, etc), but you have to
insert line breaks manually in any text that you type.</p>
</div>
</div>
<div class="sect3">
<h4 id="_approach_2_configuration">Approach #2 (configuration)</h4>
<div class="paragraph">
<p>Three steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Configure your mail server composition as plain text:
Edit&#8230;&#8203;Account Settings&#8230;&#8203;Composition &amp; Addressing,
uncheck "Compose Messages in HTML".</p>
</li>
<li>
<p>Configure your general composition window to not wrap.</p>
<div class="paragraph">
<p>In Thunderbird 2:
Edit..Preferences..Composition, wrap plain text messages at 0</p>
</div>
<div class="paragraph">
<p>In Thunderbird 3:
Edit..Preferences..Advanced..Config Editor.  Search for
"mail.wrap_long_lines".
Toggle it to make sure it is set to <code>false</code>. Also, search for
"mailnews.wraplength" and set the value to 0.</p>
</div>
</li>
<li>
<p>Disable the use of format=flowed:
Edit..Preferences..Advanced..Config Editor.  Search for
"mailnews.send_plaintext_flowed".
Toggle it to make sure it is set to <code>false</code>.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>After that is done, you should be able to compose email as you
otherwise would (cut + paste, <em>git format-patch</em> | <em>git imap-send</em>, etc),
and the patches will not be mangled.</p>
</div>
</div>
<div class="sect3">
<h4 id="_approach_3_external_editor">Approach #3 (external editor)</h4>
<div class="paragraph">
<p>The following Thunderbird extensions are needed:
AboutConfig from <a href="http://aboutconfig.mozdev.org/" class="bare">http://aboutconfig.mozdev.org/</a> and
External Editor from <a href="http://globs.org/articles.php?lng=en&amp;pg=8" class="bare">http://globs.org/articles.php?lng=en&amp;pg=8</a></p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Prepare the patch as a text file using your method of choice.</p>
</li>
<li>
<p>Before opening a compose window, use Edit&#8594;Account Settings to
uncheck the "Compose messages in HTML format" setting in the
"Composition &amp; Addressing" panel of the account to be used to
send the patch.</p>
</li>
<li>
<p>In the main Thunderbird window, <em>before</em> you open the compose
window for the patch, use Tools&#8594;about:config to set the
following to the indicated values:</p>
<div class="listingblock">
<div class="content">
<pre>        mailnews.send_plaintext_flowed  =&gt; false
        mailnews.wraplength             =&gt; 0</pre>
</div>
</div>
</li>
<li>
<p>Open a compose window and click the external editor icon.</p>
</li>
<li>
<p>In the external editor window, read in the patch file and exit
the editor normally.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>Side note: it may be possible to do step 2 with
about:config and the following settings but no one&#8217;s tried yet.</p>
</div>
<div class="listingblock">
<div class="content">
<pre>        mail.html_compose                       =&gt; false
        mail.identity.default.compose_html      =&gt; false
        mail.identity.id?.compose_html          =&gt; false</pre>
</div>
</div>
<div class="paragraph">
<p>There is a script in contrib/thunderbird-patch-inline which can help
you include patches with Thunderbird in an easy way. To use it, do the
steps above and then use the script as the external editor.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_kmail">KMail</h3>
<div class="paragraph">
<p>This should help you to submit patches inline using KMail.</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Prepare the patch as a text file.</p>
</li>
<li>
<p>Click on New Mail.</p>
</li>
<li>
<p>Go under "Options" in the Composer window and be sure that
"Word wrap" is not set.</p>
</li>
<li>
<p>Use Message &#8594; Insert file&#8230;&#8203; and insert the patch.</p>
</li>
<li>
<p>Back in the compose window: add whatever other text you wish to the
message, complete the addressing and subject fields, and press send.</p>
</li>
</ol>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_base_tree_information">BASE TREE INFORMATION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The base tree information block is used for maintainers or third party
testers to know the exact state the patch series applies to. It consists
of the <em>base commit</em>, which is a well-known commit that is part of the
stable part of the project history everybody else works off of, and zero
or more <em>prerequisite patches</em>, which are well-known patches in flight
that is not yet part of the <em>base commit</em> that need to be applied on top
of <em>base commit</em> in topological order before the patches can be applied.</p>
</div>
<div class="paragraph">
<p>The <em>base commit</em> is shown as "base-commit: " followed by the 40-hex of
the commit object name.  A <em>prerequisite patch</em> is shown as
"prerequisite-patch-id: " followed by the 40-hex <em>patch id</em>, which can
be obtained by passing the patch through the <code>git patch-id --stable</code>
command.</p>
</div>
<div class="paragraph">
<p>Imagine that on top of the public commit P, you applied well-known
patches X, Y and Z from somebody else, and then built your three-patch
series A, B, C, the history would be like:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>---P---X---Y---Z---A---B---C</pre>
</div>
</div>
<div class="paragraph">
<p>With <code>git format-patch --base=P -3 C</code> (or variants thereof, e.g. with
<code>--cover-letter</code> or using <code>Z..C</code> instead of <code>-3 C</code> to specify the
range), the base tree information block is shown at the end of the
first message the command outputs (either the first patch, or the
cover letter), like this:</p>
</div>
<div class="listingblock">
<div class="content">
<pre>base-commit: P
prerequisite-patch-id: X
prerequisite-patch-id: Y
prerequisite-patch-id: Z</pre>
</div>
</div>
<div class="paragraph">
<p>For non-linear topology, such as</p>
</div>
<div class="literalblock">
<div class="content">
<pre>---P---X---A---M---C
    \         /
     Y---Z---B</pre>
</div>
</div>
<div class="paragraph">
<p>You can also use <code>git format-patch --base=P -3 C</code> to generate patches
for A, B and C, and the identifiers for P, X, Y, Z are appended at the
end of the first message.</p>
</div>
<div class="paragraph">
<p>If set <code>--base=auto</code> in cmdline, it will automatically compute
the base commit as the merge base of tip commit of the remote-tracking
branch and revision-range specified in cmdline.
For a local branch, you need to make it to track a remote branch by <code>git branch
--set-upstream-to</code> before using this option.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_examples">EXAMPLES</h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p>Extract commits between revisions R1 and R2, and apply them on top of
the current branch using <em>git am</em> to cherry-pick them:</p>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch -k --stdout R1..R2 | git am -3 -k</pre>
</div>
</div>
</li>
<li>
<p>Extract all commits which are in the current branch but not in the
origin branch:</p>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch origin</pre>
</div>
</div>
<div class="paragraph">
<p>For each commit a separate file is created in the current directory.</p>
</div>
</li>
<li>
<p>Extract all commits that lead to <em>origin</em> since the inception of the
project:</p>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch --root origin</pre>
</div>
</div>
</li>
<li>
<p>The same as the previous one:</p>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch -M -B origin</pre>
</div>
</div>
<div class="paragraph">
<p>Additionally, it detects and handles renames and complete rewrites
intelligently to produce a renaming patch.  A renaming patch reduces
the amount of text output, and generally makes it easier to review.
Note that non-Git "patch" programs won&#8217;t understand renaming patches, so
use it only when you know the recipient uses Git to apply your patch.</p>
</div>
</li>
<li>
<p>Extract three topmost commits from the current branch and format them
as e-mailable patches:</p>
<div class="listingblock">
<div class="content">
<pre>$ git format-patch -3</pre>
</div>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_caveats">CAVEATS</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Note that <code>format-patch</code> will omit merge commits from the output, even
if they are part of the requested range. A simple "patch" does not
include enough information for the receiving end to reproduce the same
merge commit.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_see_also">SEE ALSO</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a href="git-am.html">git-am(1)</a>, <a href="git-send-email.html">git-send-email(1)</a></p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_git">GIT</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Part of the <a href="git.html">git(1)</a> suite</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>