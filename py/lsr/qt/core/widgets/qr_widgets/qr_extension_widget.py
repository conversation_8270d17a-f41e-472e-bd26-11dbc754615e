import os
from lsr.qt.core.widgets.qr_widgets.qr_ui_setting import QRUiSetting
from Qt import QtWidgets, QtCore, QtGui


class QRName(QtWidgets.QWidget, QRUiSetting):
    """
    Extension of QLineEdit with UI settings persistence capability.

    This class extends QLineEdit and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, widget: QtWidgets.QWidget, key_name):
        """
        Initialize the QRLineEdit widget.

        Args:
            key_name (str): The key name used to store settings for this widget.
        """
        super(QRName, self).__init__()
        self.key_name = key_name

        layout = QtWidgets.QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(widget)
        self.setLayout(layout)

    def get_key_path(self):
        """
        Get the key path for this widget.

        Returns:
            str: The key name used for settings storage.
        """
        return self.key_name


class QRLineEdit(QtWidgets.QLineEdit, QRUiSetting):
    """
    Extension of QLineEdit with UI settings persistence capability.

    This class extends QLineEdit and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, key_name):
        """
        Initialize the QRLineEdit widget.

        Args:
            key_name (str): The key name used to store settings for this widget.
        """
        super(QRLineEdit, self).__init__()
        self.key_name = key_name

    def load_setting(self, setting):
        """
        Load the previously saved text from settings.

        Args:
            setting (QSettings): The settings object to load from.
        """
        path = self.get_full_path()
        if setting.contains(path):
            self.setText(setting.value(path))

    def save_setting(self, setting):
        """
        Save the current text to settings.

        Args:
            setting (QSettings): The settings object to save to.
        """
        path = self.get_full_path()
        setting.setValue(path, self.text())

    def get_key_path(self):
        """
        Get the key path for this widget.

        Returns:
            str: The key name used for settings storage.
        """
        return self.key_name


class QRCheckBox(QtWidgets.QCheckBox, QRUiSetting):
    """
    Extension of QCheckBox with UI settings persistence capability.

    This class extends QCheckBox and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, key_name):
        """
        Initialize the QRCheckBox widget.

        Args:
            key_name (str): The key name used to store settings and as checkbox text.
        """
        super(QRCheckBox, self).__init__(key_name)
        self.key_name = key_name

    def load_setting(self, setting):
        """
        Load the previously saved checked state from settings.

        Args:
            setting (QSettings): The settings object to load from.
        """
        path = self.get_full_path()
        if setting.contains(path):
            self.setChecked(setting.value(path) == 'true')

    def save_setting(self, setting):
        """
        Save the current checked state to settings.

        Args:
            setting (QSettings): The settings object to save to.
        """
        path = self.get_full_path()
        setting.setValue(path, self.isChecked())

    def get_key_path(self):
        """
        Get the key path for this widget.

        Returns:
            str: The key name used for settings storage.
        """
        return self.key_name


class QRRadioButton(QtWidgets.QRadioButton, QRUiSetting):
    """
    Extension of QRadioButton with UI settings persistence capability.

    This class extends QRadioButton and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, key_name):
        """
        Initialize the QRRadioButton widget.

        Args:
            key_name (str): The key name used to store settings and as radio button text.
        """
        super(QRRadioButton, self).__init__(key_name)
        self.key_name = key_name

    def load_setting(self, setting):
        """
        Load the previously saved checked state from settings.

        Args:
            setting (QSettings): The settings object to load from.
        """
        path = self.get_full_path()
        if setting.contains(path):
            if setting.value(path):
                self.setChecked(setting.value(path) == 'true')

    def save_setting(self, setting):
        """
        Save the current checked state to settings.

        Args:
            setting (QSettings): The settings object to save to.
        """
        path = self.get_full_path()
        setting.setValue(path, self.isChecked())

    def get_key_path(self):
        """
        Get the key path for this widget.

        Returns:
            str: The key name used for settings storage.
        """
        return self.key_name


class QRTabWidget(QtWidgets.QTabWidget, QRUiSetting):
    """
    Extension of QTabWidget with UI settings persistence capability.

    This class extends QTabWidget and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, key_name):
        """
        Initialize the QRTabWidget widget.

        Args:
            key_name (str): The key name used to store settings for this widget.
        """
        super(QRTabWidget, self).__init__()
        self.key_name = key_name

    def load_setting(self, setting):
        """
        Load the previously saved tab index from settings.

        Args:
            setting (QSettings): The settings object to load from.
        """
        path = self.get_full_path()
        if setting.contains(path):
            if setting.value(path):
                self.setCurrentIndex(int(setting.value(path)))
            else:  # support maya2020
                self.setCurrentIndex(0)

    def save_setting(self, setting):
        """
        Save the current tab index to settings.

        Args:
            setting (QSettings): The settings object to save to.
        """
        path = self.get_full_path()
        setting.setValue(path, self.currentIndex())

    def get_key_path(self):
        """
        Get the key path for this widget by traversing parent hierarchy.

        This method tries to get the tab text as the key path by finding
        parent widgets that implement QRUiSetting.

        Returns:
            str: The tab text or widget class name.
        """
        parent_object = []
        widget = self
        while widget is not None:
            if isinstance(widget, QRUiSetting):
                parent_object.insert(0, widget)
            widget = widget.parent()

        if len(parent_object) > 1:
            index = widget.indexOf(parent_object[1])
            return widget.tabText(index)

        return type(widget).__name__

    def addTab(self, widget, text):
        """
        Add a new tab with the specified widget and text.

        Args:
            widget (QWidget): The widget to be added as a tab.
            text (str): The text label for the tab.
        """
        super(QRTabWidget, self).addTab(QRName(widget, text), text)


class QRListWidget(QtWidgets.QListWidget, QRUiSetting):
    """
    Extension of QListWidget with UI settings persistence capability.

    This class extends QListWidget and implements the QRUiSetting interface
    to provide functionality for saving and loading UI state.
    """

    def __init__(self, key_name):
        """
        Initialize the QRListWidget widget.

        Args:
            key_name (str): The key name used to store settings for this widget.
        """
        super(QRListWidget, self).__init__()
        self.key_name = key_name

    def load_setting(self, setting):
        """
        Load the previously saved list items from settings.

        Args:
            setting (QSettings): The settings object to load from.
        """
        path = self.get_full_path()
        if setting.contains(path):
            self.clear()
            files = setting.value(path)
            if files:
                self.add_items(files)

    def save_setting(self, setting):
        """
        Save all list item texts to settings.

        Args:
            setting (QSettings): The settings object to save to.
        """
        path = self.get_full_path()
        setting.setValue(path, [self.item(i).text() for i in range(self.count())])

    def get_key_path(self):
        """
        Get the key path for this widget.

        Returns:
            str: The key name used for settings storage.
        """
        return self.key_name

    def add_items(self, items):
        """
        Add multiple items to the list widget.

        Args:
            items (list): List of item texts to add.
        """
        for item in items:
            self.add_item(item)

    def add_item(self, text):
        """
        Add a single item to the list widget.

        This method creates a QListWidgetItem with the given text and adds it to the list.
        If a corresponding JSON file exists, the item is styled with bold font and blue color.

        Args:
            text (str): Item text to add.
        """
        if text is None or text == "":
            return

        item = QtWidgets.QListWidgetItem()
        item.setText(text)

        maya_file = text
        maya_file_without_ext = os.path.splitext(maya_file)[0]
        json_file = maya_file_without_ext + "_Split.json"
        if not os.path.exists(maya_file):
            font = item.font()
            font.setBold(True)
            item.setFont(font)
            item.setForeground(QtGui.QColor(110, 110, 110))

        elif os.path.exists(json_file):
            font = item.font()
            font.setBold(True)
            item.setFont(font)
            item.setForeground(QtGui.QColor(0, 120, 215))

        self.addItem(item)
