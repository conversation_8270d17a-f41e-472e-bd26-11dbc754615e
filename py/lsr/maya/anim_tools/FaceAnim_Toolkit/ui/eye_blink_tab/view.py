#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线编辑器 - View层
负责UI显示、用户交互和视觉反馈
"""

from PySide2 import QtWidgets, QtCore, QtGui


class BezierCurveWidget(QtWidgets.QWidget):
    """贝塞尔曲线绘制控件"""
    
    # 信号定义
    mouse_pressed = QtCore.Signal(QtCore.QPointF, int)  # 鼠标按下 (位置, 按钮)
    mouse_moved = QtCore.Signal(QtCore.QPointF)  # 鼠标移动
    mouse_released = QtCore.Signal(QtCore.QPointF)  # 鼠标释放
    key_pressed = QtCore.Signal(int, int)  # 按键按下 (键值, 修饰键)
    focus_changed = QtCore.Signal(bool)  # 焦点改变
    
    def __init__(self, parent=None):
        super(BezierCurveWidget, self).__init__(parent)
        self.setMinimumSize(400, 300)
        self.setMouseTracking(True)
        self.setFocusPolicy(QtCore.Qt.StrongFocus)
        
        # 视图数据
        self.keyframes = []
        self.selected_keyframe = None
        self.time_range = (0.0, 10.0)
        self.value_range = (-1.0, 1.0)
        self.grid_enabled = True
        self.curve_points = []
        
        # 绘制参数
        self.keyframe_size = 8
        self.tangent_size = 6
        
        # 交互状态
        self.dragging_keyframe = None
        self.dragging_tangent = None
    
    # ==================== 数据更新接口 ====================
    
    def update_keyframes(self, keyframes):
        """更新关键帧数据"""
        self.keyframes = keyframes
        self.update()
    
    def update_selected_keyframe(self, keyframe):
        """更新选中的关键帧"""
        self.selected_keyframe = keyframe
        self.update()
    
    def update_view_range(self, time_range, value_range):
        """更新视图范围"""
        self.time_range = time_range
        self.value_range = value_range
        self.update()
    
    def update_curve_points(self, points):
        """更新曲线点数据"""
        self.curve_points = points
        self.update()
    
    def set_grid_enabled(self, enabled):
        """设置网格显示"""
        self.grid_enabled = enabled
        self.update()
    
    def set_dragging_state(self, keyframe=None, tangent=None):
        """设置拖拽状态"""
        self.dragging_keyframe = keyframe
        self.dragging_tangent = tangent
    
    # ==================== 坐标转换 ====================
    
    def world_to_screen(self, time, value):
        """世界坐标转屏幕坐标"""
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        time_norm = (time - self.time_range[0]) / (self.time_range[1] - self.time_range[0])
        value_norm = (value - self.value_range[0]) / (self.value_range[1] - self.value_range[0])
        
        x = rect.left() + time_norm * rect.width()
        y = rect.bottom() - value_norm * rect.height()
        
        return QtCore.QPointF(x, y)
    
    def screen_to_world(self, screen_pos):
        """屏幕坐标转世界坐标"""
        if isinstance(screen_pos, QtCore.QPoint):
            screen_pos = QtCore.QPointF(screen_pos)
        
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        time_norm = (screen_pos.x() - rect.left()) / rect.width()
        value_norm = (rect.bottom() - screen_pos.y()) / rect.height()
        
        time = self.time_range[0] + time_norm * (self.time_range[1] - self.time_range[0])
        value = self.value_range[0] + value_norm * (self.value_range[1] - self.value_range[0])
        
        return time, value
    
    def is_pos_in_curve_area(self, pos):
        """检查位置是否在曲线编辑区域内"""
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)
        
        curve_rect = self.rect().adjusted(20, 20, -20, -20)
        return curve_rect.contains(pos.toPoint())
    
    # ==================== 碰撞检测 ====================
    
    def get_keyframe_at_pos(self, pos):
        """获取指定位置的关键帧"""
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)
        
        for keyframe in self.keyframes:
            screen_pos = self.world_to_screen(keyframe.time, keyframe.value)
            if (pos - screen_pos).manhattanLength() < self.keyframe_size:
                return keyframe
        return None
    
    def get_tangent_at_pos(self, pos):
        """获取指定位置的切线控制点"""
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)
        
        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)
            
            # 检查入切线
            in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * 30, -keyframe.in_tangent[1] * 30)
            if (pos - in_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'in'
            
            # 检查出切线
            out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * 30, -keyframe.out_tangent[1] * 30)
            if (pos - out_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'out'
        
        return None, None
    
    # ==================== 绘制方法 ====================
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        # 绘制整个控件背景
        painter.fillRect(self.rect(), QtGui.QColor(30, 30, 30))
        
        # 曲线编辑区域
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        # 绘制曲线区域背景
        painter.fillRect(rect, QtGui.QColor(40, 40, 40))
        painter.setPen(QtGui.QPen(QtGui.QColor(120, 120, 120), 2))
        painter.drawRect(rect)
        
        # 绘制网格
        if self.grid_enabled:
            self._draw_grid(painter, rect)
        
        # 绘制曲线
        self._draw_curve(painter)
        
        # 绘制关键帧
        self._draw_keyframes(painter)
        
        # 绘制区域提示文字
        painter.setPen(QtGui.QPen(QtGui.QColor(150, 150, 150), 1))
        painter.setFont(QtGui.QFont("Arial", 9))
        painter.drawText(rect.left() + 5, rect.top() - 5, "曲线编辑区域")
        
        # 如果有焦点，显示快捷键提示
        if self.hasFocus():
            painter.setPen(QtGui.QPen(QtGui.QColor(100, 200, 255), 1))
            painter.setFont(QtGui.QFont("Arial", 8))
            hint_text = "快捷键: Delete删除 | Esc取消 | Ctrl+A添加 | Ctrl+Z撤销 | Ctrl+Y重做"
            painter.drawText(rect.left() + 5, rect.bottom() + 15, hint_text)
            
            # 绘制焦点边框
            painter.setPen(QtGui.QPen(QtGui.QColor(100, 200, 255), 1, QtCore.Qt.DashLine))
            painter.drawRect(rect.adjusted(-1, -1, 1, 1))
    
    def _draw_grid(self, painter, rect):
        """绘制网格"""
        painter.setPen(QtGui.QPen(QtGui.QColor(60, 60, 60), 1))
        
        # 垂直网格线
        for i in range(11):
            x = rect.left() + i * rect.width() / 10
            painter.drawLine(x, rect.top(), x, rect.bottom())
        
        # 水平网格线
        for i in range(11):
            y = rect.top() + i * rect.height() / 10
            painter.drawLine(rect.left(), y, rect.right(), y)
    
    def _draw_curve(self, painter):
        """绘制贝塞尔曲线"""
        if len(self.curve_points) < 2:
            return
        
        painter.setPen(QtGui.QPen(QtGui.QColor(100, 200, 255), 2))
        
        path = QtGui.QPainterPath()
        first_point = True
        
        for time, value in self.curve_points:
            screen_pos = self.world_to_screen(time, value)
            
            if first_point:
                path.moveTo(screen_pos)
                first_point = False
            else:
                path.lineTo(screen_pos)
        
        painter.drawPath(path)
    
    def _draw_keyframes(self, painter):
        """绘制关键帧和切线"""
        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)
            
            # 绘制切线
            if keyframe == self.selected_keyframe:
                painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 0), 1))
                
                # 入切线
                in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * 30, -keyframe.in_tangent[1] * 30)
                painter.drawLine(kf_pos, in_pos)
                painter.fillRect(in_pos.x() - self.tangent_size//2, in_pos.y() - self.tangent_size//2,
                               self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))
                
                # 出切线
                out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * 30, -keyframe.out_tangent[1] * 30)
                painter.drawLine(kf_pos, out_pos)
                painter.fillRect(out_pos.x() - self.tangent_size//2, out_pos.y() - self.tangent_size//2,
                               self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))
            
            # 绘制关键帧点
            color = QtGui.QColor(255, 100, 100) if keyframe == self.selected_keyframe else QtGui.QColor(200, 200, 200)
            painter.setBrush(QtGui.QBrush(color))
            painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 2))
            
            painter.drawEllipse(kf_pos, self.keyframe_size, self.keyframe_size)
    
    # ==================== 事件处理 ====================
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        self.setFocus()
        pos = QtCore.QPointF(event.pos())
        self.mouse_pressed.emit(pos, event.button())
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        pos = QtCore.QPointF(event.pos())
        self.mouse_moved.emit(pos)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        pos = QtCore.QPointF(event.pos())
        self.mouse_released.emit(pos)
    
    def keyPressEvent(self, event):
        """键盘按下事件"""
        self.key_pressed.emit(event.key(), int(event.modifiers()))
    
    def focusInEvent(self, event):
        """获得焦点事件"""
        super(BezierCurveWidget, self).focusInEvent(event)
        self.focus_changed.emit(True)
        self.update()
    
    def focusOutEvent(self, event):
        """失去焦点事件"""
        super(BezierCurveWidget, self).focusOutEvent(event)
        self.focus_changed.emit(False)
        self.update()


class KeyframePropertiesWidget(QtWidgets.QWidget):
    """关键帧属性编辑器"""
    
    # 信号定义
    time_changed = QtCore.Signal(float)
    value_changed = QtCore.Signal(float)
    in_tangent_changed = QtCore.Signal(tuple)
    out_tangent_changed = QtCore.Signal(tuple)
    
    def __init__(self, parent=None):
        super(KeyframePropertiesWidget, self).__init__(parent)
        self.current_keyframe = None
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QFormLayout()
        
        # 时间输入
        self.time_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_spinbox.setRange(-999.0, 999.0)
        self.time_spinbox.setDecimals(3)
        self.time_spinbox.valueChanged.connect(self._on_time_changed)
        layout.addRow("时间:", self.time_spinbox)
        
        # 值输入
        self.value_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_spinbox.setRange(-999.0, 999.0)
        self.value_spinbox.setDecimals(3)
        self.value_spinbox.valueChanged.connect(self._on_value_changed)
        layout.addRow("值:", self.value_spinbox)
        
        # 入切线
        self.in_tangent_x = QtWidgets.QDoubleSpinBox()
        self.in_tangent_x.setRange(-10.0, 10.0)
        self.in_tangent_x.setDecimals(3)
        self.in_tangent_x.valueChanged.connect(self._on_in_tangent_changed)
        
        self.in_tangent_y = QtWidgets.QDoubleSpinBox()
        self.in_tangent_y.setRange(-10.0, 10.0)
        self.in_tangent_y.setDecimals(3)
        self.in_tangent_y.valueChanged.connect(self._on_in_tangent_changed)
        
        in_layout = QtWidgets.QHBoxLayout()
        in_layout.addWidget(QtWidgets.QLabel("X:"))
        in_layout.addWidget(self.in_tangent_x)
        in_layout.addWidget(QtWidgets.QLabel("Y:"))
        in_layout.addWidget(self.in_tangent_y)
        
        in_widget = QtWidgets.QWidget()
        in_widget.setLayout(in_layout)
        layout.addRow("入切线:", in_widget)
        
        # 出切线
        self.out_tangent_x = QtWidgets.QDoubleSpinBox()
        self.out_tangent_x.setRange(-10.0, 10.0)
        self.out_tangent_x.setDecimals(3)
        self.out_tangent_x.valueChanged.connect(self._on_out_tangent_changed)
        
        self.out_tangent_y = QtWidgets.QDoubleSpinBox()
        self.out_tangent_y.setRange(-10.0, 10.0)
        self.out_tangent_y.setDecimals(3)
        self.out_tangent_y.valueChanged.connect(self._on_out_tangent_changed)
        
        out_layout = QtWidgets.QHBoxLayout()
        out_layout.addWidget(QtWidgets.QLabel("X:"))
        out_layout.addWidget(self.out_tangent_x)
        out_layout.addWidget(QtWidgets.QLabel("Y:"))
        out_layout.addWidget(self.out_tangent_y)
        
        out_widget = QtWidgets.QWidget()
        out_widget.setLayout(out_layout)
        layout.addRow("出切线:", out_widget)
        
        self.setLayout(layout)
        self.setEnabled(False)
    
    def set_keyframe(self, keyframe):
        """设置当前编辑的关键帧"""
        self.current_keyframe = keyframe
        
        if keyframe:
            self.setEnabled(True)
            # 阻止信号发射，避免循环更新
            self._block_signals(True)
            
            self.time_spinbox.setValue(keyframe.time)
            self.value_spinbox.setValue(keyframe.value)
            self.in_tangent_x.setValue(keyframe.in_tangent[0])
            self.in_tangent_y.setValue(keyframe.in_tangent[1])
            self.out_tangent_x.setValue(keyframe.out_tangent[0])
            self.out_tangent_y.setValue(keyframe.out_tangent[1])
            
            self._block_signals(False)
        else:
            self.setEnabled(False)
    
    def _block_signals(self, block):
        """阻止/恢复信号发射"""
        self.time_spinbox.blockSignals(block)
        self.value_spinbox.blockSignals(block)
        self.in_tangent_x.blockSignals(block)
        self.in_tangent_y.blockSignals(block)
        self.out_tangent_x.blockSignals(block)
        self.out_tangent_y.blockSignals(block)
    
    def _on_time_changed(self):
        """时间改变"""
        if self.current_keyframe:
            self.time_changed.emit(self.time_spinbox.value())
    
    def _on_value_changed(self):
        """值改变"""
        if self.current_keyframe:
            self.value_changed.emit(self.value_spinbox.value())
    
    def _on_in_tangent_changed(self):
        """入切线改变"""
        if self.current_keyframe:
            tangent = (self.in_tangent_x.value(), self.in_tangent_y.value())
            self.in_tangent_changed.emit(tangent)
    
    def _on_out_tangent_changed(self):
        """出切线改变"""
        if self.current_keyframe:
            tangent = (self.out_tangent_x.value(), self.out_tangent_y.value())
            self.out_tangent_changed.emit(tangent)
