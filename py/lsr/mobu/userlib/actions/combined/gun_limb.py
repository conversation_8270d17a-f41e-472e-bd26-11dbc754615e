"""
Gun Ctrl Limb Action
"""

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa

from lsr.mobu.base_actions import RigAction
from lsr.mobu.utils.get_resource import get_fbx_res
from lsr.mobu.rig.file_operator import import_file
from lsr.mobu.rig.constants import CROSS_CTRL, PROP_CTRL, GRIP_CTRL

from lsr.mobu.nodezoo.node import Node
from lsr.mobu.nodezoo.node import Joint
from lsr.mobu.nodezoo.node import Marker

from lsr.mobu.nodezoo.node import Null
from lsr.mobu.nodezoo.node import MultiReferentialConstraint
from lsr.mobu.nodezoo.node import ParentConstraint
from lsr.mobu.nodezoo.node import RelationConstraint
from lsr.mobu.nodezoo.node import CharacterExtension
from lsr.mobu.nodezoo.node import Folder

from lsr.mobu.utils.FindObjects import find_animation_node

import lsr.mobu.rig.maker_util as maker_util
import lsr.mobu.rig.constants as const

from lsr.mobu.utils.FindObjects import get_scene_root_bones
from lsr.mobu.rig.constants import LOOK_KEYS


class Gun_Ctrl_Limb(RigAction):
    """
    Gun Ctrl Limb Action
    """

    _UI_ICON = 'gun_ctrl'

    @pa.str_param(default='GunA_CTRL')
    def ctrl_name(self):
        """The name of the ctrl."""

    @pa.pyobject_param(output=True)
    def gun_ctrl_node(self):
        """The gun ctrl node."""

    def run(self):
        """Executes this action."""
        self.reach_suffix = 'Reach_Master'

        self.grip_iks = [const.RightWrist_ToGrip,
                         const.LeftWrist_ToGrip,
                         const.RightWrist_ToForegrip,
                         const.LeftWrist_ToForegrip]

        self.gun_root = Null.create(name=const.GUN_ROOT)
        self.gun_ctrl = self.add_gun_ctrl()
        self.gun_ctrl_node.value = self.gun_ctrl

        self.gun_root.add_tag(const.GUN_CTRL, self.gun_ctrl)

        self.gun_ctrl_plc = self.gun_ctrl.parent
        self.gun_ctrl_plc.parent = self.gun_root

        self.world_ctrl = self.add_world_ctrl()
        self.world_ctrl.parent = self.gun_root

        self.re_const = None

        prefix = self.ctrl_name.value.split('_')[0]

        # self.right_aux, self.left_aux = self.add_aux_refs()
        # gun_parent_refs = [self.world_ctrl, self.right_aux, self.left_aux]
        gun_parent_refs = [self.world_ctrl]
        self.gun_constraint = MultiReferentialConstraint.create(
            gun_parent_refs, self.gun_ctrl_plc,
            name='MultiReferential_{}'.format(prefix))

        self.ik_nodes = []

        for ik_name in self.grip_iks:
            self.ik_nodes.append(self.add_grip_ik_ctrl(ik_name))

        self.folder = Folder.create(name='{}_Folder'.format(prefix), constraint=self.gun_constraint)

    def create_relation(self, *args, **kwargs):
        """
        Create relation constraint

        Returns:

        """

        r_const = RelationConstraint.create(name='MasterRelation_{}'.format(self.ctrl_name.value))

        self.gun_ctrl.visibility.animatable = True
        gun_ctrl_box = r_const.set_source(self.gun_ctrl)
        r_const.set_box_position(gun_ctrl_box, -1000, 0)
        vis_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(), 'Visibility')

        for i, ik_name in enumerate(self.grip_iks):
            reach_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(),
                                            '{}_{}'.format(ik_name, self.reach_suffix))

            mul_box = r_const.add_function_box('Number', 'Multiply (a x b)')
            r_const.set_box_position(mul_box, -300, i*150)
            mul_box_a = find_animation_node(mul_box.AnimationNodeInGet(), 'a')
            mul_box_b = find_animation_node(mul_box.AnimationNodeInGet(), 'b')
            mul_box_out = find_animation_node(mul_box.AnimationNodeOutGet(), 'Result')

            fb.FBConnect(reach_out, mul_box_a)
            fb.FBConnect(vis_out, mul_box_b)

            grip_node = self.gun_root.get_tag(ik_name)
            grip_node.visibility.animatable = True
            grip_box = r_const.set_destination(grip_node)
            r_const.set_box_position(grip_box, 0, i * 150)
            vis_in = find_animation_node(grip_box.AnimationNodeInGet(), 'Visibility')
            fb.FBConnect(mul_box_out, vis_in)

        gun_constraint_box = r_const.set_destination(self.gun_constraint)
        r_const.set_box_position(gun_constraint_box, -200, -300)

        gun_constraint_active_in = find_animation_node(gun_constraint_box.AnimationNodeInGet(), 'Active Parent Index')
        gun_ctrl_active_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(), 'Active_Parent_Index')
        fb.FBConnect(gun_ctrl_active_out, gun_constraint_active_in)

        # set self.world_ctrl visibility
        self.world_ctrl.visibility.animatable = True
        world_box = r_const.set_destination(self.world_ctrl)
        r_const.set_box_position(world_box, 100, -600)

        identical_box = r_const.add_function_box('Number', 'Is Identical (a == b)')
        r_const.set_box_position(identical_box, -200, -600)
        identical_box_a = find_animation_node(identical_box.AnimationNodeInGet(), 'a')
        identical_box_b = find_animation_node(identical_box.AnimationNodeInGet(), 'b')
        identical_box_b.WriteData([1])
        fb.FBConnect(gun_ctrl_active_out, identical_box_a)
        identical_box_out = find_animation_node(identical_box.AnimationNodeOutGet(), 'Result')

        world_box_visibility = find_animation_node(world_box.AnimationNodeInGet(), 'Visibility')
        fb.FBConnect(identical_box_out, world_box_visibility)

        r_const.active = True
        r_const.weight = 100.0
        self.folder.add_constraint(r_const)

        self.re_const = r_const

    def add_gun_ctrl(self):
        """
        Add Gun Ctrl

        Returns:

        """
        ctrl_name = self.ctrl_name.value
        fbx_path = get_fbx_res('{}.fbx'.format(PROP_CTRL))
        import_file(fbx_path, only_model=True)

        ctrl_node = Node(PROP_CTRL)
        ctrl_node.name = ctrl_name

        ctrl_plc = Null.create(name='{}_PLC'.format(ctrl_name))

        ctrl_node.parent = ctrl_plc

        return ctrl_node

    def edit_gun_constraint(self):
        """
        Edit Gun Constraint

        Returns:
            None
        """
        attr = self.gun_constraint.attr('Active Parent Index')
        attr.animatable = True

        offset_names = self.gun_constraint.all_attr_names('*.Offset.*')
        for offset_name in offset_names:
            attr = self.gun_constraint.attr(offset_name)
            attr.animatable = True

        self.gun_root.add_tag(const.GUN_MULTI_CONSTRAINT, self.gun_constraint)

    def edit_gun_ctrl(self):
        """
        Edit Gun Ctrl

        Returns:
            None
        """
        for ik_name in self.grip_iks:
            grip_attr = self.gun_ctrl.add_attr('double',
                                               name='{}_{}'.format(ik_name, self.reach_suffix),
                                               keyable=True)
            grip_attr.animatable = True

        index_attr = self.gun_ctrl.add_attr('enum', name='Active_Parent_Index')
        index_attr.add_enum_list(['Gun', 'World', 'Right_Hand', 'Left_Hand'])
        index_attr.animatable = True

    def add_world_ctrl(self):
        """
        Add World Ctrl

        Returns:

        """
        ctrl_name = self.ctrl_name.value
        fbx_path = get_fbx_res('{}.fbx'.format(CROSS_CTRL))
        import_file(fbx_path, only_model=True)

        ctrl_node = Node(CROSS_CTRL)
        ctrl_node.name = '{}_World_CTRL'.format(ctrl_name.split('_')[0])

        return ctrl_node

    def add_grip_ik_ctrl(self, ctrl_name):
        """
        Add Grip IK Ctrl

        Args:
            ctrl_name (str): ctrl name

        Returns:
            Node: ctrl node
        """
        fbx_path = get_fbx_res('{}.fbx'.format(GRIP_CTRL))
        import_file(fbx_path, only_model=True)

        ctrl_node = Node(GRIP_CTRL)
        ctrl_node.name = '{}_CTRL'.format(ctrl_name)

        ctrl_plc = Null.create(name='{}_PLC'.format(ctrl_name))
        ctrl_node.parent = ctrl_plc

        ctrl_plc.parent = self.gun_ctrl

        self.gun_root.add_tag(ctrl_name, ctrl_node)

        return ctrl_node

    def add_extension(self):
        """
        Add Extension

        Returns:
            CharacterExtension: extension node
        """
        ext_node = CharacterExtension.create(name='{}_EXT'.format(self.ctrl_name.value.split('_')[0]))

        nodes = self.ik_nodes + [self.gun_ctrl, self.world_ctrl, self.gun_ctrl_plc]

        for node in nodes:
            ext_node.add_model(node)

        self.gun_root.add_tag(const.EXTENSION, ext_node)

        return ext_node

    def create_aux_constraint(self, grip_ctrl, fore_ctrl, mk_node):
        """
        Create aux constraint
        Args:
            grip_ctrl ():
            fore_ctrl ():
            mk_node ():

        Returns:

        """
        aux_constraint = ParentConstraint.create(grip_ctrl, mk_node, name='Parent_{}'.format(mk_node.name))
        aux_constraint.add_reference(1, fore_ctrl)
        aux_constraint.set_weight_animatable(True)
        self.folder.add_constraint(aux_constraint)
        return aux_constraint

    def connect_animation_nodes(self, constraint_box, gun_ctrl_box, grip_ctrl, grip_idx):
        """
        Connect animation nodes
        Args:
            constraint_box ():
            gun_ctrl_box ():
            grip_ctrl ():
            grip_idx ():

        Returns:

        """
        constraint_in = find_animation_node(constraint_box.AnimationNodeInGet(), '{}.Weight'.format(grip_ctrl.name))
        grip_out = find_animation_node(gun_ctrl_box.AnimationNodeOutGet(),
                                       '{}_{}'.format(self.grip_iks[grip_idx], self.reach_suffix))
        fb.FBConnect(grip_out, constraint_in)

    def add_aux_refs(self):
        """ Add aux refs. """
        ref_node = []
        aux_names = ['RightWrist_Aux_REF', 'LeftWrist_Aux_REF']

        for aux_name in aux_names:
            mk_node = Marker.create(name=aux_name)
            mk_node.look = 'kFBMarkerLookSphere'
            mk_node.size = 100.0
            mk_node.color = fb.FBColor(1, 1, 1)
            mk_node.set_translation([0, 0, 0], space='world')
            ref_node.append(mk_node)
            self.gun_root.add_tag(aux_name, mk_node)

        return ref_node

    def edit_aux_refs(self):
        """ Edit aux refs. """
        aux_names = ['RightWrist_Aux_REF', 'LeftWrist_Aux_REF']
        grip_indices = [(0, 2), (1, 3)]
        box_positions = [600, 750]

        for aux_name, grip_idx, box_pos in zip(aux_names, grip_indices, box_positions):
            grip_ctrl = self.gun_root.get_tag(self.grip_iks[grip_idx[0]])
            fore_ctrl = self.gun_root.get_tag(self.grip_iks[grip_idx[1]])
            mk_node = self.gun_root.get_tag(aux_name)
            aux_constraint = self.create_aux_constraint(grip_ctrl, fore_ctrl, mk_node)

            if self.re_const:
                constraint_box = self.re_const.set_destination(aux_constraint)
                self.re_const.set_box_position(constraint_box, -300, box_pos)
                gun_ctrl_box = self.re_const.find_box_by_name(self.gun_ctrl.name)

                self.connect_animation_nodes(constraint_box, gun_ctrl_box, grip_ctrl, grip_idx[0])
                self.connect_animation_nodes(constraint_box, gun_ctrl_box, fore_ctrl, grip_idx[1])

    def end(self):
        self.edit_gun_ctrl()

        self.edit_gun_constraint()

        self.create_relation()

        # self.edit_aux_refs()
        self.add_extension()
