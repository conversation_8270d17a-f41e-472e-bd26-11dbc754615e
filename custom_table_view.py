#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义TableView，支持表头定义数据，item中可以使用combobox或lineEdit进行编辑
"""

import sys
from PySide2 import QtWidgets, QtCore, QtGui


class EditableTableModel(QtCore.QAbstractTableModel):
    """可编辑的表格模型"""
    
    def __init__(self, headers=None, data=None, parent=None):
        super(EditableTableModel, self).__init__(parent)
        self._headers = headers or []
        self._data = data or []
        
    def rowCount(self, parent=QtCore.QModelIndex()):
        return len(self._data)
    
    def columnCount(self, parent=QtCore.QModelIndex()):
        return len(self._headers)
    
    def data(self, index, role=QtCore.Qt.DisplayRole):
        if not index.isValid():
            return None
            
        row = index.row()
        col = index.column()
        
        if row >= len(self._data) or col >= len(self._headers):
            return None
            
        if role == QtCore.Qt.DisplayRole or role == QtCore.Qt.EditRole:
            return self._data[row][col]
        elif role == QtCore.Qt.UserRole:
            # 返回列的配置信息
            return self._headers[col]
            
        return None
    
    def setData(self, index, value, role=QtCore.Qt.EditRole):
        if not index.isValid() or role != QtCore.Qt.EditRole:
            return False
            
        row = index.row()
        col = index.column()
        
        if row >= len(self._data) or col >= len(self._headers):
            return False
            
        self._data[row][col] = value
        self.dataChanged.emit(index, index, [role])
        return True
    
    def flags(self, index):
        if not index.isValid():
            return QtCore.Qt.NoItemFlags
            
        col = index.column()
        header_config = self._headers[col]
        
        # 检查是否可编辑
        if header_config.get('editable', True):
            return QtCore.Qt.ItemIsEnabled | QtCore.Qt.ItemIsSelectable | QtCore.Qt.ItemIsEditable
        else:
            return QtCore.Qt.ItemIsEnabled | QtCore.Qt.ItemIsSelectable
    
    def headerData(self, section, orientation, role=QtCore.Qt.DisplayRole):
        if orientation == QtCore.Qt.Horizontal and role == QtCore.Qt.DisplayRole:
            if section < len(self._headers):
                return self._headers[section].get('title', f'Column {section}')
        return None
    
    def insertRows(self, row, count, parent=QtCore.QModelIndex()):
        self.beginInsertRows(parent, row, row + count - 1)
        for i in range(count):
            # 创建新行，使用默认值
            new_row = []
            for header in self._headers:
                default_value = header.get('default', '')
                new_row.append(default_value)
            self._data.insert(row + i, new_row)
        self.endInsertRows()
        return True
    
    def removeRows(self, row, count, parent=QtCore.QModelIndex()):
        if row < 0 or row + count > len(self._data):
            return False
            
        self.beginRemoveRows(parent, row, row + count - 1)
        for i in range(count):
            self._data.pop(row)
        self.endRemoveRows()
        return True
    
    def set_headers(self, headers):
        """设置表头配置"""
        self.beginResetModel()
        self._headers = headers
        # 调整现有数据以匹配新的列数
        for row in self._data:
            while len(row) < len(headers):
                row.append('')
            while len(row) > len(headers):
                row.pop()
        self.endResetModel()
    
    def set_data(self, data):
        """设置表格数据"""
        self.beginResetModel()
        self._data = data
        self.endResetModel()
    
    def get_data(self):
        """获取表格数据"""
        return self._data

    def get_data_as_dict_list(self):
        """获取表格数据，以key_name为键的字典列表格式返回

        Returns:
            list: 每行数据为一个字典，键为header中的key_name
        """
        result = []
        for row_data in self._data:
            row_dict = {}
            for col_index, value in enumerate(row_data):
                if col_index < len(self._headers):
                    key_name = self._headers[col_index].get('key_name', f'column_{col_index}')
                    row_dict[key_name] = value
            result.append(row_dict)
        return result


class CustomItemDelegate(QtWidgets.QStyledItemDelegate):
    """自定义项目委托，支持combobox和lineEdit编辑器"""
    
    def createEditor(self, parent, option, index):
        """创建编辑器"""
        # 获取列的配置信息
        header_config = index.data(QtCore.Qt.UserRole)
        editor_type = header_config.get('editor_type', 'lineedit')
        
        if editor_type == 'combobox':
            editor = QtWidgets.QComboBox(parent)
            items = header_config.get('items', [])
            editor.addItems(items)
            editor.setEditable(header_config.get('editable_combo', False))
            return editor
        elif editor_type == 'lineedit':
            editor = QtWidgets.QLineEdit(parent)
            # 设置输入验证器
            validator_type = header_config.get('validator', None)
            if validator_type == 'int':
                editor.setValidator(QtGui.QIntValidator())
            elif validator_type == 'double':
                editor.setValidator(QtGui.QDoubleValidator())
            return editor
        elif editor_type == 'spinbox':
            editor = QtWidgets.QSpinBox(parent)
            editor.setMinimum(header_config.get('min_value', 0))
            editor.setMaximum(header_config.get('max_value', 100))
            return editor
        elif editor_type == 'doublespinbox':
            editor = QtWidgets.QDoubleSpinBox(parent)
            editor.setMinimum(header_config.get('min_value', 0.0))
            editor.setMaximum(header_config.get('max_value', 100.0))
            editor.setDecimals(header_config.get('decimals', 2))
            return editor
        else:
            # 默认使用父类的编辑器
            return super(CustomItemDelegate, self).createEditor(parent, option, index)
    
    def setEditorData(self, editor, index):
        """设置编辑器数据"""
        value = index.data(QtCore.Qt.EditRole)
        header_config = index.data(QtCore.Qt.UserRole)
        editor_type = header_config.get('editor_type', 'lineedit')
        
        if editor_type == 'combobox':
            if value in [editor.itemText(i) for i in range(editor.count())]:
                editor.setCurrentText(str(value))
            else:
                editor.setEditText(str(value))
        elif editor_type == 'lineedit':
            editor.setText(str(value))
        elif editor_type in ['spinbox', 'doublespinbox']:
            try:
                if editor_type == 'spinbox':
                    editor.setValue(int(value))
                else:
                    editor.setValue(float(value))
            except (ValueError, TypeError):
                editor.setValue(0)
        else:
            super(CustomItemDelegate, self).setEditorData(editor, index)
    
    def setModelData(self, editor, model, index):
        """设置模型数据"""
        header_config = index.data(QtCore.Qt.UserRole)
        editor_type = header_config.get('editor_type', 'lineedit')
        
        if editor_type == 'combobox':
            value = editor.currentText()
        elif editor_type == 'lineedit':
            value = editor.text()
        elif editor_type in ['spinbox', 'doublespinbox']:
            value = editor.value()
        else:
            super(CustomItemDelegate, self).setModelData(editor, model, index)
            return
            
        model.setData(index, value, QtCore.Qt.EditRole)
    
    def updateEditorGeometry(self, editor, option, index):
        """更新编辑器几何形状"""
        editor.setGeometry(option.rect)


class CustomTableView(QtWidgets.QTableView):
    """自定义TableView"""
    
    def __init__(self, parent=None):
        super(CustomTableView, self).__init__(parent)
        
        # 设置基本属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectRows)
        self.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        
        # 设置表头
        self.horizontalHeader().setStretchLastSection(True)
        self.verticalHeader().setDefaultSectionSize(30)
        
        # 设置自定义委托
        self.delegate = CustomItemDelegate()
        self.setItemDelegate(self.delegate)
        
        # 创建模型
        self.model = EditableTableModel()
        self.setModel(self.model)
        
        # 设置右键菜单
        self.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def set_headers(self, headers):
        """设置表头配置
        
        Args:
            headers (list): 表头配置列表，每个元素是一个字典，包含以下键：
                - title: 列标题
                - editor_type: 编辑器类型 ('lineedit', 'combobox', 'spinbox', 'doublespinbox')
                - items: combobox的选项列表
                - editable: 是否可编辑
                - editable_combo: combobox是否可编辑
                - validator: 输入验证器类型 ('int', 'double')
                - min_value, max_value: spinbox的范围
                - decimals: doublespinbox的小数位数
                - default: 默认值
        """
        self.model.set_headers(headers)
        
        # 调整列宽
        for i, header in enumerate(headers):
            width = header.get('width', 100)
            self.setColumnWidth(i, width)
    
    def set_data(self, data):
        """设置表格数据"""
        self.model.set_data(data)
    
    def get_data(self):
        """获取表格数据"""
        return self.model.get_data()

    def get_data_as_dict_list(self):
        """获取表格数据，以key_name为键的字典列表格式返回

        Returns:
            list: 每行数据为一个字典，键为header中的key_name
        """
        return self.model.get_data_as_dict_list()
    
    def add_row(self):
        """添加行"""
        row_count = self.model.rowCount()
        self.model.insertRows(row_count, 1)
    
    def remove_selected_rows(self):
        """删除选中的行"""
        selection = self.selectionModel().selectedRows()
        if not selection:
            return
            
        # 按行号降序排列，从后往前删除
        rows = sorted([index.row() for index in selection], reverse=True)
        for row in rows:
            self.model.removeRows(row, 1)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QtWidgets.QMenu(self)
        
        add_action = menu.addAction("添加行")
        add_action.triggered.connect(self.add_row)
        
        if self.selectionModel().selectedRows():
            remove_action = menu.addAction("删除选中行")
            remove_action.triggered.connect(self.remove_selected_rows)
        
        menu.exec_(self.mapToGlobal(position))


class TableViewDemo(QtWidgets.QWidget):
    """演示窗口"""
    
    def __init__(self):
        super(TableViewDemo, self).__init__()
        self.init_ui()
        self.setup_demo_data()
    
    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QVBoxLayout()
        
        # 创建表格
        self.table_view = CustomTableView()
        layout.addWidget(self.table_view)
        
        # 创建按钮
        button_layout = QtWidgets.QHBoxLayout()
        
        self.add_row_btn = QtWidgets.QPushButton("添加行")
        self.add_row_btn.clicked.connect(self.table_view.add_row)
        button_layout.addWidget(self.add_row_btn)
        
        self.remove_row_btn = QtWidgets.QPushButton("删除选中行")
        self.remove_row_btn.clicked.connect(self.table_view.remove_selected_rows)
        button_layout.addWidget(self.remove_row_btn)
        
        self.print_data_btn = QtWidgets.QPushButton("打印数据")
        self.print_data_btn.clicked.connect(self.print_data)
        button_layout.addWidget(self.print_data_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        self.setWindowTitle("自定义可编辑TableView演示")
        self.resize(800, 600)
    
    def setup_demo_data(self):
        """设置演示数据"""
        # 定义表头配置
        headers = [
            {
                'title': '姓名',
                'editor_type': 'lineedit',
                'editable': True,
                'width': 120,
                'default': '新用户'
            },
            {
                'title': '年龄',
                'editor_type': 'spinbox',
                'min_value': 0,
                'max_value': 150,
                'editable': True,
                'width': 80,
                'default': 25
            },
            {
                'title': '职业',
                'editor_type': 'combobox',
                'items': ['程序员', '设计师', '产品经理', '测试工程师', '运营'],
                'editable_combo': True,
                'editable': True,
                'width': 120,
                'default': '程序员'
            },
            {
                'title': '薪资',
                'editor_type': 'doublespinbox',
                'min_value': 0.0,
                'max_value': 100000.0,
                'decimals': 2,
                'editable': True,
                'width': 100,
                'default': 10000.0
            },
            {
                'title': '状态',
                'editor_type': 'combobox',
                'items': ['在职', '离职', '休假'],
                'editable_combo': False,
                'editable': True,
                'width': 80,
                'default': '在职'
            },
            {
                'title': '备注',
                'editor_type': 'lineedit',
                'editable': True,
                'width': 200,
                'default': ''
            }
        ]
        
        # 设置表头
        self.table_view.set_headers(headers)
        
        # 设置初始数据
        data = [
            ['张三', 28, '程序员', 15000.0, '在职', '资深开发工程师'],
            ['李四', 25, '设计师', 12000.0, '在职', 'UI设计师'],
            ['王五', 30, '产品经理', 18000.0, '休假', '产品负责人'],
            ['赵六', 26, '测试工程师', 11000.0, '在职', '自动化测试'],
        ]
        
        self.table_view.set_data(data)
    
    def print_data(self):
        """打印当前表格数据"""
        data = self.table_view.get_data()
        print("当前表格数据:")
        for i, row in enumerate(data):
            print(f"行 {i}: {row}")


if __name__ == '__main__':
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建演示窗口
    demo = TableViewDemo()
    demo.show()
    
    sys.exit(app.exec_())
