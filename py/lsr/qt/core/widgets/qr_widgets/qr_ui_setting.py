from Qt import QtWidgets

class QRUiSetting(object):
    '''
    Interface for setting
    '''

    def load_setting(self, setting):
        pass



    def save_setting(self, setting):
        pass


    def get_key_path(self):
        pass


    def get_full_path(self):
        """
        Get the path of the widget.
        """

        widget = self
        path = []
        parent_object = []
        while widget is not None:
            if isinstance(widget, QRUiSetting):
                path.insert(0, widget.get_key_path())
            elif isinstance(widget, QtWidgets.QGroupBox):
                path.insert(0, widget.title())
            elif isinstance(widget, QtWidgets.QMainWindow):
                path.insert(0, widget.windowTitle())

            parent_object.insert(0, widget)
            widget = widget.parent()

        return '/'.join(path)
