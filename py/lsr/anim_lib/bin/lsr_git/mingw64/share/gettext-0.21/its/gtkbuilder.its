<?xml version="1.0"?>
<!--
  Copyright (C) 2015 Free Software Foundation, Inc.
  This file was written by <PERSON><PERSON> <<EMAIL>>, 2015.

  This program is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation; either version 3 of the License, or
  (at your option) any later version.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with this program.  If not, see <https://www.gnu.org/licenses/>.
-->
<its:rules xmlns:its="http://www.w3.org/2005/11/its"
           xmlns:gt="https://www.gnu.org/s/gettext/ns/its/extensions/1.0"
           version="2.0">
  <its:translateRule selector="/interface" translate="no"/>
  <its:translateRule selector="/interface//*[@translatable = 'yes']"
                     translate="yes"/>
  <its:locNoteRule selector="/interface//*[@comments]"
                   locNotePointer="@comments"
                   locNoteType="alert"/>
  <gt:escapeRule selector="/interface//@comments" escape="no"/>

  <gt:contextRule selector="/interface//*[@context]" contextPointer="@context"/>

  <its:preserveSpaceRule selector="/interface" space="preserve"/>
  <gt:escapeRule selector="/interface" escape="no"/>
</its:rules>
