<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>git-branch(1)</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="manpage">
<div id="header">
<h1>git-branch(1) Manual Page</h1>
<h2 id="_name">NAME</h2>
<div class="sectionbody">
<p>git-branch - List, create, or delete branches</p>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="_synopsis">SYNOPSIS</h2>
<div class="sectionbody">
<div class="verseblock">
<pre class="content"><em>git branch</em> [--color[=&lt;when&gt;] | --no-color] [--show-current]
        [-v [--abbrev=&lt;n&gt; | --no-abbrev]]
        [--column[=&lt;options&gt;] | --no-column] [--sort=&lt;key&gt;]
        [--merged [&lt;commit&gt;]] [--no-merged [&lt;commit&gt;]]
        [--contains [&lt;commit&gt;]] [--no-contains [&lt;commit&gt;]]
        [--points-at &lt;object&gt;] [--format=&lt;format&gt;]
        [(-r | --remotes) | (-a | --all)]
        [--list] [&lt;pattern&gt;&#8230;&#8203;]
<em>git branch</em> [--track[=(direct|inherit)] | --no-track] [-f]
        [--recurse-submodules] &lt;branchname&gt; [&lt;start-point&gt;]
<em>git branch</em> (--set-upstream-to=&lt;upstream&gt; | -u &lt;upstream&gt;) [&lt;branchname&gt;]
<em>git branch</em> --unset-upstream [&lt;branchname&gt;]
<em>git branch</em> (-m | -M) [&lt;oldbranch&gt;] &lt;newbranch&gt;
<em>git branch</em> (-c | -C) [&lt;oldbranch&gt;] &lt;newbranch&gt;
<em>git branch</em> (-d | -D) [-r] &lt;branchname&gt;&#8230;&#8203;
<em>git branch</em> --edit-description [&lt;branchname&gt;]</pre>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_description">DESCRIPTION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If <code>--list</code> is given, or if there are no non-option arguments, existing
branches are listed; the current branch will be highlighted in green and
marked with an asterisk.  Any branches checked out in linked worktrees will
be highlighted in cyan and marked with a plus sign. Option <code>-r</code> causes the
remote-tracking branches to be listed,
and option <code>-a</code> shows both local and remote branches.</p>
</div>
<div class="paragraph">
<p>If a <code>&lt;pattern&gt;</code>
is given, it is used as a shell wildcard to restrict the output to
matching branches. If multiple patterns are given, a branch is shown if
it matches any of the patterns.</p>
</div>
<div class="paragraph">
<p>Note that when providing a
<code>&lt;pattern&gt;</code>, you must use <code>--list</code>; otherwise the command may be interpreted
as branch creation.</p>
</div>
<div class="paragraph">
<p>With <code>--contains</code>, shows only the branches that contain the named commit
(in other words, the branches whose tip commits are descendants of the
named commit), <code>--no-contains</code> inverts it. With <code>--merged</code>, only branches
merged into the named commit (i.e. the branches whose tip commits are
reachable from the named commit) will be listed.  With <code>--no-merged</code> only
branches not merged into the named commit will be listed.  If the &lt;commit&gt;
argument is missing it defaults to <code>HEAD</code> (i.e. the tip of the current
branch).</p>
</div>
<div class="paragraph">
<p>The command&#8217;s second form creates a new branch head named &lt;branchname&gt;
which points to the current <code>HEAD</code>, or &lt;start-point&gt; if given. As a
special case, for &lt;start-point&gt;, you may use <code>"A...B"</code> as a shortcut for
the merge base of <code>A</code> and <code>B</code> if there is exactly one merge base. You
can leave out at most one of <code>A</code> and <code>B</code>, in which case it defaults to
<code>HEAD</code>.</p>
</div>
<div class="paragraph">
<p>Note that this will create the new branch, but it will not switch the
working tree to it; use "git switch &lt;newbranch&gt;" to switch to the
new branch.</p>
</div>
<div class="paragraph">
<p>When a local branch is started off a remote-tracking branch, Git sets up the
branch (specifically the <code>branch.&lt;name&gt;.remote</code> and <code>branch.&lt;name&gt;.merge</code>
configuration entries) so that <em>git pull</em> will appropriately merge from
the remote-tracking branch. This behavior may be changed via the global
<code>branch.autoSetupMerge</code> configuration flag. That setting can be
overridden by using the <code>--track</code> and <code>--no-track</code> options, and
changed later using <code>git branch --set-upstream-to</code>.</p>
</div>
<div class="paragraph">
<p>With a <code>-m</code> or <code>-M</code> option, &lt;oldbranch&gt; will be renamed to &lt;newbranch&gt;.
If &lt;oldbranch&gt; had a corresponding reflog, it is renamed to match
&lt;newbranch&gt;, and a reflog entry is created to remember the branch
renaming. If &lt;newbranch&gt; exists, -M must be used to force the rename
to happen.</p>
</div>
<div class="paragraph">
<p>The <code>-c</code> and <code>-C</code> options have the exact same semantics as <code>-m</code> and
<code>-M</code>, except instead of the branch being renamed, it will be copied to a
new name, along with its config and reflog.</p>
</div>
<div class="paragraph">
<p>With a <code>-d</code> or <code>-D</code> option, <code>&lt;branchname&gt;</code> will be deleted.  You may
specify more than one branch for deletion.  If the branch currently
has a reflog then the reflog will also be deleted.</p>
</div>
<div class="paragraph">
<p>Use <code>-r</code> together with <code>-d</code> to delete remote-tracking branches. Note, that it
only makes sense to delete remote-tracking branches if they no longer exist
in the remote repository or if <em>git fetch</em> was configured not to fetch
them again. See also the <em>prune</em> subcommand of <a href="git-remote.html">git-remote(1)</a> for a
way to clean up all obsolete remote-tracking branches.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_options">OPTIONS</h2>
<div class="sectionbody">
<div class="dlist">
<dl>
<dt class="hdlist1">-d</dt>
<dt class="hdlist1">--delete</dt>
<dd>
<p>Delete a branch. The branch must be fully merged in its
upstream branch, or in <code>HEAD</code> if no upstream was set with
<code>--track</code> or <code>--set-upstream-to</code>.</p>
</dd>
<dt class="hdlist1">-D</dt>
<dd>
<p>Shortcut for <code>--delete --force</code>.</p>
</dd>
<dt class="hdlist1">--create-reflog</dt>
<dd>
<p>Create the branch&#8217;s reflog.  This activates recording of
all changes made to the branch ref, enabling use of date
based sha1 expressions such as "&lt;branchname&gt;@{yesterday}".
Note that in non-bare repositories, reflogs are usually
enabled by default by the <code>core.logAllRefUpdates</code> config option.
The negated form <code>--no-create-reflog</code> only overrides an earlier
<code>--create-reflog</code>, but currently does not negate the setting of
<code>core.logAllRefUpdates</code>.</p>
</dd>
<dt class="hdlist1">-f</dt>
<dt class="hdlist1">--force</dt>
<dd>
<p>Reset &lt;branchname&gt; to &lt;startpoint&gt;, even if &lt;branchname&gt; exists
already. Without <code>-f</code>, <em>git branch</em> refuses to change an existing branch.
In combination with <code>-d</code> (or <code>--delete</code>), allow deleting the
branch irrespective of its merged status, or whether it even
points to a valid commit. In combination with
<code>-m</code> (or <code>--move</code>), allow renaming the branch even if the new
branch name already exists, the same applies for <code>-c</code> (or <code>--copy</code>).</p>
</dd>
<dt class="hdlist1">-m</dt>
<dt class="hdlist1">--move</dt>
<dd>
<p>Move/rename a branch, together with its config and reflog.</p>
</dd>
<dt class="hdlist1">-M</dt>
<dd>
<p>Shortcut for <code>--move --force</code>.</p>
</dd>
<dt class="hdlist1">-c</dt>
<dt class="hdlist1">--copy</dt>
<dd>
<p>Copy a branch, together with its config and reflog.</p>
</dd>
<dt class="hdlist1">-C</dt>
<dd>
<p>Shortcut for <code>--copy --force</code>.</p>
</dd>
<dt class="hdlist1">--color[=&lt;when&gt;]</dt>
<dd>
<p>Color branches to highlight current, local, and
remote-tracking branches.
The value must be always (the default), never, or auto.</p>
</dd>
<dt class="hdlist1">--no-color</dt>
<dd>
<p>Turn off branch colors, even when the configuration file gives the
default to color output.
Same as <code>--color=never</code>.</p>
</dd>
<dt class="hdlist1">-i</dt>
<dt class="hdlist1">--ignore-case</dt>
<dd>
<p>Sorting and filtering branches are case insensitive.</p>
</dd>
<dt class="hdlist1">--column[=&lt;options&gt;]</dt>
<dt class="hdlist1">--no-column</dt>
<dd>
<p>Display branch listing in columns. See configuration variable
<code>column.branch</code> for option syntax. <code>--column</code> and <code>--no-column</code>
without options are equivalent to <em>always</em> and <em>never</em> respectively.</p>
<div class="paragraph">
<p>This option is only applicable in non-verbose mode.</p>
</div>
</dd>
<dt class="hdlist1">-r</dt>
<dt class="hdlist1">--remotes</dt>
<dd>
<p>List or delete (if used with -d) the remote-tracking branches.
Combine with <code>--list</code> to match the optional pattern(s).</p>
</dd>
<dt class="hdlist1">-a</dt>
<dt class="hdlist1">--all</dt>
<dd>
<p>List both remote-tracking branches and local branches.
Combine with <code>--list</code> to match optional pattern(s).</p>
</dd>
<dt class="hdlist1">-l</dt>
<dt class="hdlist1">--list</dt>
<dd>
<p>List branches.  With optional <code>&lt;pattern&gt;...</code>, e.g. <code>git
branch --list 'maint-*'</code>, list only the branches that match
the pattern(s).</p>
</dd>
<dt class="hdlist1">--show-current</dt>
<dd>
<p>Print the name of the current branch. In detached HEAD state,
nothing is printed.</p>
</dd>
<dt class="hdlist1">-v</dt>
<dt class="hdlist1">-vv</dt>
<dt class="hdlist1">--verbose</dt>
<dd>
<p>When in list mode,
show sha1 and commit subject line for each head, along with
relationship to upstream branch (if any). If given twice, print
the path of the linked worktree (if any) and the name of the upstream
branch, as well (see also <code>git remote show &lt;remote&gt;</code>).  Note that the
current worktree&#8217;s HEAD will not have its path printed (it will always
be your current directory).</p>
</dd>
<dt class="hdlist1">-q</dt>
<dt class="hdlist1">--quiet</dt>
<dd>
<p>Be more quiet when creating or deleting a branch, suppressing
non-error messages.</p>
</dd>
<dt class="hdlist1">--abbrev=&lt;n&gt;</dt>
<dd>
<p>In the verbose listing that show the commit object name,
show the shortest prefix that is at least <em>&lt;n&gt;</em> hexdigits
long that uniquely refers the object.
The default value is 7 and can be overridden by the <code>core.abbrev</code>
config option.</p>
</dd>
<dt class="hdlist1">--no-abbrev</dt>
<dd>
<p>Display the full sha1s in the output listing rather than abbreviating them.</p>
</dd>
<dt class="hdlist1">-t</dt>
<dt class="hdlist1">--track[=(direct|inherit)]</dt>
<dd>
<p>When creating a new branch, set up <code>branch.&lt;name&gt;.remote</code> and
<code>branch.&lt;name&gt;.merge</code> configuration entries to set "upstream" tracking
configuration for the new branch. This
configuration will tell git to show the relationship between the
two branches in <code>git status</code> and <code>git branch -v</code>. Furthermore,
it directs <code>git pull</code> without arguments to pull from the
upstream when the new branch is checked out.</p>
<div class="paragraph">
<p>The exact upstream branch is chosen depending on the optional argument:
<code>-t</code>, <code>--track</code>, or <code>--track=direct</code> means to use the start-point branch
itself as the upstream; <code>--track=inherit</code> means to copy the upstream
configuration of the start-point branch.</p>
</div>
<div class="paragraph">
<p><code>--track=direct</code> is the default when the start point is a remote-tracking branch.
Set the branch.autoSetupMerge configuration variable to <code>false</code> if you
want <code>git switch</code>, <code>git checkout</code> and <code>git branch</code> to always behave as if <code>--no-track</code>
were given. Set it to <code>always</code> if you want this behavior when the
start-point is either a local or remote-tracking branch. Set it to
<code>inherit</code> if you want to copy the tracking configuration from the
branch point.</p>
</div>
<div class="paragraph">
<p>See <a href="git-pull.html">git-pull(1)</a> and <a href="git-config.html">git-config(1)</a> for additional discussion on
how the <code>branch.&lt;name&gt;.remote</code> and <code>branch.&lt;name&gt;.merge</code> options are used.</p>
</div>
</dd>
<dt class="hdlist1">--no-track</dt>
<dd>
<p>Do not set up "upstream" configuration, even if the
branch.autoSetupMerge configuration variable is set.</p>
</dd>
<dt class="hdlist1">--recurse-submodules</dt>
<dd>
<p>THIS OPTION IS EXPERIMENTAL! Causes the current command to
recurse into submodules if <code>submodule.propagateBranches</code> is
enabled. See <code>submodule.propagateBranches</code> in
<a href="git-config.html">git-config(1)</a>. Currently, only branch creation is
supported.</p>
<div class="paragraph">
<p>When used in branch creation, a new branch &lt;branchname&gt; will be created
in the superproject and all of the submodules in the superproject&#8217;s
&lt;start-point&gt;. In submodules, the branch will point to the submodule
commit in the superproject&#8217;s &lt;start-point&gt; but the branch&#8217;s tracking
information will be set up based on the submodule&#8217;s branches and remotes
e.g. <code>git branch --recurse-submodules topic origin/main</code> will create the
submodule branch "topic" that points to the submodule commit in the
superproject&#8217;s "origin/main", but tracks the submodule&#8217;s "origin/main".</p>
</div>
</dd>
<dt class="hdlist1">--set-upstream</dt>
<dd>
<p>As this option had confusing syntax, it is no longer supported.
Please use <code>--track</code> or <code>--set-upstream-to</code> instead.</p>
</dd>
<dt class="hdlist1">-u &lt;upstream&gt;</dt>
<dt class="hdlist1">--set-upstream-to=&lt;upstream&gt;</dt>
<dd>
<p>Set up &lt;branchname&gt;'s tracking information so &lt;upstream&gt; is
considered &lt;branchname&gt;'s upstream branch. If no &lt;branchname&gt;
is specified, then it defaults to the current branch.</p>
</dd>
<dt class="hdlist1">--unset-upstream</dt>
<dd>
<p>Remove the upstream information for &lt;branchname&gt;. If no branch
is specified it defaults to the current branch.</p>
</dd>
<dt class="hdlist1">--edit-description</dt>
<dd>
<p>Open an editor and edit the text to explain what the branch is
for, to be used by various other commands (e.g. <code>format-patch</code>,
<code>request-pull</code>, and <code>merge</code> (if enabled)). Multi-line explanations
may be used.</p>
</dd>
<dt class="hdlist1">--contains [&lt;commit&gt;]</dt>
<dd>
<p>Only list branches which contain the specified commit (HEAD
if not specified). Implies <code>--list</code>.</p>
</dd>
<dt class="hdlist1">--no-contains [&lt;commit&gt;]</dt>
<dd>
<p>Only list branches which don&#8217;t contain the specified commit
(HEAD if not specified). Implies <code>--list</code>.</p>
</dd>
<dt class="hdlist1">--merged [&lt;commit&gt;]</dt>
<dd>
<p>Only list branches whose tips are reachable from the
specified commit (HEAD if not specified). Implies <code>--list</code>.</p>
</dd>
<dt class="hdlist1">--no-merged [&lt;commit&gt;]</dt>
<dd>
<p>Only list branches whose tips are not reachable from the
specified commit (HEAD if not specified). Implies <code>--list</code>.</p>
</dd>
<dt class="hdlist1">&lt;branchname&gt;</dt>
<dd>
<p>The name of the branch to create or delete.
The new branch name must pass all checks defined by
<a href="git-check-ref-format.html">git-check-ref-format(1)</a>.  Some of these checks
may restrict the characters allowed in a branch name.</p>
</dd>
<dt class="hdlist1">&lt;start-point&gt;</dt>
<dd>
<p>The new branch head will point to this commit.  It may be
given as a branch name, a commit-id, or a tag.  If this
option is omitted, the current HEAD will be used instead.</p>
</dd>
<dt class="hdlist1">&lt;oldbranch&gt;</dt>
<dd>
<p>The name of an existing branch to rename.</p>
</dd>
<dt class="hdlist1">&lt;newbranch&gt;</dt>
<dd>
<p>The new name for an existing branch. The same restrictions as for
&lt;branchname&gt; apply.</p>
</dd>
<dt class="hdlist1">--sort=&lt;key&gt;</dt>
<dd>
<p>Sort based on the key given. Prefix <code>-</code> to sort in descending
order of the value. You may use the --sort=&lt;key&gt; option
multiple times, in which case the last key becomes the primary
key. The keys supported are the same as those in <code>git
for-each-ref</code>. Sort order defaults to the value configured for the
<code>branch.sort</code> variable if exists, or to sorting based on the
full refname (including <code>refs/...</code> prefix). This lists
detached HEAD (if present) first, then local branches and
finally remote-tracking branches. See <a href="git-config.html">git-config(1)</a>.</p>
</dd>
<dt class="hdlist1">--points-at &lt;object&gt;</dt>
<dd>
<p>Only list branches of the given object.</p>
</dd>
<dt class="hdlist1">--format &lt;format&gt;</dt>
<dd>
<p>A string that interpolates <code>%(fieldname)</code> from a branch ref being shown
and the object it points at.  The format is the same as
that of <a href="git-for-each-ref.html">git-for-each-ref(1)</a>.</p>
</dd>
</dl>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_configuration">CONFIGURATION</h2>
<div class="sectionbody">
<div class="paragraph">
<p><code>pager.branch</code> is only respected when listing branches, i.e., when
<code>--list</code> is used or implied. The default is to use a pager.
See <a href="git-config.html">git-config(1)</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_examples">EXAMPLES</h2>
<div class="sectionbody">
<div class="dlist">
<dl>
<dt class="hdlist1">Start development from a known tag</dt>
<dd>
<div class="listingblock">
<div class="content">
<pre>$ git clone git://git.kernel.org/pub/scm/.../linux-2.6 my2.6
$ cd my2.6
$ git branch my2.6.14 v2.6.14   <b class="conum">(1)</b>
$ git switch my2.6.14</pre>
</div>
</div>
<div class="colist arabic">
<ol>
<li>
<p>This step and the next one could be combined into a single step with
"checkout -b my2.6.14 v2.6.14".</p>
</li>
</ol>
</div>
</dd>
<dt class="hdlist1">Delete an unneeded branch</dt>
<dd>
<div class="listingblock">
<div class="content">
<pre>$ git clone git://git.kernel.org/.../git.git my.git
$ cd my.git
$ git branch -d -r origin/todo origin/html origin/man   <b class="conum">(1)</b>
$ git branch -D test                                    <b class="conum">(2)</b></pre>
</div>
</div>
<div class="colist arabic">
<ol>
<li>
<p>Delete the remote-tracking branches "todo", "html" and "man". The next
<em>fetch</em> or <em>pull</em> will create them again unless you configure them not to.
See <a href="git-fetch.html">git-fetch(1)</a>.</p>
</li>
<li>
<p>Delete the "test" branch even if the "master" branch (or whichever branch
is currently checked out) does not have all commits from the test branch.</p>
</li>
</ol>
</div>
</dd>
<dt class="hdlist1">Listing branches from a specific remote</dt>
<dd>
<div class="listingblock">
<div class="content">
<pre>$ git branch -r -l '&lt;remote&gt;/&lt;pattern&gt;'                 <b class="conum">(1)</b>
$ git for-each-ref 'refs/remotes/&lt;remote&gt;/&lt;pattern&gt;'    <b class="conum">(2)</b></pre>
</div>
</div>
<div class="colist arabic">
<ol>
<li>
<p>Using <code>-a</code> would conflate &lt;remote&gt; with any local branches you happen to
have been prefixed with the same &lt;remote&gt; pattern.</p>
</li>
<li>
<p><code>for-each-ref</code> can take a wide range of options. See <a href="git-for-each-ref.html">git-for-each-ref(1)</a></p>
</li>
</ol>
</div>
</dd>
</dl>
</div>
<div class="paragraph">
<p>Patterns will normally need quoting.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_notes">NOTES</h2>
<div class="sectionbody">
<div class="paragraph">
<p>If you are creating a branch that you want to switch to immediately,
it is easier to use the "git switch" command with its <code>-c</code> option to
do the same thing with a single command.</p>
</div>
<div class="paragraph">
<p>The options <code>--contains</code>, <code>--no-contains</code>, <code>--merged</code> and <code>--no-merged</code>
serve four related but different purposes:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>--contains &lt;commit&gt;</code> is used to find all branches which will need
special attention if &lt;commit&gt; were to be rebased or amended, since those
branches contain the specified &lt;commit&gt;.</p>
</li>
<li>
<p><code>--no-contains &lt;commit&gt;</code> is the inverse of that, i.e. branches that don&#8217;t
contain the specified &lt;commit&gt;.</p>
</li>
<li>
<p><code>--merged</code> is used to find all branches which can be safely deleted,
since those branches are fully contained by HEAD.</p>
</li>
<li>
<p><code>--no-merged</code> is used to find branches which are candidates for merging
into HEAD, since those branches are not fully contained by HEAD.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>When combining multiple <code>--contains</code> and <code>--no-contains</code> filters, only
references that contain at least one of the <code>--contains</code> commits and
contain none of the <code>--no-contains</code> commits are shown.</p>
</div>
<div class="paragraph">
<p>When combining multiple <code>--merged</code> and <code>--no-merged</code> filters, only
references that are reachable from at least one of the <code>--merged</code>
commits and from none of the <code>--no-merged</code> commits are shown.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_see_also">SEE ALSO</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a href="git-check-ref-format.html">git-check-ref-format(1)</a>,
<a href="git-fetch.html">git-fetch(1)</a>,
<a href="git-remote.html">git-remote(1)</a>,
<a href="user-manual.html#what-is-a-branch">&#8220;Understanding history: What is
a branch?&#8221;</a> in the Git User&#8217;s Manual.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_git">GIT</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Part of the <a href="git.html">git(1)</a> suite</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>