#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速AnimSequence统计工具
"""

import unreal


def quick_anim_sequence_stats():
    """
    快速统计当前选中资产中的AnimSequence和特定关键字
    """
    # 目标关键字
    keywords = ['P_dflt', 'LQCUSTOM_', 'A_Vehicles', 'NPC_Attack', 'Meleecombat', 'A_N']
    
    try:
        # 获取选中的资产
        selected_assets = unreal.EditorUtilityLibrary.get_selected_assets()
        
        if not selected_assets:
            print("没有选中任何资产")
            return
        
        # 获取资产注册表
        asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        
        # 初始化计数器
        total_assets = len(selected_assets)
        anim_sequence_count = 0
        referenced_anim_count = 0
        keyword_counts = {keyword: 0 for keyword in keywords}
        
        print(f"正在分析 {total_assets} 个选中的资产...")
        
        # 遍历所有选中资产
        for asset in selected_assets:
            asset_name = asset.get_name()
            asset_path = asset.get_path_name()
            
            # 检查是否为AnimSequence
            is_anim_sequence = isinstance(asset, unreal.AnimSequence)
            if is_anim_sequence:
                anim_sequence_count += 1
                
                # 检查是否被引用
                try:
                    asset_data = asset_registry.get_asset_by_object_path(asset_path)
                    if asset_data:
                        referencers = asset_registry.get_referencers(
                            asset_data.package_name,
                            unreal.AssetRegistryDependencyOptions()
                        )
                        if len(referencers) > 0:
                            referenced_anim_count += 1
                except:
                    pass
            
            # 检查关键字
            for keyword in keywords:
                if keyword.lower() in asset_name.lower():
                    keyword_counts[keyword] += 1
        
        # 打印结果
        print("\n" + "="*50)
        print("统计结果")
        print("="*50)
        print(f"总资产数量: {total_assets}")
        print(f"AnimSequence总数: {anim_sequence_count}")
        print(f"被引用的AnimSequence: {referenced_anim_count}")
        print(f"未被引用的AnimSequence: {anim_sequence_count - referenced_anim_count}")
        
        if anim_sequence_count > 0:
            ref_percentage = (referenced_anim_count / anim_sequence_count) * 100
            print(f"AnimSequence引用率: {ref_percentage:.1f}%")
        
        print("\n关键字统计:")
        print("-" * 30)
        for keyword, count in keyword_counts.items():
            print(f"包含 '{keyword}' 的资产: {count}")
        
        print("\n统计完成!")
        
    except Exception as e:
        print(f"统计过程中出错: {e}")


def get_anim_sequence_details():
    """
    获取AnimSequence的详细信息
    """
    try:
        selected_assets = unreal.EditorUtilityLibrary.get_selected_assets()
        asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        
        anim_sequences = []
        
        for asset in selected_assets:
            if isinstance(asset, unreal.AnimSequence):
                asset_name = asset.get_name()
                asset_path = asset.get_path_name()
                
                # 检查引用状态
                is_referenced = False
                try:
                    asset_data = asset_registry.get_asset_by_object_path(asset_path)
                    if asset_data:
                        referencers = asset_registry.get_referencers(
                            asset_data.package_name,
                            unreal.AssetRegistryDependencyOptions()
                        )
                        is_referenced = len(referencers) > 0
                except:
                    pass
                
                anim_sequences.append({
                    'name': asset_name,
                    'path': asset_path,
                    'is_referenced': is_referenced
                })
        
        # 打印详细信息
        print(f"\nAnimSequence详细列表 (共 {len(anim_sequences)} 个):")
        print("-" * 60)
        
        referenced_list = [anim for anim in anim_sequences if anim['is_referenced']]
        unreferenced_list = [anim for anim in anim_sequences if not anim['is_referenced']]
        
        if referenced_list:
            print(f"\n被引用的AnimSequence ({len(referenced_list)} 个):")
            for anim in referenced_list:
                print(f"  ✓ {anim['name']}")
        
        if unreferenced_list:
            print(f"\n未被引用的AnimSequence ({len(unreferenced_list)} 个):")
            for anim in unreferenced_list:
                print(f"  ✗ {anim['name']}")
        
        return anim_sequences
        
    except Exception as e:
        print(f"获取详细信息时出错: {e}")
        return []


def check_keyword_assets(keyword):
    """
    检查包含特定关键字的资产
    
    Args:
        keyword (str): 要搜索的关键字
    """
    try:
        selected_assets = unreal.EditorUtilityLibrary.get_selected_assets()
        
        matching_assets = []
        for asset in selected_assets:
            asset_name = asset.get_name()
            if keyword.lower() in asset_name.lower():
                matching_assets.append({
                    'name': asset_name,
                    'path': asset.get_path_name(),
                    'type': type(asset).__name__
                })
        
        print(f"\n包含 '{keyword}' 的资产 (共 {len(matching_assets)} 个):")
        print("-" * 50)
        
        for asset in matching_assets:
            print(f"  {asset['name']} ({asset['type']})")
        
        return matching_assets
        
    except Exception as e:
        print(f"搜索关键字时出错: {e}")
        return []


# 直接运行统计
if __name__ == "__main__":
    quick_anim_sequence_stats()
    
    # 可选：显示详细信息
    show_details = input("\n是否显示AnimSequence详细信息? (y/n): ").lower().strip()
    if show_details in ['y', 'yes']:
        get_anim_sequence_details()
