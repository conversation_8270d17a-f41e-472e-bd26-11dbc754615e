#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线K帧编辑器 - MVC架构版本
主入口文件，展示如何使用MVC架构的贝塞尔曲线编辑器
"""

import sys
from PySide2 import QtWidgets, QtCore
from controller import BezierCurveMainWindow


class BezierCurveEditorApp(QtWidgets.QApplication):
    """贝塞尔曲线编辑器应用程序"""
    
    def __init__(self, sys_argv):
        super(BezierCurveEditorApp, self).__init__(sys_argv)
        
        # 设置应用程序属性
        self.setApplicationName("贝塞尔曲线K帧编辑器")
        self.setApplicationVersion("2.0 (MVC架构)")
        self.setOrganizationName("LSR Animation Tools")
        
        # 创建主窗口
        self.main_window = BezierCurveMainWindow()
        
        # 设置样式
        self.setup_style()
        
        # 显示窗口
        self.main_window.show()
    
    def setup_style(self):
        """设置应用程序样式"""
        style = """
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 11px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 60px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 80px;
        }
        
        QComboBox::drop-down {
            border: none;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QLabel {
            color: #cccccc;
        }
        
        QFormLayout QLabel {
            color: #ffffff;
            font-weight: bold;
        }
        
        /* 滚动条样式 */
        QScrollBar:vertical {
            background-color: #404040;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #606060;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #707070;
        }
        """
        
        self.setStyleSheet(style)


def create_standalone_editor():
    """创建独立的编辑器窗口"""
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)
    
    # 创建主窗口
    editor = BezierCurveMainWindow()
    editor.setWindowTitle("贝塞尔曲线K帧编辑器 (MVC架构)")
    editor.resize(1200, 800)
    
    # 设置样式
    app_instance = BezierCurveEditorApp([])
    app_instance.setup_style()
    
    editor.show()
    return editor


def get_controller_for_integration():
    """获取控制器实例，用于集成到其他应用中"""
    from controller import BezierCurveController
    return BezierCurveController()


def demo_mvc_usage():
    """演示MVC架构的使用方法"""
    print("=== 贝塞尔曲线编辑器 MVC架构演示 ===")
    print()
    print("架构说明:")
    print("📁 model.py - 数据模型层")
    print("  ├── BezierPoint: 关键点数据结构")
    print("  └── BezierCurveModel: 曲线数据管理、业务逻辑、撤销系统")
    print()
    print("📁 view.py - 视图层")
    print("  ├── BezierCurveWidget: 曲线绘制和交互")
    print("  └── KeyframePropertiesWidget: 属性编辑面板")
    print()
    print("📁 controller.py - 控制器层")
    print("  ├── BezierCurveController: 协调Model和View")
    print("  └── BezierCurveMainWindow: 主窗口整合")
    print()
    print("优势:")
    print("✅ 职责分离 - 数据、视图、逻辑分离")
    print("✅ 易于测试 - 各层可独立测试")
    print("✅ 易于扩展 - 可轻松添加新功能")
    print("✅ 易于维护 - 修改一层不影响其他层")
    print("✅ 可重用性 - 组件可在其他项目中重用")
    print()
    print("使用方法:")
    print("1. 独立使用: python bezier_curve_editor_mvc.py")
    print("2. 集成使用: controller = get_controller_for_integration()")
    print("3. 自定义UI: 直接使用Model和View组件")


if __name__ == '__main__':
    # 显示架构说明
    demo_mvc_usage()
    print()
    
    # 创建并运行应用程序
    app = BezierCurveEditorApp(sys.argv)
    
    # 添加欢迎信息
    QtWidgets.QMessageBox.information(
        app.main_window,
        "MVC架构版本",
        "🎉 欢迎使用贝塞尔曲线K帧编辑器 (MVC架构版本)!\n\n"
        "✨ 新特性:\n"
        "• 采用MVC架构设计\n"
        "• 更好的代码组织和可维护性\n"
        "• 易于扩展和集成\n"
        "• 完整的撤销/重做系统\n"
        "• 实时属性编辑\n\n"
        "📖 使用说明:\n"
        "• 左键点击添加关键帧\n"
        "• 拖拽关键帧和切线调整曲线\n"
        "• 右侧面板精确编辑属性\n"
        "• 支持完整的键盘快捷键\n\n"
        "🔧 开发友好:\n"
        "• Model层: 纯数据和业务逻辑\n"
        "• View层: UI显示和用户交互\n"
        "• Controller层: 协调和控制逻辑"
    )
    
    sys.exit(app.exec_())
