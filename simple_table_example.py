#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的TableView使用示例
"""

from PySide2 import QtWidgets, QtCore
from custom_table_view import CustomTableView


class SimpleTableExample(QtWidgets.QWidget):
    """简单的表格示例"""
    
    def __init__(self):
        super(SimpleTableExample, self).__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QVBoxLayout()
        
        # 创建表格
        self.table = CustomTableView()
        
        # 定义表头 - 这是关键部分
        headers = [
            {
                'title': '骨骼名称',           # 列标题
                'editor_type': 'lineedit',    # 编辑器类型：文本框
                'width': 150,                 # 列宽
                'default': 'Bone_01'          # 默认值
            },
            {
                'title': '类型',
                'editor_type': 'combobox',    # 编辑器类型：下拉框
                'items': ['Root', 'Spine', 'Arm', 'Leg', 'Hand', 'Foot'],  # 下拉选项
                'width': 100,
                'default': 'Spine'
            },
            {
                'title': '权重',
                'editor_type': 'doublespinbox',  # 编辑器类型：浮点数输入框
                'min_value': 0.0,
                'max_value': 1.0,
                'decimals': 3,                   # 小数位数
                'width': 80,
                'default': 1.0
            },
            {
                'title': '启用',
                'editor_type': 'combobox',
                'items': ['是', '否'],
                'width': 60,
                'default': '是'
            }
        ]
        
        # 设置表头配置
        self.table.set_headers(headers)
        
        # 设置初始数据
        initial_data = [
            ['Root_Bone', 'Root', 1.0, '是'],
            ['Spine_01', 'Spine', 0.8, '是'],
            ['L_Arm_01', 'Arm', 0.6, '否'],
        ]
        self.table.set_data(initial_data)
        
        layout.addWidget(self.table)
        
        # 添加控制按钮
        btn_layout = QtWidgets.QHBoxLayout()
        
        add_btn = QtWidgets.QPushButton("添加骨骼")
        add_btn.clicked.connect(self.table.add_row)
        btn_layout.addWidget(add_btn)
        
        get_data_btn = QtWidgets.QPushButton("获取数据")
        get_data_btn.clicked.connect(self.get_table_data)
        btn_layout.addWidget(get_data_btn)
        
        btn_layout.addStretch()
        layout.addLayout(btn_layout)
        
        self.setLayout(layout)
        self.setWindowTitle("骨骼配置表格")
        self.resize(500, 400)
    
    def get_table_data(self):
        """获取表格数据"""
        data = self.table.get_data()
        print("当前表格数据:")
        for row in data:
            print(f"骨骼: {row[0]}, 类型: {row[1]}, 权重: {row[2]}, 启用: {row[3]}")


if __name__ == '__main__':
    import sys
    app = QtWidgets.QApplication(sys.argv)
    
    window = SimpleTableExample()
    window.show()
    
    sys.exit(app.exec_())
