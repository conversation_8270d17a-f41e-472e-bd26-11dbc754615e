#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的贝塞尔曲线编辑器
"""

import sys
from PySide2 import QtWidgets, QtCore
from bezier_curve_editor import BezierCurveEditor


def test_bezier_editor():
    """测试贝塞尔曲线编辑器"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建编辑器
    editor = BezierCurveEditor()
    editor.setWindowTitle("测试贝塞尔曲线编辑器 - 修复版本")
    
    # 设置样式
    editor.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 11px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 60px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 80px;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QLabel {
            color: #cccccc;
        }
    """)
    
    # 显示窗口
    editor.show()
    
    # 添加一些测试提示
    QtWidgets.QMessageBox.information(
        editor, 
        "使用说明", 
        "修复说明:\n"
        "- 已修复QPoint和QPointF类型不匹配的问题\n"
        "- 现在可以正常点击和拖拽关键帧\n"
        "- 可以正常调整切线控制点\n\n"
        "操作方法:\n"
        "- 左键点击空白处添加关键帧\n"
        "- 左键拖拽关键帧移动位置\n"
        "- 选中关键帧后拖拽黄色切线控制点\n"
        "- 右键点击关键帧删除\n"
        "- 使用右侧预设按钮快速设置曲线"
    )
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    test_bezier_editor()
