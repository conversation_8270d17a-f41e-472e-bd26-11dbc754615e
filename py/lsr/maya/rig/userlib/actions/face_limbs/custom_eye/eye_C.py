"""
Eye C Limb
"""

import maya.cmds as cmds
from lsr.maya.nodezoo.node import Joint

import lsr.protostar.core.parameter as pa

from lsr.maya.rig.userlib.actions.face_limbs.custom_eye.eye_B import Eye_B_Limb


class Eye_C_Limb(Eye_B_Limb):
    """
    Eye_C_Limb limb class
    Create a rig controller for eye B type

    :limb type: Eye_C_Limb
    """

    def marker_data(self):
        """Skip marker data as this limb depends on a pre-built
        joint hierarchy."""
        return

    def resolve_input_skeleton(self):
        """Re-implemented to find pivot joints from the main chain."""
        super(Eye_C_Limb, self).resolve_input_skeleton()

    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'eyeC')
        name_side = kwargs.get('name_side', 'L')
        cmds.select(deselect=True)
        joint_chain = []
        for i in range(2):
            jt_node = Joint.create(
                name='{}_{:02d}_{}_RIGJNT'.format(limb_name, i, name_side),
                p=(0, 0, i * 10))
            joint_chain.append(jt_node)
        joint_chain[0].orient_chain(
            orientJoint='zyx', secondaryAxisOrient='yup', zeroScaleOrient=True, children=True)

        joint_chain[1].jointOrient.value = (0, 0, 0)

        _joint_part = ['UpLid_00',
                       'UpLid_01',
                       'LowLid_00',
                       'LowLid_01',
                       'InnerLid',
                       'OuterLid']
        _joint_pos = [
            (-1, 2, 2),
            (1, 2, 2),
            (-1, -2, 2),
            (1, -2, 2),
            (-3, 0, 2),
            (3, 0, 2)
        ]

        for i, (part_name, pos_data) in enumerate(zip(_joint_part, _joint_pos)):
            cmds.select(deselect=True)

            jt_node = Joint.create(
                name='{}_{}_{}_RIGJNT'.format(
                    limb_name, part_name, name_side),
                p=pos_data)
            joint_chain.append(jt_node)
            jt_node.set_parent(joint_chain[0])

        template_data = [
            {'type': 18, 'otherType': 'EyeBall', 'overrideColor': 13},
            {'type': 18, 'otherType': 'EyeEnd', 'overrideColor': 1},
            {'type': 18, 'otherType': 'UpLid_00', 'overrideColor': 14},
            {'type': 18, 'otherType': 'UpLid_01', 'overrideColor': 14},
            {'type': 18, 'otherType': 'LowLid_00', 'overrideColor': 14},
            {'type': 18, 'otherType': 'LowLid_01', 'overrideColor': 14},
            {'type': 18, 'otherType': 'InnerLid', 'overrideColor': 14},
            {'type': 18, 'otherType': 'OuterLid', 'overrideColor': 14}
        ]

        for jni_index, jnt in enumerate(joint_chain):
            jnt.set_attr('overrideEnabled', True)
            jnt.set_attr('drawLabel', True)
            for key, value in template_data[jni_index].items():
                jnt.set_attr(key, value)

        joint_chain = [joint_chain[i] for i in [0, 2, 3, 4, 5, 6, 7, 1]]

        cmds.select(deselect=True)
        return joint_chain

    def run(self):
        """Builds the limb ctrl rig."""
        if len(self.group_exts.value) < 8:
            self.group_exts.value = self.groups

        super(Eye_C_Limb, self).run()
