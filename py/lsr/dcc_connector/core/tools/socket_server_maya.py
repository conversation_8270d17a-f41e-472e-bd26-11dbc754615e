import socket

from lsr.dcc_connector.utils.utils_action_operator import init_dcc_action_instance
from lsr.dcc_connector.utils.socket_data import MayaMobuSocketData, CommandMode


class SocketServerMaya(object):
    """
    This class is used to create a socket server in Maya.
    """
    def __init__(self, host='', port=7828, *args, **kwargs):
        self.HOST = host
        self.PORT = port
        self.SIZE = 2**20
        self.BACKLOG = 5
        self.s = socket.socket(socket.AF_INET, socket.SOCK_STREAM, socket.IPPROTO_TCP)

    def get_socket(self):
        """
        This function is used to get the socket.

        Returns:
            bool: True if the socket is created successfully, False otherwise.
        """
        try:
            self.s.bind((self.HOST, self.PORT))
            self.s.listen(self.BACKLOG)
            print('Socket Bind successfully. %s' % self.PORT)
            return True
        except socket.error as msg:
            print(
                "Tried to open port %s, but failed: It is probably already open. \n" %
                self.PORT)
            return False


class MayaMobuCommands(object):
    """
    This class is used to process the command from Mobu to Maya.
    """

    def __init__(self, data, *args, **kwargs):
        """
        This function is used to initialize the MayaMobuCommands class.

        Args:
            data (MayaMobuSocketData): The MayaMobuSocketData object.
        """
        self.SocketData = data
        self.characters = []
        self.dcc_action_instance = init_dcc_action_instance('DccRetargetApply')
        if not self.dcc_action_instance:
            raise ValueError("DccRetargetApply instance is None.")

    def processCommand(self, *args, **kwargs):
        """
        This function is used to process the command from Mobu to Maya.

        Returns:

        """
        print(self.SocketData.commandType)
        if self.SocketData.commandType == CommandMode.import_cmd:
            print('Start Getting Valid Characters.')
            if not self.dcc_action_instance:
                print("[DccRetargetApply] : DccRetargetApply instance is not initialized.")
                return []
            return self.dcc_action_instance.get_character_list()

        elif self.SocketData.commandType == CommandMode.export_cmd:
            print('Receiving custom rig animation data.')
            if not self.dcc_action_instance:
                print("[DccRetargetApply] : DccRetargetApply instance is not initialized.")
                return []
            return self.dcc_action_instance.retarget_anim(self.SocketData)

        else:
            print('There is no valid command need to be implemented.')
            return []
