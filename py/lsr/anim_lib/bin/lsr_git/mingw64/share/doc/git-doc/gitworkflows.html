<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>gitworkflows(7)</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="manpage">
<div id="header">
<h1>gitworkflows(7) Manual Page</h1>
<h2 id="_name">NAME</h2>
<div class="sectionbody">
<p>gitworkflows - An overview of recommended workflows with Git</p>
</div>
</div>
<div id="content">
<div class="sect1">
<h2 id="_synopsis">SYNOPSIS</h2>
<div class="sectionbody">
<div class="verseblock">
<pre class="content">git *</pre>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_description">DESCRIPTION</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This document attempts to write down and motivate some of the workflow
elements used for <code>git.git</code> itself.  Many ideas apply in general,
though the full workflow is rarely required for smaller projects with
fewer people involved.</p>
</div>
<div class="paragraph">
<p>We formulate a set of <em>rules</em> for quick reference, while the prose
tries to motivate each of them.  Do not always take them literally;
you should value good reasons for your actions higher than manpages
such as this one.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_separate_changes">SEPARATE CHANGES</h2>
<div class="sectionbody">
<div class="paragraph">
<p>As a general rule, you should try to split your changes into small
logical steps, and commit each of them.  They should be consistent,
working independently of any later commits, pass the test suite, etc.
This makes the review process much easier, and the history much more
useful for later inspection and analysis, for example with
<a href="git-blame.html">git-blame(1)</a> and <a href="git-bisect.html">git-bisect(1)</a>.</p>
</div>
<div class="paragraph">
<p>To achieve this, try to split your work into small steps from the very
beginning. It is always easier to squash a few commits together than
to split one big commit into several.  Don&#8217;t be afraid of making too
small or imperfect steps along the way. You can always go back later
and edit the commits with <code>git rebase --interactive</code> before you
publish them.  You can use <code>git stash push --keep-index</code> to run the
test suite independent of other uncommitted changes; see the EXAMPLES
section of <a href="git-stash.html">git-stash(1)</a>.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_managing_branches">MANAGING BRANCHES</h2>
<div class="sectionbody">
<div class="paragraph">
<p>There are two main tools that can be used to include changes from one
branch on another: <a href="git-merge.html">git-merge(1)</a> and
<a href="git-cherry-pick.html">git-cherry-pick(1)</a>.</p>
</div>
<div class="paragraph">
<p>Merges have many advantages, so we try to solve as many problems as
possible with merges alone.  Cherry-picking is still occasionally
useful; see "Merging upwards" below for an example.</p>
</div>
<div class="paragraph">
<p>Most importantly, merging works at the branch level, while
cherry-picking works at the commit level.  This means that a merge can
carry over the changes from 1, 10, or 1000 commits with equal ease,
which in turn means the workflow scales much better to a large number
of contributors (and contributions).  Merges are also easier to
understand because a merge commit is a "promise" that all changes from
all its parents are now included.</p>
</div>
<div class="paragraph">
<p>There is a tradeoff of course: merges require a more careful branch
management.  The following subsections discuss the important points.</p>
</div>
<div class="sect2">
<h3 id="_graduation">Graduation</h3>
<div class="paragraph">
<p>As a given feature goes from experimental to stable, it also
"graduates" between the corresponding branches of the software.
<code>git.git</code> uses the following <em>integration branches</em>:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><em>maint</em> tracks the commits that should go into the next "maintenance
release", i.e., update of the last released stable version;</p>
</li>
<li>
<p><em>master</em> tracks the commits that should go into the next release;</p>
</li>
<li>
<p><em>next</em> is intended as a testing branch for topics being tested for
stability for master.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>There is a fourth official branch that is used slightly differently:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><em>seen</em> (patches seen by the maintainer) is an integration branch for
things that are not quite ready for inclusion yet (see "Integration
Branches" below).</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Each of the four branches is usually a direct descendant of the one
above it.</p>
</div>
<div class="paragraph">
<p>Conceptually, the feature enters at an unstable branch (usually <em>next</em>
or <em>seen</em>), and "graduates" to <em>master</em> for the next release once it is
considered stable enough.</p>
</div>
</div>
<div class="sect2">
<h3 id="_merging_upwards">Merging upwards</h3>
<div class="paragraph">
<p>The "downwards graduation" discussed above cannot be done by actually
merging downwards, however, since that would merge <em>all</em> changes on
the unstable branch into the stable one.  Hence the following:</p>
</div>
<div class="exampleblock">
<div class="title">Rule: Merge upwards</div>
<div class="content">
<div class="paragraph">
<p>Always commit your fixes to the oldest supported branch that requires
them.  Then (periodically) merge the integration branches upwards into each
other.</p>
</div>
</div>
</div>
<div class="paragraph">
<p>This gives a very controlled flow of fixes.  If you notice that you
have applied a fix to e.g. <em>master</em> that is also required in <em>maint</em>,
you will need to cherry-pick it (using <a href="git-cherry-pick.html">git-cherry-pick(1)</a>)
downwards.  This will happen a few times and is nothing to worry about
unless you do it very frequently.</p>
</div>
</div>
<div class="sect2">
<h3 id="_topic_branches">Topic branches</h3>
<div class="paragraph">
<p>Any nontrivial feature will require several patches to implement, and
may get extra bugfixes or improvements during its lifetime.</p>
</div>
<div class="paragraph">
<p>Committing everything directly on the integration branches leads to many
problems: Bad commits cannot be undone, so they must be reverted one
by one, which creates confusing histories and further error potential
when you forget to revert part of a group of changes.  Working in
parallel mixes up the changes, creating further confusion.</p>
</div>
<div class="paragraph">
<p>Use of "topic branches" solves these problems.  The name is pretty
self explanatory, with a caveat that comes from the "merge upwards"
rule above:</p>
</div>
<div class="exampleblock">
<div class="title">Rule: Topic branches</div>
<div class="content">
<div class="paragraph">
<p>Make a side branch for every topic (feature, bugfix, &#8230;&#8203;). Fork it off
at the oldest integration branch that you will eventually want to merge it
into.</p>
</div>
</div>
</div>
<div class="paragraph">
<p>Many things can then be done very naturally:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>To get the feature/bugfix into an integration branch, simply merge
it.  If the topic has evolved further in the meantime, merge again.
(Note that you do not necessarily have to merge it to the oldest
integration branch first.  For example, you can first merge a bugfix
to <em>next</em>, give it some testing time, and merge to <em>maint</em> when you
know it is stable.)</p>
</li>
<li>
<p>If you find you need new features from the branch <em>other</em> to continue
working on your topic, merge <em>other</em> to <em>topic</em>.  (However, do not
do this "just habitually", see below.)</p>
</li>
<li>
<p>If you find you forked off the wrong branch and want to move it
"back in time", use <a href="git-rebase.html">git-rebase(1)</a>.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Note that the last point clashes with the other two: a topic that has
been merged elsewhere should not be rebased.  See the section on
RECOVERING FROM UPSTREAM REBASE in <a href="git-rebase.html">git-rebase(1)</a>.</p>
</div>
<div class="paragraph">
<p>We should point out that "habitually" (regularly for no real reason)
merging an integration branch into your topics&#8201;&#8212;&#8201;and by extension,
merging anything upstream into anything downstream on a regular basis&#8201;&#8212;&#8201;is frowned upon:</p>
</div>
<div class="exampleblock">
<div class="title">Rule: Merge to downstream only at well-defined points</div>
<div class="content">
<div class="paragraph">
<p>Do not merge to downstream except with a good reason: upstream API
changes affect your branch; your branch no longer merges to upstream
cleanly; etc.</p>
</div>
</div>
</div>
<div class="paragraph">
<p>Otherwise, the topic that was merged to suddenly contains more than a
single (well-separated) change.  The many resulting small merges will
greatly clutter up history.  Anyone who later investigates the history
of a file will have to find out whether that merge affected the topic
in development.  An upstream might even inadvertently be merged into a
"more stable" branch.  And so on.</p>
</div>
</div>
<div class="sect2">
<h3 id="_throw_away_integration">Throw-away integration</h3>
<div class="paragraph">
<p>If you followed the last paragraph, you will now have many small topic
branches, and occasionally wonder how they interact.  Perhaps the
result of merging them does not even work?  But on the other hand, we
want to avoid merging them anywhere "stable" because such merges
cannot easily be undone.</p>
</div>
<div class="paragraph">
<p>The solution, of course, is to make a merge that we can undo: merge
into a throw-away branch.</p>
</div>
<div class="exampleblock">
<div class="title">Rule: Throw-away integration branches</div>
<div class="content">
<div class="paragraph">
<p>To test the interaction of several topics, merge them into a
throw-away branch.  You must never base any work on such a branch!</p>
</div>
</div>
</div>
<div class="paragraph">
<p>If you make it (very) clear that this branch is going to be deleted
right after the testing, you can even publish this branch, for example
to give the testers a chance to work with it, or other developers a
chance to see if their in-progress work will be compatible.  <code>git.git</code>
has such an official throw-away integration branch called <em>seen</em>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_branch_management_for_a_release">Branch management for a release</h3>
<div class="paragraph">
<p>Assuming you are using the merge approach discussed above, when you
are releasing your project you will need to do some additional branch
management work.</p>
</div>
<div class="paragraph">
<p>A feature release is created from the <em>master</em> branch, since <em>master</em>
tracks the commits that should go into the next feature release.</p>
</div>
<div class="paragraph">
<p>The <em>master</em> branch is supposed to be a superset of <em>maint</em>. If this
condition does not hold, then <em>maint</em> contains some commits that
are not included on <em>master</em>. The fixes represented by those commits
will therefore not be included in your feature release.</p>
</div>
<div class="paragraph">
<p>To verify that <em>master</em> is indeed a superset of <em>maint</em>, use git log:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Verify <em>master</em> is a superset of <em>maint</em></div>
<div class="content">
<div class="paragraph">
<p><code>git log master..maint</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>This command should not list any commits.  Otherwise, check out
<em>master</em> and merge <em>maint</em> into it.</p>
</div>
<div class="paragraph">
<p>Now you can proceed with the creation of the feature release. Apply a
tag to the tip of <em>master</em> indicating the release version:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Release tagging</div>
<div class="content">
<div class="paragraph">
<p><code>git tag -s -m "Git X.Y.Z" vX.Y.Z master</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>You need to push the new tag to a public Git server (see
"DISTRIBUTED WORKFLOWS" below). This makes the tag available to
others tracking your project. The push could also trigger a
post-update hook to perform release-related items such as building
release tarballs and preformatted documentation pages.</p>
</div>
<div class="paragraph">
<p>Similarly, for a maintenance release, <em>maint</em> is tracking the commits
to be released. Therefore, in the steps above simply tag and push
<em>maint</em> rather than <em>master</em>.</p>
</div>
</div>
<div class="sect2">
<h3 id="_maintenance_branch_management_after_a_feature_release">Maintenance branch management after a feature release</h3>
<div class="paragraph">
<p>After a feature release, you need to manage your maintenance branches.</p>
</div>
<div class="paragraph">
<p>First, if you wish to continue to release maintenance fixes for the
feature release made before the recent one, then you must create
another branch to track commits for that previous release.</p>
</div>
<div class="paragraph">
<p>To do this, the current maintenance branch is copied to another branch
named with the previous release version number (e.g. maint-X.Y.(Z-1)
where X.Y.Z is the current release).</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Copy maint</div>
<div class="content">
<div class="paragraph">
<p><code>git branch maint-X.Y.(Z-1) maint</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>The <em>maint</em> branch should now be fast-forwarded to the newly released
code so that maintenance fixes can be tracked for the current release:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Update maint to new release</div>
<div class="content">
<div class="ulist">
<ul>
<li>
<p><code>git checkout maint</code></p>
</li>
<li>
<p><code>git merge --ff-only master</code></p>
</li>
</ul>
</div>
</div>
</div>
<div class="paragraph">
<p>If the merge fails because it is not a fast-forward, then it is
possible some fixes on <em>maint</em> were missed in the feature release.
This will not happen if the content of the branches was verified as
described in the previous section.</p>
</div>
</div>
<div class="sect2">
<h3 id="_branch_management_for_next_and_seen_after_a_feature_release">Branch management for next and seen after a feature release</h3>
<div class="paragraph">
<p>After a feature release, the integration branch <em>next</em> may optionally be
rewound and rebuilt from the tip of <em>master</em> using the surviving
topics on <em>next</em>:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Rewind and rebuild next</div>
<div class="content">
<div class="ulist">
<ul>
<li>
<p><code>git switch -C next master</code></p>
</li>
<li>
<p><code>git merge ai/topic_in_next1</code></p>
</li>
<li>
<p><code>git merge ai/topic_in_next2</code></p>
</li>
<li>
<p>&#8230;&#8203;</p>
</li>
</ul>
</div>
</div>
</div>
<div class="paragraph">
<p>The advantage of doing this is that the history of <em>next</em> will be
clean. For example, some topics merged into <em>next</em> may have initially
looked promising, but were later found to be undesirable or premature.
In such a case, the topic is reverted out of <em>next</em> but the fact
remains in the history that it was once merged and reverted. By
recreating <em>next</em>, you give another incarnation of such topics a clean
slate to retry, and a feature release is a good point in history to do
so.</p>
</div>
<div class="paragraph">
<p>If you do this, then you should make a public announcement indicating
that <em>next</em> was rewound and rebuilt.</p>
</div>
<div class="paragraph">
<p>The same rewind and rebuild process may be followed for <em>seen</em>. A public
announcement is not necessary since <em>seen</em> is a throw-away branch, as
described above.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_distributed_workflows">DISTRIBUTED WORKFLOWS</h2>
<div class="sectionbody">
<div class="paragraph">
<p>After the last section, you should know how to manage topics.  In
general, you will not be the only person working on the project, so
you will have to share your work.</p>
</div>
<div class="paragraph">
<p>Roughly speaking, there are two important workflows: merge and patch.
The important difference is that the merge workflow can propagate full
history, including merges, while patches cannot.  Both workflows can
be used in parallel: in <code>git.git</code>, only subsystem maintainers use
the merge workflow, while everyone else sends patches.</p>
</div>
<div class="paragraph">
<p>Note that the maintainer(s) may impose restrictions, such as
"Signed-off-by" requirements, that all commits/patches submitted for
inclusion must adhere to.  Consult your project&#8217;s documentation for
more information.</p>
</div>
<div class="sect2">
<h3 id="_merge_workflow">Merge workflow</h3>
<div class="paragraph">
<p>The merge workflow works by copying branches between upstream and
downstream.  Upstream can merge contributions into the official
history; downstream base their work on the official history.</p>
</div>
<div class="paragraph">
<p>There are three main tools that can be used for this:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="git-push.html">git-push(1)</a> copies your branches to a remote repository,
usually to one that can be read by all involved parties;</p>
</li>
<li>
<p><a href="git-fetch.html">git-fetch(1)</a> that copies remote branches to your repository;
and</p>
</li>
<li>
<p><a href="git-pull.html">git-pull(1)</a> that does fetch and merge in one go.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Note the last point.  Do <em>not</em> use <em>git pull</em> unless you actually want
to merge the remote branch.</p>
</div>
<div class="paragraph">
<p>Getting changes out is easy:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Push/pull: Publishing branches/topics</div>
<div class="content">
<div class="paragraph">
<p><code>git push &lt;remote&gt; &lt;branch&gt;</code> and tell everyone where they can fetch
from.</p>
</div>
</div>
</div>
<div class="paragraph">
<p>You will still have to tell people by other means, such as mail.  (Git
provides the <a href="git-request-pull.html">git-request-pull(1)</a> to send preformatted pull
requests to upstream maintainers to simplify this task.)</p>
</div>
<div class="paragraph">
<p>If you just want to get the newest copies of the integration branches,
staying up to date is easy too:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Push/pull: Staying up to date</div>
<div class="content">
<div class="paragraph">
<p>Use <code>git fetch &lt;remote&gt;</code> or <code>git remote update</code> to stay up to date.</p>
</div>
</div>
</div>
<div class="paragraph">
<p>Then simply fork your topic branches from the stable remotes as
explained earlier.</p>
</div>
<div class="paragraph">
<p>If you are a maintainer and would like to merge other people&#8217;s topic
branches to the integration branches, they will typically send a
request to do so by mail.  Such a request looks like</p>
</div>
<div class="listingblock">
<div class="content">
<pre>Please pull from
    &lt;URL&gt; &lt;branch&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>In that case, <em>git pull</em> can do the fetch and merge in one go, as
follows.</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: Push/pull: Merging remote topics</div>
<div class="content">
<div class="paragraph">
<p><code>git pull &lt;URL&gt; &lt;branch&gt;</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>Occasionally, the maintainer may get merge conflicts when they try to
pull changes from downstream.  In this case, they can ask downstream to
do the merge and resolve the conflicts themselves (perhaps they will
know better how to resolve them).  It is one of the rare cases where
downstream <em>should</em> merge from upstream.</p>
</div>
</div>
<div class="sect2">
<h3 id="_patch_workflow">Patch workflow</h3>
<div class="paragraph">
<p>If you are a contributor that sends changes upstream in the form of
emails, you should use topic branches as usual (see above).  Then use
<a href="git-format-patch.html">git-format-patch(1)</a> to generate the corresponding emails
(highly recommended over manually formatting them because it makes the
maintainer&#8217;s life easier).</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: format-patch/am: Publishing branches/topics</div>
<div class="content">
<div class="ulist">
<ul>
<li>
<p><code>git format-patch -M upstream..topic</code> to turn them into preformatted
patch files</p>
</li>
<li>
<p><code>git send-email --to=&lt;recipient&gt; &lt;patches&gt;</code></p>
</li>
</ul>
</div>
</div>
</div>
<div class="paragraph">
<p>See the <a href="git-format-patch.html">git-format-patch(1)</a> and <a href="git-send-email.html">git-send-email(1)</a>
manpages for further usage notes.</p>
</div>
<div class="paragraph">
<p>If the maintainer tells you that your patch no longer applies to the
current upstream, you will have to rebase your topic (you cannot use a
merge because you cannot format-patch merges):</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: format-patch/am: Keeping topics up to date</div>
<div class="content">
<div class="paragraph">
<p><code>git pull --rebase &lt;URL&gt; &lt;branch&gt;</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>You can then fix the conflicts during the rebase.  Presumably you have
not published your topic other than by mail, so rebasing it is not a
problem.</p>
</div>
<div class="paragraph">
<p>If you receive such a patch series (as maintainer, or perhaps as a
reader of the mailing list it was sent to), save the mails to files,
create a new topic branch and use <em>git am</em> to import the commits:</p>
</div>
<div class="exampleblock">
<div class="title">Recipe: format-patch/am: Importing patches</div>
<div class="content">
<div class="paragraph">
<p><code>git am &lt; patch</code></p>
</div>
</div>
</div>
<div class="paragraph">
<p>One feature worth pointing out is the three-way merge, which can help
if you get conflicts: <code>git am -3</code> will use index information contained
in patches to figure out the merge base.  See <a href="git-am.html">git-am(1)</a> for
other options.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_see_also">SEE ALSO</h2>
<div class="sectionbody">
<div class="paragraph">
<p><a href="gittutorial.html">gittutorial(7)</a>,
<a href="git-push.html">git-push(1)</a>,
<a href="git-pull.html">git-pull(1)</a>,
<a href="git-merge.html">git-merge(1)</a>,
<a href="git-rebase.html">git-rebase(1)</a>,
<a href="git-format-patch.html">git-format-patch(1)</a>,
<a href="git-send-email.html">git-send-email(1)</a>,
<a href="git-am.html">git-am(1)</a></p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_git">GIT</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Part of the <a href="git.html">git(1)</a> suite</p>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>