__path__ = __import__('pkgutil').extend_path(__path__, __name__)


'''
developer reload tips

import sys
# RELOAD
try:
    main.Window.close_instances()
except Exception:
    pass


# delete module cache
def module_cleanup(module_name):
    """Cleanup module_name in sys.modules cache.

    Args:
        module_name (str): Module Name
    """
    if module_name in sys.builtin_module_names:
        return
    packages = [mod for mod in sys.modules if mod.startswith("%s." % module_name)]
    for package in packages + [module_name]:
        module = sys.modules.get(package)
        if module is not None:
            del sys.modules[package]


module_cleanup("lsr.maya.anim_lib")
module_cleanup("lsr.qt")

# delete instance cache
import __main__

__main__.__dict__['lsr.qt.core.base_main_window_lsr_single_ui'] = {};
import lsr.maya.anim_lib.main as main

main.launch()

developer reload convenient function
'''








