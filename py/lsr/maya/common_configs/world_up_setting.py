import maya.cmds as cmds
import maya.mel as mel


def change_y_up(*args):
    """
    change world up axis to Y
    Args:
        *args ():

    Returns:
        None
    """
    cmds.upAxis(ax='Y', rv=True)
    mel.eval('GoToDefaultView')


def change_z_up(*args):
    """
    change world up axis to Z
    Args:
        *args ():

    Returns:
        None
    """
    cmds.upAxis(ax='Z', rv=True)
    mel.eval('GoToDefaultView')
