#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试曲线编辑区域限制功能
"""

import sys
from PySide2 import QtWidgets, QtCore
from bezier_curve_editor import BezierCurveEditor


def test_curve_area_restriction():
    """测试曲线编辑区域限制"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建编辑器
    editor = BezierCurveEditor()
    editor.setWindowTitle("测试曲线编辑区域限制")
    editor.resize(900, 700)
    
    # 设置样式
    editor.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 11px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 60px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 80px;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QLabel {
            color: #cccccc;
        }
    """)
    
    # 显示窗口
    editor.show()
    
    # 添加测试说明
    QtWidgets.QMessageBox.information(
        editor, 
        "区域限制测试", 
        "功能改进说明:\n\n"
        "✅ 修复内容:\n"
        "- 关键帧只能在灰色网格区域内创建\n"
        "- 点击网格外不会生成关键帧\n"
        "- 拖拽关键帧时限制在有效区域内\n"
        "- 切线控制有长度限制，避免过度延伸\n"
        "- 右键删除也限制在网格区域内\n\n"
        "🎯 测试方法:\n"
        "1. 尝试在网格区域内点击 - 应该创建关键帧\n"
        "2. 尝试在网格区域外点击 - 不应该创建关键帧\n"
        "3. 拖拽关键帧到区域外 - 应该被限制\n"
        "4. 拖拽切线控制点 - 有合理的长度限制\n\n"
        "💡 视觉提示:\n"
        "- 深灰色区域：不可编辑\n"
        "- 浅灰色网格区域：可编辑区域\n"
        "- 区域边框更明显"
    )
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    test_curve_area_restriction()
