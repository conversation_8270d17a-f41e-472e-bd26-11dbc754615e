# -*- coding: utf-8 -*-

import os
import re

import maya.cmds as cmds
import maya.OpenMayaUI as OpenMayaUI

from PySide2 import QtWidgets
from shiboken2 import wrapInstance

from lsr.maya.maya_ui.CustomMayaBaseWindow import Custom<PERSON>ayaBaseWindow
from lsr.maya.maya_ui.maya_ui_widgets import Maya_ExportInfoUI
from lsr.python.core.compatible import long
import lsr.maya.maya_ui as maya_ui


class ReplaceReferenceFiles(object):
    """
    Batch Replace Reference Files
    """
    def __init__(self, dirName, rigFile, rename=False):
        self.mayaFilesList = []

        dirName = dirName.replace('\\', '/')
        if dirName.endswith("/"):
            dirName = dirName[:-1]
        self.dirName = dirName
        self.rigFile = rigFile
        self.getfilelist(self.dirName)
        self.rename = rename

    def getfilelist(self, filepath):
        filelist = os.listdir(filepath)

        mayaFileRe = r'.*\.(mb|ma)$'
        for num in range(len(filelist)):
            filename = filelist[num]
            if os.path.isdir(filepath + "/" + filename):
                self.getfilelist(filepath + "/" + filename)
            else:
                if re.match(mayaFileRe, filename):
                    self.mayaFilesList.append(filepath + '/' + filename)
        print(self.mayaFilesList)

    def batchReplace(self):
        infoUI = Maya_ExportInfoUI(len(self.mayaFilesList), "ReplaceReferenceFiles_Progress")
        infoUI.step()
        for animationFile in self.mayaFilesList:
            self.replaceRef(animationFile)
            infoUI.step()
        infoUI.delete()
        del (infoUI)

    def replaceRef(self, animationFile):
        cmds.file(new=True, f=True)
        cmds.file(animationFile, open=True, f=True, pmt=False)
        rnodes = cmds.ls(type='reference')
        refNode = ''
        for rn in rnodes:
            if re.match(r'[^:]*RN$', rn):
                refNode = rn
                break
        # print refNode
        cmds.file(self.rigFile, loadReference=refNode)

        rnodes = cmds.ls(type='reference')
        for nd in rnodes:
            if nd.find(":") == -1:
                try:
                    refPath = cmds.referenceQuery(nd, filename=True)
                    ns = os.path.basename(refPath).split(".")[0]
                    cmds.file(refPath, e=True, namespace=ns)
                except:
                    pass
        if self.rename:
            prefix = os.path.basename(self.rigFile).split('_')[0]
            fileName = os.path.basename(animationFile)
            dirName = os.path.dirname(animationFile)
            orifix = re.match(r'(.*)@.*\.(mb|ma)$', fileName).group(1)
            cmds.file(rename=os.path.join(dirName, fileName.replace(orifix, prefix)))
            cmds.file(save=True, force=True)
        else:
            cmds.file(save=True, force=True)


class ReplaceReferenceFiles_GUI(CustomMayaBaseWindow):
    def __init__(self):
        CustomMayaBaseWindow.__init__(self)
        self.size = (439, 190)
        self.window = "ReplaceReferenceFiles_Window"
        self.title = u'批量替换动画参考工具'
        self.actionName = "Apply and Close"

    def displayOptions(self):
        self.dir_tfb = cmds.textFieldButtonGrp(label=u'动画文件夹', text='', buttonLabel=u'选择目录',
                                               buttonCommand=self.ChooseDir_Path,
                                               columnAlign=([1, 'left'], [2, 'left'], [3, 'left']),
                                               columnWidth=[1, 70],
                                               adj=True)

        self.rig_tfb = cmds.textFieldButtonGrp(label=u'参考文件', text='', buttonLabel=u'选择文件',
                                               buttonCommand=self.ChooseRIG_Path,
                                               columnAlign=([1, 'left'], [2, 'left'], [3, 'left']),
                                               columnWidth=[1, 70],
                                               adj=True)
        cmds.formLayout(self.optionsForm, e=True,
                        attachForm=
                        (
                            [self.dir_tfb, 'left', 25],
                            [self.dir_tfb, 'right', 25],
                            [self.dir_tfb, 'top', 15],
                            [self.rig_tfb, 'left', 25],
                            [self.rig_tfb, 'right', 25],
                            [self.rig_tfb, 'top', 55],
                        )
                        )
        cmds.window(self.window, e=True, sizeable=False)

    def ChooseDir_Path(self, *args):
        path = cmds.fileDialog2(dialogStyle=1, fileMode=3)
        if path != None:
            selectedPath = path[0]
            cmds.textFieldButtonGrp(self.dir_tfb, e=True, text=selectedPath)

    def ChooseRIG_Path(self, *args):
        path = cmds.fileDialog2(dialogStyle=1, fileMode=1)
        if path != None:
            selectedPath = path[0]
            cmds.textFieldButtonGrp(self.rig_tfb, e=True, text=selectedPath)

    def applyBtnCmd(self, *args):
        dirPath = cmds.textFieldButtonGrp(self.dir_tfb, q=True, text=True)
        dirPath = dirPath.replace("\\", "/")
        rigPath = cmds.textFieldButtonGrp(self.rig_tfb, q=True, text=True)
        rigPath = rigPath.replace("\\", "/")
        RRInstance = ReplaceReferenceFiles(dirPath, rigPath)
        print('ready')
        RRInstance.batchReplace()

    def changeStyle(self):
        ptr = OpenMayaUI.MQtUtil.findControl(self.window)
        widget = wrapInstance(long(ptr), QtWidgets.QWidget)
        s_path = os.path.join(os.path.dirname(maya_ui.__file__), 'stylesheets', 'common.qss')
        with open(s_path, 'r') as f:
            widget.setStyleSheet(f.read())

    @classmethod
    def showUI(cls):
        """A function to instantiate the options window"""
        win = cls()
        win.create()
        try:
            win.changeStyle()
        except:
            pass
        return win


class ReplaceRefAndRename_GUI(CustomMayaBaseWindow):
    def __init__(self):
        CustomMayaBaseWindow.__init__(self)
        self.size = (439, 190)
        self.window = "ReplaceRefAndRename_Window"
        self.title = u'批量替换动画参考 && 自动命名工具'
        self.actionName = "Apply and Close"

    def displayOptions(self):
        self.dir_tfb = cmds.textFieldButtonGrp(label=u'动画文件夹', text='', buttonLabel=u'选择目录',
                                               buttonCommand=self.ChooseDir_Path,
                                               columnAlign=([1, 'left'], [2, 'left'], [3, 'left']),
                                               columnWidth=[1, 30],
                                               adj=True)

        self.rig_tfb = cmds.textFieldButtonGrp(label=u'参考文件', text='', buttonLabel=u'选择文件',
                                               buttonCommand=self.ChooseRIG_Path,
                                               columnAlign=([1, 'left'], [2, 'left'], [3, 'left']),
                                               columnWidth=[1, 30],
                                               adj=True)
        cmds.formLayout(self.optionsForm, e=True,
                        attachForm=
                        (
                            [self.dir_tfb, 'left', 25],
                            [self.dir_tfb, 'right', 25],
                            [self.dir_tfb, 'top', 15],
                            [self.rig_tfb, 'left', 25],
                            [self.rig_tfb, 'right', 25],
                            [self.rig_tfb, 'top', 55],
                        )
                        )
        cmds.window(self.window, e=True, sizeable=False)

    def ChooseDir_Path(self, *args):
        path = cmds.fileDialog2(dialogStyle=1, fileMode=3)
        if path != None:
            selectedPath = path[0]
            cmds.textFieldButtonGrp(self.dir_tfb, e=True, text=selectedPath)

    def ChooseRIG_Path(self, *args):
        path = cmds.fileDialog2(dialogStyle=1, fileMode=1)
        if path != None:
            selectedPath = path[0]
            cmds.textFieldButtonGrp(self.rig_tfb, e=True, text=selectedPath)

    def applyBtnCmd(self, *args):
        dirPath = cmds.textFieldButtonGrp(self.dir_tfb, q=True, text=True)
        dirPath = dirPath.replace("\\", "/")
        rigPath = cmds.textFieldButtonGrp(self.rig_tfb, q=True, text=True)
        rigPath = rigPath.replace("\\", "/")
        RRInstance = ReplaceReferenceFiles(dirPath, rigPath, True)
        RRInstance.batchReplace()
