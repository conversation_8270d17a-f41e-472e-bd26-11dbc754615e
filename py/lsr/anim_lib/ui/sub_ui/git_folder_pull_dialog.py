"""
to create the folder view, clone from git or set local
"""
import os
from functools import partial
try:
    from urllib.parse import quote_plus
except Exception:
    from urllib import quote_plus


# Studio package module imports
from Qt import QtWidgets, QtCore, QtGui

# Tool package module imports
from lsr.anim_lib.ui.sub_ui.base import Anim<PERSON>ibBaseSub<PERSON>
from lsr.anim_lib.data import constant
from lsr.anim_lib.utility.pyside import resolve_icon_path
from lsr.anim_lib.utility.pyside import change_pixmap_color


class AddFromRemoteWidget(QtWidgets.QWidget):
    def __init__(self, *args, **kwargs):
        super(AddFromRemoteWidget, self).__init__(*args, **kwargs)

        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):
        main_layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(main_layout)

        self.account_label = QtWidgets.QLabel(self)
        self.account_edit = QtWidgets.QLineEdit(self)
        self.account_warning_label = QtWidgets.QLabel(self)

        self.password_label = QtWidgets.QLabel(self)
        password_layout = QtWidgets.QHBoxLayout(self)
        self.password_show_switch_btn = QtWidgets.QPushButton(self)
        self.password_edit = QtWidgets.QLineEdit(self)
        password_tips_layout = QtWidgets.QHBoxLayout(self)
        self.password_warning_label = QtWidgets.QLabel(self)
        self.password_forget_url_label = QtWidgets.QLabel(self)
        self.git_label = QtWidgets.QLabel(self)
        self.git_edit = QtWidgets.QLineEdit(self)
        self.git_warning_label = QtWidgets.QLabel(self)
        self.target_path_label = QtWidgets.QLabel(self)
        target_path_edit_layout = QtWidgets.QHBoxLayout(self)
        self.target_path_edit = QtWidgets.QLineEdit(self)
        self.target_path_load_btn = QtWidgets.QPushButton(self)
        self.target_path_warning_label = QtWidgets.QLabel(self)

        self.account_label.setText('User:')
        self.account_edit.setPlaceholderText('Enter your account')
        main_layout.addWidget(self.account_label)
        main_layout.addWidget(self.account_edit)
        main_layout.addWidget(self.account_warning_label)

        self.password_label.setText('Password:')
        self.password_edit.setPlaceholderText('Enter your password')
        self.password_forget_url_label.setText(
            "<a href='https://git.tencent.com/users/sign_in' style='color: rgb(52, 173, 224)'>Forget Password?</a>"
        )
        self.password_forget_url_label.setOpenExternalLinks(True)
        self.password_edit.setValidator(
            QtGui.QRegExpValidator(QtCore.QRegExp("^[A-Za-z0-9`~!@#$%^&*()_-+=<>,.\\\/]+${50}"), self))
        self.account_edit.setValidator(
            QtGui.QRegExpValidator(QtCore.QRegExp("^[A-Za-z0-9`~!@#$%^&*()_-+=<>,.\\\/]+${50}"), self))
        self.password_edit.setEchoMode(QtWidgets.QLineEdit.Password)
        password_layout.addWidget(self.password_edit)
        password_layout.addWidget(self.password_show_switch_btn)
        password_tips_layout.addWidget(self.password_warning_label)
        password_tips_layout.addWidget(self.password_forget_url_label, 0, QtCore.Qt.AlignRight)

        main_layout.addWidget(self.password_label)
        main_layout.addLayout(password_layout)
        main_layout.addLayout(password_tips_layout)
        self.git_label.setText('Git HTTPS:')
        self.git_edit.setPlaceholderText('Enter Git HTTPS')
        domain_list = ["***************:", "******************:", "*******************:", "************************:",
                       "git@?-git.code.tencent.com:", "**************:", "**************"]
        git_edit_completer = QtWidgets.QCompleter(domain_list)
        self.git_edit.setCompleter(git_edit_completer)
        main_layout.addWidget(self.git_label)
        main_layout.addWidget(self.git_edit)
        main_layout.addWidget(self.git_warning_label)

        self.target_path_label.setText('Local Path:')
        self.target_path_edit.setPlaceholderText('Click button to browser folder')
        self.target_path_edit.setFocusPolicy(QtCore.Qt.NoFocus)
        target_path_edit_layout.addWidget(self.target_path_edit)
        target_path_edit_layout.addWidget(self.target_path_load_btn)
        main_layout.addWidget(self.target_path_label)
        main_layout.addLayout(target_path_edit_layout)
        main_layout.addWidget(self.target_path_warning_label)

    def _customized_view(self, *args, **kwargs):
        self.setMinimumSize(400, 360)

        eye_pixmap = change_pixmap_color(QtGui.QPixmap(resolve_icon_path(sub_dir='/library/eye.png')), "#a19f9f")
        eye_icon = QtGui.QIcon(eye_pixmap)
        self.password_show_switch_btn.setIcon(eye_icon)
        self.password_show_switch_btn.setIconSize(QtCore.QSize(30, 30))
        self.password_show_switch_btn.setFixedSize(30, 30)
        self.password_show_switch_btn.setStyleSheet("border: None; background-color: transparent;")

        fld_pixmap = change_pixmap_color(QtGui.QPixmap(resolve_icon_path(sub_dir='/library/folder-empty.png')), "#a19f9f")
        fld_icon = QtGui.QIcon(fld_pixmap)
        self.target_path_load_btn.setIcon(fld_icon)
        self.target_path_load_btn.setStyleSheet("border: None; background-color: transparent;")
        self.target_path_load_btn.setIconSize(QtCore.QSize(30, 30))
        self.target_path_load_btn.setFixedSize(30, 30)

        self.account_warning_label.setStyleSheet('color: red; font-size: 15px')
        self.password_warning_label.setStyleSheet('color: red; font-size: 15px')
        self.git_warning_label.setStyleSheet('color: red; font-size: 15px')
        self.target_path_warning_label.setStyleSheet('color: red; font-size: 15px')

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.password_show_switch_btn.clicked.connect(partial(self.password_echo_switch))
        self.target_path_load_btn.clicked.connect(partial(self.target_path_load_btn_clicked))

    def password_echo_switch(self, *args, **kwargs):
        if self.password_edit.echoMode() is QtWidgets.QLineEdit.Password:
            self.password_edit.setEchoMode(QtWidgets.QLineEdit.Normal)
        else:
            self.password_edit.setEchoMode(QtWidgets.QLineEdit.Password)

    def _folder_empty_recursion(self, *args, **kwargs):
        dir_path = self.call_file_dialog()
        if len(dir_path) == 0:
            return None
        else:
            if len(os.listdir(dir_path)) != 0:
                warning = QtWidgets.QMessageBox(self)
                warning.warning(None,
                                'Warning!',
                                constant.INSTANCE_UI_TRANSLATOR.translate_string('Please select a empty folder!')
                )
                return self._folder_empty_recursion()
            else:
                return dir_path

    def call_file_dialog(self, default_path=r'C:/', *args, **kwargs):
        dir_path = QtWidgets.QFileDialog.getExistingDirectory(None, 'Select folder', default_path)
        return dir_path

    def target_path_load_btn_clicked(self, *args, **kwargs):
        dir_path = self._folder_empty_recursion()
        if dir_path is not None:
            self.target_path_edit.setText(dir_path)
            self.target_path_warning_label.clear()


class AddFromLocalWidget(QtWidgets.QWidget):
    def __init__(self, *args, **kwargs):
        super(AddFromLocalWidget, self).__init__(*args, **kwargs)

        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):
        self.setMinimumSize(400, 120)
        main_layout = QtWidgets.QVBoxLayout(self)
        self.setLayout(main_layout)

        self.local_path_label = QtWidgets.QLabel(self)
        local_path_edit_layout = QtWidgets.QHBoxLayout(self)
        self.local_path_edit = QtWidgets.QLineEdit(self)
        self.local_path_load_btn = QtWidgets.QPushButton(self)
        self.local_path_warning_label = QtWidgets.QLabel(self)

        self.local_path_label.setText('Local Path:')
        self.local_path_edit.setPlaceholderText('Click button to browser folder')
        self.local_path_edit.setFocusPolicy(QtCore.Qt.NoFocus)

        local_path_edit_layout.addWidget(self.local_path_edit)
        local_path_edit_layout.addWidget(self.local_path_load_btn)
        main_layout.addWidget(self.local_path_label)
        main_layout.addLayout(local_path_edit_layout)
        main_layout.addWidget(self.local_path_warning_label)

    def _customized_view(self, *args, **kwargs):
        self.local_path_warning_label.setStyleSheet('color: red; font-size: 15px')
        fld_pixmap = change_pixmap_color(QtGui.QPixmap(resolve_icon_path(sub_dir='/library/folder-empty.png')), "#a19f9f")
        fld_icon = QtGui.QIcon(fld_pixmap)
        self.local_path_load_btn.setIcon(fld_icon)
        self.local_path_load_btn.setStyleSheet("border: None; background-color: transparent;")
        self.local_path_load_btn.setIconSize(QtCore.QSize(30, 30))
        self.local_path_load_btn.setFixedSize(30, 30)

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.local_path_load_btn.clicked.connect(partial(self.local_path_load_btn_clicked))

    def call_file_dialog(self, default_path=r'D:\UGit', *args, **kwargs):
        dir_path = QtWidgets.QFileDialog.getExistingDirectory(None, 'Select folder', default_path)
        return dir_path

    def local_path_load_btn_clicked(self, *args, **kwargs):
        dir_path = self._is_git_folder_recursion()
        if dir_path is not None:
            self.local_path_edit.setText(dir_path)
            self.local_path_warning_label.clear()

    def _is_git_folder_recursion(self, *args, **kwargs):
        dir_path = self.call_file_dialog()
        if len(dir_path) == 0:
            return None
        else:
            if '.git' not in os.listdir(dir_path):
                QtWidgets.QMessageBox(self).warning(None, 'Warning!',
                                                    'It is not a git folder!\nPlease select a git folder!')
                return self._is_git_folder_recursion()
            else:
                return dir_path


class GitFolderCreateWindow(AnimLibBaseSubUI):
    def __init__(self, parent=None, *args, **kwargs):
        super(GitFolderCreateWindow, self).__init__(parent, *args, **kwargs)

        self._init_layout()
        self._customized_view()
        self._do_signal_slot_connection()

    def _init_layout(self, *args, **kwargs):

        self.main_layout = QtWidgets.QVBoxLayout()
        self.setLayout(self.main_layout)

        self.button_h_layout = QtWidgets.QHBoxLayout()
        self.main_layout.addLayout(self.button_h_layout)

        self.switch_btn_grp = QtWidgets.QButtonGroup()
        self.remote_radio_btn = QtWidgets.QRadioButton('Add From Remote')
        self.local_radio_btn = QtWidgets.QRadioButton('Add From Local')
        self.remote_widget = AddFromRemoteWidget()
        self.local_widget = AddFromLocalWidget()
        self.ok_cancel_btn_layout = QtWidgets.QHBoxLayout()
        self.confirm_btn = QtWidgets.QPushButton('Confirm')
        self.cancel_btn = QtWidgets.QPushButton('Cancel')

        self.button_h_layout.addWidget(self.remote_radio_btn, 0, QtCore.Qt.AlignCenter)
        self.button_h_layout.addWidget(self.local_radio_btn, 0, QtCore.Qt.AlignCenter)
        self.main_layout.addWidget(self.remote_widget, QtCore.Qt.AlignTop)
        self.main_layout.addWidget(self.local_widget, QtCore.Qt.AlignTop)
        self.ok_cancel_btn_layout.addStretch()
        self.ok_cancel_btn_layout.addWidget(self.confirm_btn)
        self.ok_cancel_btn_layout.addWidget(self.cancel_btn)
        self.main_layout.addLayout(self.ok_cancel_btn_layout, QtCore.Qt.AlignBottom)

        self.switch_btn_grp.addButton(self.remote_radio_btn, 0)
        self.switch_btn_grp.addButton(self.local_radio_btn, 1)
        self.remote_radio_btn.setChecked(True)
        self.remote_widget.setVisible(True)
        self.local_widget.setVisible(False)
        self.switch_btn_grp.setId(self.remote_radio_btn, 0)

    def _customized_view(self, *args, **kwargs):
        self.setMinimumSize(420, 500)
        self.resize(420, 500)
        self.main_layout.setSpacing(0)
        self.main_layout.setAlignment(QtCore.Qt.AlignCenter)
        self.button_h_layout.setAlignment(QtCore.Qt.AlignTop)
        self.setWindowTitle("Git folder create")

    def _do_signal_slot_connection(self, *args, **kwargs):
        self.switch_btn_grp.buttonClicked.connect(partial(self.switch_widget))
        self.confirm_btn.clicked.connect(partial(self.login_accept))
        self.cancel_btn.clicked.connect(partial(self.login_cancel))

    def switch_widget(self, item, *args, **kwargs):
        if item.group().checkedId() == 0:
            self.setMinimumSize(420, 500)
            self.resize(420, 500)
            self.remote_widget.setVisible(True)
            self.local_widget.setVisible(False)
            for warning_label in [self.remote_widget.account_warning_label,
                                  self.remote_widget.password_warning_label,
                                  self.remote_widget.git_warning_label,
                                  self.remote_widget.target_path_warning_label]:
                warning_label.clear()

        else:  # if item.group().checkedId() == 1
            self.setMinimumSize(420, 220)
            self.resize(420, 220)
            self.local_widget.local_path_warning_label.clear()
            self.remote_widget.setVisible(False)
            self.local_widget.setVisible(True)

    def check_ui_input(self, *args, **kwargs):
        def _check_account_edit():
            if len(self.remote_widget.account_edit.text()) == 0:
                self.remote_widget.account_warning_label.setText('User name must not be empty!')
                return False
            else:
                self.remote_widget.account_warning_label.clear()
                return True

        def _check_password_edit():
            if len(self.remote_widget.password_edit.text()) == 0:
                self.remote_widget.password_warning_label.setText('Password must not be empty!')
                return False
            else:
                user_account = self.remote_widget.account_edit.text()
                user_password = quote_plus(self.remote_widget.password_edit.text())
                if not constant.INSTANCE_TEAM_ACTION_CTRL.validate_account(user_account, user_password):
                    self.remote_widget.password_warning_label.setText('Wrong account or password!')
                    return False
                self.remote_widget.password_warning_label.clear()
                return True

        def _check_git_edit():
            filter_str = '.git'
            if len(self.remote_widget.git_edit.text()) == 0:
                self.remote_widget.git_warning_label.setText('Git url must not be empty!')
                return False
            else:
                if not self.remote_widget.git_edit.text().endswith(filter_str):
                    self.remote_widget.git_warning_label.setText('Illegal git url!')
                    return False
                else:
                    self.remote_widget.git_warning_label.clear()
                    return True

        def _check_remote_target_path_edit():
            if len(self.remote_widget.target_path_edit.text()) == 0:
                self.remote_widget.target_path_warning_label.setText('local path must not be empty!')
                return False
            else:
                self.remote_widget.target_path_warning_label.clear()
                return True

        def _check_local_path_edit():
            if len(self.local_widget.local_path_edit.text()) == 0:
                self.local_widget.local_path_warning_label.setText('local path must not be empty!')
                return False
            else:
                self.local_widget.local_path_warning_label.clear()
                return True

        if self.switch_btn_grp.checkedId() == 0:
            check_list = [
                _check_account_edit(),
                _check_password_edit(),
                _check_git_edit(),
                _check_remote_target_path_edit()
            ]
            if False in check_list:
                return False
            return True
        else:  # self.switch_btn_grp.checkedId() == 1
            check_list = [_check_local_path_edit()]
            if False in check_list:
                return False
            return True

    def collect_view_data(self, *args, **kwargs):
        datablock = dict()
        datablock["git_pull_method"] = self.switch_btn_grp.checkedId()
        datablock["account"] = self.remote_widget.account_edit.text()
        datablock["password"] = quote_plus(self.remote_widget.password_edit.text())
        datablock["git"] = self.remote_widget.git_edit.text()
        datablock["remote_local_path"] = self.remote_widget.target_path_edit.text()
        datablock["exsited_local_path"] = self.local_widget.local_path_edit.text()
        return datablock

    def login_accept(self, *args, **kwargs):
        if self.check_ui_input():
            self.accept()

    def login_cancel(self, *args, **kwargs):
        self.reject()

    def exec_(self, out_data, *args, **kwargs):
        result = super(GitFolderCreateWindow, self).exec_()
        if result == QtWidgets.QDialog.Accepted:
            out_data.update(self.collect_view_data())
            return True
        else:
            return False