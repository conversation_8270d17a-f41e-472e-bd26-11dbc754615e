#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试撤销功能和属性编辑器的贝塞尔曲线编辑器
"""

import sys
from PySide2 import QtWidgets, QtCore
from bezier_curve_editor import BezierCurveEditor


def test_undo_and_properties():
    """测试撤销功能和属性编辑器"""
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建编辑器
    editor = BezierCurveEditor()
    editor.setWindowTitle("测试撤销功能和属性编辑器")
    editor.resize(1200, 800)
    
    # 设置样式
    editor.setStyleSheet("""
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: Arial;
            font-size: 11px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 5px;
            min-width: 80px;
        }
        
        QPushButton:hover {
            background-color: #505050;
        }
        
        QPushButton:pressed {
            background-color: #303030;
        }
        
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 60px;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #606060;
            border-radius: 3px;
            padding: 2px;
            min-width: 80px;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #606060;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QLabel {
            color: #cccccc;
        }
        
        QFormLayout QLabel {
            color: #ffffff;
            font-weight: bold;
        }
    """)
    
    # 显示窗口
    editor.show()
    
    # 确保曲线编辑器获得焦点
    editor.curve_widget.setFocus()
    
    # 添加功能说明
    QtWidgets.QMessageBox.information(
        editor, 
        "撤销功能和属性编辑器测试", 
        "🎯 新增功能测试:\n\n"
        "✅ 撤销/重做系统:\n"
        "• Ctrl+Z - 撤销上一步操作\n"
        "• Ctrl+Y - 重做操作\n"
        "• 支持最多50步撤销历史\n"
        "• 智能状态保存，只在实际改变时记录\n\n"
        "✅ 属性编辑器增强:\n"
        "• 右侧面板可以精确调整关键帧属性\n"
        "• 实时修改时间、值、切线参数\n"
        "• 每次修改都会自动保存到撤销栈\n"
        "• 修改后立即更新曲线显示\n\n"
        "🧪 测试步骤:\n"
        "1. 点击曲线编辑区域获得焦点\n"
        "2. 添加几个关键帧\n"
        "3. 选中关键帧，在右侧属性面板修改数值\n"
        "4. 拖拽关键帧和切线\n"
        "5. 使用Ctrl+Z撤销各种操作\n"
        "6. 使用Ctrl+Y重做操作\n"
        "7. 观察控制台输出的操作记录\n\n"
        "💡 特色功能:\n"
        "• 每个操作都有详细的描述\n"
        "• 属性面板与曲线编辑器完全同步\n"
        "• 撤销栈智能管理，避免重复记录\n"
        "• 支持工具栏按钮和快捷键双重操作"
    )
    
    # 添加一些测试关键帧
    curve_widget = editor.curve_widget
    curve_widget.add_keyframe(1.0, 0.3, save_state=False)
    curve_widget.add_keyframe(3.0, -0.5, save_state=False)
    curve_widget.add_keyframe(6.0, 0.8, save_state=False)
    curve_widget.add_keyframe(8.5, -0.2, save_state=False)
    
    # 保存测试状态
    curve_widget.save_state("添加测试关键帧")
    
    print("=== 撤销功能和属性编辑器测试开始 ===")
    print("功能说明:")
    print("1. 撤销系统 - 记录所有编辑操作")
    print("2. 属性编辑器 - 右侧面板精确调整")
    print("3. 实时同步 - 修改立即生效")
    print("4. 智能保存 - 只在实际改变时记录状态")
    print("\n测试方法:")
    print("• 选择关键帧后在右侧修改数值")
    print("• 拖拽关键帧和切线")
    print("• 使用Ctrl+Z/Ctrl+Y测试撤销重做")
    print("• 观察控制台输出的详细操作记录")
    print("=" * 50)
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    test_undo_and_properties()
