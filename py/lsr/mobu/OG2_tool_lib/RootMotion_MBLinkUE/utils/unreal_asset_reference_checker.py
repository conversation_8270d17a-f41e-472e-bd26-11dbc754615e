#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Unreal Engine 资产引用检查工具
用于检查资产是否被其他资产引用
"""

import unreal


class AssetReferenceChecker:
    """资产引用检查器"""
    
    def __init__(self):
        self.asset_registry = unreal.AssetRegistryHelpers.get_asset_registry()
        self.editor_asset_lib = unreal.EditorAssetLibrary()
    
    def is_asset_referenced(self, asset_path):
        """
        检查指定资产是否被其他资产引用
        
        Args:
            asset_path (str): 资产路径，例如 "/Game/Characters/MyCharacter"
            
        Returns:
            bool: 如果资产被引用返回True，否则返回False
        """
        try:
            # 获取引用该资产的所有资产
            referencers = self.get_asset_referencers(asset_path)
            return len(referencers) > 0
        except Exception as e:
            print(f"检查资产引用时出错: {e}")
            return False
    
    def get_asset_referencers(self, asset_path):
        """
        获取引用指定资产的所有资产列表
        
        Args:
            asset_path (str): 资产路径
            
        Returns:
            list: 引用该资产的资产路径列表
        """
        try:
            # 使用AssetRegistry获取引用者
            asset_data = self.asset_registry.get_asset_by_object_path(asset_path)
            if not asset_data:
                print(f"未找到资产: {asset_path}")
                return []
            
            # 获取引用该资产的所有资产
            referencers = self.asset_registry.get_referencers(
                asset_data.package_name,
                unreal.AssetRegistryDependencyOptions()
            )
            
            return [str(ref) for ref in referencers]
            
        except Exception as e:
            print(f"获取资产引用者时出错: {e}")
            return []
    
    def get_asset_dependencies(self, asset_path):
        """
        获取指定资产依赖的所有资产列表
        
        Args:
            asset_path (str): 资产路径
            
        Returns:
            list: 该资产依赖的资产路径列表
        """
        try:
            asset_data = self.asset_registry.get_asset_by_object_path(asset_path)
            if not asset_data:
                print(f"未找到资产: {asset_path}")
                return []
            
            # 获取该资产依赖的所有资产
            dependencies = self.asset_registry.get_dependencies(
                asset_data.package_name,
                unreal.AssetRegistryDependencyOptions()
            )
            
            return [str(dep) for dep in dependencies]
            
        except Exception as e:
            print(f"获取资产依赖时出错: {e}")
            return []
    
    def get_detailed_reference_info(self, asset_path):
        """
        获取资产的详细引用信息
        
        Args:
            asset_path (str): 资产路径
            
        Returns:
            dict: 包含引用信息的字典
        """
        result = {
            'asset_path': asset_path,
            'is_referenced': False,
            'referencers': [],
            'dependencies': [],
            'reference_count': 0,
            'dependency_count': 0
        }
        
        try:
            # 检查是否存在
            if not self.editor_asset_lib.does_asset_exist(asset_path):
                result['error'] = f"资产不存在: {asset_path}"
                return result
            
            # 获取引用者
            referencers = self.get_asset_referencers(asset_path)
            result['referencers'] = referencers
            result['reference_count'] = len(referencers)
            result['is_referenced'] = len(referencers) > 0
            
            # 获取依赖
            dependencies = self.get_asset_dependencies(asset_path)
            result['dependencies'] = dependencies
            result['dependency_count'] = len(dependencies)
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def can_safely_delete_asset(self, asset_path):
        """
        检查资产是否可以安全删除（没有被其他资产引用）
        
        Args:
            asset_path (str): 资产路径
            
        Returns:
            tuple: (bool, list) - (是否可以安全删除, 引用该资产的资产列表)
        """
        referencers = self.get_asset_referencers(asset_path)
        can_delete = len(referencers) == 0
        return can_delete, referencers
    
    def find_unused_assets_in_folder(self, folder_path="/Game"):
        """
        查找指定文件夹中未被引用的资产
        
        Args:
            folder_path (str): 文件夹路径，默认为"/Game"
            
        Returns:
            list: 未被引用的资产路径列表
        """
        unused_assets = []
        
        try:
            # 获取文件夹中的所有资产
            assets = self.editor_asset_lib.list_assets(folder_path, recursive=True)
            
            for asset_path in assets:
                if not self.is_asset_referenced(asset_path):
                    unused_assets.append(asset_path)
                    
        except Exception as e:
            print(f"查找未使用资产时出错: {e}")
        
        return unused_assets


def check_single_asset_reference(asset_path):
    """
    检查单个资产的引用情况（便捷函数）
    
    Args:
        asset_path (str): 资产路径
        
    Returns:
        dict: 引用信息字典
    """
    checker = AssetReferenceChecker()
    return checker.get_detailed_reference_info(asset_path)


def find_all_referencers(asset_path):
    """
    查找所有引用指定资产的资产（便捷函数）
    
    Args:
        asset_path (str): 资产路径
        
    Returns:
        list: 引用者列表
    """
    checker = AssetReferenceChecker()
    return checker.get_asset_referencers(asset_path)


def batch_check_assets_references(asset_paths):
    """
    批量检查多个资产的引用情况
    
    Args:
        asset_paths (list): 资产路径列表
        
    Returns:
        dict: 每个资产的引用信息
    """
    checker = AssetReferenceChecker()
    results = {}
    
    for asset_path in asset_paths:
        results[asset_path] = checker.get_detailed_reference_info(asset_path)
    
    return results


# 使用示例
if __name__ == "__main__":
    # 创建检查器实例
    checker = AssetReferenceChecker()
    
    # 示例1: 检查单个资产是否被引用
    asset_path = "/Game/Characters/MyCharacter"
    is_referenced = checker.is_asset_referenced(asset_path)
    print(f"资产 {asset_path} 是否被引用: {is_referenced}")
    
    # 示例2: 获取详细引用信息
    info = checker.get_detailed_reference_info(asset_path)
    print(f"详细信息: {info}")
    
    # 示例3: 检查是否可以安全删除
    can_delete, referencers = checker.can_safely_delete_asset(asset_path)
    print(f"可以安全删除: {can_delete}")
    if not can_delete:
        print(f"引用者: {referencers}")
    
    # 示例4: 查找未使用的资产
    unused_assets = checker.find_unused_assets_in_folder("/Game/Animations")
    print(f"未使用的动画资产: {unused_assets}")
