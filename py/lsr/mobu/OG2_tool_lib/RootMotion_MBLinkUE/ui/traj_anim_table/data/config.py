

class TrajAnimTableConfig:

    EAI_DIRECTION = ['EAI_None',
                      'EAI_Forward', 'EAI_ForwardRight', 'EAI_Right', 'EAI_BackwardRight',
                      'EAI_Backward', 'EAI_BackwardLeft', 'EAI_Left', 'EAI_ForwardLeft']
    MOVE_MODE = ['Veer', 'Aim', 'Hip']
    MOVE_SPEED = ['Walk', 'Run', 'Sprint']

    headers = [
        {
            'title': '轨迹运动方向（世界空间）',
            'key_name': 'MovementInput',
            'editor_type': 'combobox',
            'items': EAI_DIRECTION,
            'width': 200,
            'default': EAI_DIRECTION[0]
        },
        {
            'title': '角色朝向方向（世界空间）',
            'key_name': 'RotationInput',
            'editor_type': 'combobox',
            'items': EAI_DIRECTION,
            'width': 200,
            'default': EAI_DIRECTION[0]
        },
        {
            'title': '持续时间',
            'key_name': 'Duration',
            'editor_type': 'doublespinbox',
            'width': 100,
            'default': 1
        },
        {
            'title': '运动模式',
            'key_name': 'MovementPattern',
            'editor_type': 'combobox',
            'items': MOVE_MODE,
            'width': 100,
            'default': 'Veer'
        },
        {
            'title': '运动速度',
            'key_name': 'MovementSpeed',
            'editor_type': 'combobox',
            'items': MOVE_SPEED,
            'width': 100,
            'default': MOVE_SPEED[0]
        }
    ]

    url = 'http://127.0.0.1:30010/remote/preset/RCP_TrajectoryExporter/function/GenerateMultipleMotionModelTrajectory(Player)'

    IN_TIME_STEP = 0.1

    TEC_DOC_URL = 'https://iwiki.tencent.com/p/8000064371'
