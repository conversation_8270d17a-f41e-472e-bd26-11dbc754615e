"""
Brow C Limb
"""

import maya.cmds as cmds

from lsr.maya.nodezoo.node import Joint
from lsr.maya.rig.userlib.actions.face_limbs.custom_brow.brow_custom_base import Brow_Custom_Base_Limb


class Brow_C_Limb(Brow_Custom_Base_Limb):
    """
    Brow_C_Limb limb class
    Create a rig controller for Brow C type

    :limb type: Brow_C_Limb
    """

    def marker_data(self):
        """Skip marker data as this limb depends on a pre-built
        joint hierarchy."""
        return

    def limb_template_data(self, *args, **kwargs):
        """limb template data"""
        limb_name = kwargs.get('name_part', 'BrowC')
        name_side = kwargs.get('name_side', 'L')
        cmds.select(deselect=True)

        joint_chain = []
        jt_node = Joint.create(
            name='{}_Main_{}_RIGJNT'.format(limb_name, name_side),
            p=(0, 0, 0))
        joint_chain.append(jt_node)

        _joint_pos = [(-1, 0, -1), (0, 0.5, -1), (1, 0, -1)]

        for i, pos_data in enumerate(_joint_pos):
            cmds.select(deselect=True)
            jt_node = Joint.create(
                name='{}_{:02d}_{}_RIGJNT'.format(
                    limb_name, i, name_side),
                p=pos_data)
            joint_chain.append(jt_node)
            jt_node.set_parent(joint_chain[0])

        template_data = [
            {'type': 18, 'otherType': 'Brow_Main', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Brow_01', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Brow_02', 'overrideColor': 18},
            {'type': 18, 'otherType': 'Brow_03', 'overrideColor': 18}
        ]

        for jni_index, jnt in enumerate(joint_chain):
            jnt.set_attr('overrideEnabled', True)
            jnt.set_attr('drawLabel', True)
            for key, value in template_data[jni_index].items():
                jnt.set_attr(key, value)

        cmds.select(deselect=True)
        return joint_chain

    def run(self):
        super(Brow_C_Limb, self).run()

    def end(self):
        super(Brow_C_Limb, self).end()
        weight_list = [0.8, 1, 0.8]
        self.change_area_weights(self.main_ctrl, self.ctrls, weight_list)

