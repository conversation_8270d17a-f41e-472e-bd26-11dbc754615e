# -*- coding: utf-8 -*-
import os

from lsr.qt.core.base_main_window import get_window_class
from Qt import QtWidgets, QtCore

# get the base main window class
base_class = get_window_class(app_name='Arena FaceAnim Toolkit')


class ArenaFaceAnimToolkit(base_class):
    """
    The main face anim toolkit
    """

    def __init__(self):
        """ Creates and initializes this window. """
        super(ArenaFaceAnimToolkit, self).__init__()

    def setup_ui(self):
        """Rewrite Creates UI elements. """
        self._init_central_widget()

        self.layout().setAlignment(QtCore.Qt.AlignTop)

    def _init_central_widget(self):
        """
        Initialize the central widget of the main window.
        The central widget is a tab widget that contains the different tabs of the tool.
        But now we only have one tab.

        Returns:
            None
        """

        self.central_widget = QtWidgets.QTabWidget(self)
        self.setCentralWidget(self.central_widget)

        self.eye_blink_widget = QtWidgets.QWidget(self)

        self.central_widget.addTab(self.eye_blink_widget, "Eye Blink")

    def save_settings(self):
        """
        Updates the app settings and saves it to disk.

        Returns:
            QSettings: The settings object.
        """
        settings = super(ArenaFaceAnimToolkit, self).save_settings()
        settings.beginGroup('arena_face_anim_toolkit')
        settings.endGroup()
        return settings

    def load_settings(self):
        """
        Loads the app settings.

        Returns:
            QSettings: The settings object.
        """
        settings = super(ArenaFaceAnimToolkit, self).load_settings()
        settings.beginGroup('arena_face_anim_toolkit')
        settings.endGroup()

        return settings

    def hideEvent(self, event):
        """ Save settings before hiding. """
        self.closeEvent(event)

# ---------------------------------------------------------------------
# Debug code
# ---------------------------------------------------------------------

# import sys
#
# # RELOAD
# try:
#     main.Window.close_instances()
# except Exception:
#     pass
# # delete module cache
# def module_cleanup(module_name):
#     """Cleanup module_name in sys.modules cache.
#
#     Args:
#         module_name (str): Module Name
#     """
#     if module_name in sys.builtin_module_names:
#         return
#     packages = [mod for mod in sys.modules if mod.startswith("%s." % module_name)]
#     for package in packages + [module_name]:
#         module = sys.modules.get(package)
#         if module is not None:
#             del sys.modules[package]
# module_cleanup("lsr.maya.anim_tools.FaceAnim_Toolkit")
# module_cleanup("lsr.qt")
#
#
# # delete instance cache
# import __main__
# __main__.__dict__['lsr.qt.core.base_main_window_lsr_single_ui']={};
# from lsr.maya.anim_tools.FaceAnim_Toolkit import main
# main.ArenaFaceAnimToolkit.holder_show()
#
#
#
# sys.stdout.flush()

