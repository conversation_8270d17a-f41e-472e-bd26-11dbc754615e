# -*- coding: utf-8 -*-

import re

import maya.api.OpenMaya as om
import maya.cmds as cmds
import maya.mel as mel

from lsr.maya.model_tools.misc_tool.utils.base import UndoChunkContext

from lsr.maya.model_tools.misc_tool.skin_weight import GetDeformerNode, CopyWeightsAndSkin


def get_materils_order():
    """
    Get materils order

    Returns:
        list or None
    """
    objs = cmds.ls(sl=True, objectsOnly=True, long=True)
    if objs:
        model = objs[0]
        if cmds.nodeType(model) == 'transform':
            mesh = cmds.listRelatives(c=True, s=True, fullPath=True)[0]

            if cmds.nodeType(mesh) != 'mesh':
                return None

        selectionList = om.MSelectionList()
        selectionList.add(model)
        dagPath = om.MDagPath()
        dagPath = selectionList.getDagPath(0)
        mfn = om.MFnMesh(dagPath)
        mat_array, index_array = mfn.getConnectedShaders(0)

        mat_index = []
        for index in index_array:
            if index not in mat_index:
                mat_index.append(index)

        sha_engs = []
        for sd in mat_array:
            nodeFn = om.MFnDependencyNode(sd)
            sha_engs.append(nodeFn.name())

        materials = []
        if sha_engs:
            for index in mat_index:
                plug = cmds.connectionInfo(
                    sha_engs[index] + '.surfaceShader',
                    sourceFromDestination=1)
                mat = plug.split('.')[0]
                materials.append(mat)

        return materials

    else:
        cmds.warning('please select a model')
        return None


def get_selected_materils():
    """
    Get selected materils

    Returns:
        list or None
    """
    objs = cmds.ls(sl=True, objectsOnly=True, long=True)
    if objs:
        model = objs[0]
        if cmds.nodeType(model) == 'transform':
            model = cmds.listRelatives(c=True, s=True, fullPath=True)[0]

        sha_engs = cmds.listConnections(
            model,
            type='shadingEngine',
            source=True)

        sha_engs = list(set(sha_engs))

        materials = []
        if sha_engs:
            for seng in sha_engs:
                plug = cmds.connectionInfo(
                    seng + '.surfaceShader', sourceFromDestination=1)
                mat = plug.split('.')[0]
                materials.append(mat)

        return materials

    else:
        cmds.warning('please select a model')
        return None


class ReMergeModel(object):
    """
    ReMerge Model Class
    """
    def __init__(self, *args, **kwargs):
        with UndoChunkContext():
            model = cmds.ls(sl=True, long=True)
            if not model:
                return
            mesh = cmds.listRelatives(model[0], c=True, s=True, fullPath=True)
            if not mesh:
                return
            mesh = mesh[0]
            if cmds.nodeType(mesh) != 'mesh':
                return

            self.materials = kwargs.get("materials", kwargs.get("mat", None))
            if not self.materials:
                self.materials = get_materils_order()

            self.model = model[0]
            self.target = None
            self.model_dup = cmds.duplicate(model, rr=True)[0]
            self.extract_models = []
            self.separate_mesh()

    def separate_mesh(self):
        self.extract_models = []

        for i, mat in enumerate(self.materials):
            dup_mesh = cmds.duplicate(self.model_dup, rr=True)[0]
            model_faces = []
            cmds.hyperShade(objects=mat)
            cmds.select(cmds.polyListComponentConversion(toFace=True))
            components = cmds.ls(sl=True, flatten=True, long=True)
            for f in components:
                if re.search(r'{}\.f'.format(dup_mesh), f):
                    # if f.startswith(dup_mesh):
                    model_faces.append(f)

            mel.eval('buildObjectMenuItemsNow \"MainPane|viewPanes\"')
            mel.eval('dagMenuProc(\"MainPane|viewPanes\", \"\")')
            mel.eval(
                'doMenuComponentSelectionExt(\"%s\", \"facet\", 0)' %
                dup_mesh)
            cmds.select(model_faces, r=True)
            self.extract_face(i, mat)

        self.combine_mesh()
        self.transfer_skin()

        cmds.select(self.target)

    def combine_mesh(self):
        if self.extract_models:
            cmds.select(cl=True)
            for model in self.extract_models:
                cmds.select(model, add=True)
            combine_model = cmds.polyUnite(
                ch=0, mergeUVSets=1, centerPivot=True)
            combine_model = cmds.rename(combine_model, '%s_merge' % self.model)
            mel.eval('ConvertSelectionToVertices')
            cmds.polyMergeVertex(d=0.01, ch=False, am=True)
            cmds.delete(self.model_dup)
            mel.eval('changeSelectMode -object')

            cmds.select(combine_model, r=True)
            self.target = combine_model

    def extract_face(self, index, mat=None):
        component = cmds.ls(sl=True, flatten=True, long=True)
        if component:
            model_name = component[0].split('.')[0]
            ori_parent = cmds.listRelatives(model_name, p=True, fullPath=True)

            cmds.ExtractFace()
            cmds.bakePartialHistory(prePostDeformers=True)

            mel.eval('changeSelectMode -object')
            new_models = cmds.ls(sl=True, long=True)
            models_combine = self.check_faces_model(new_models, mat)
            # print '++++++++++++++++++++'
            # print new_models
            # print models_combine
            # print models
            [cmds.delete(mesh)
             for mesh in new_models if mesh not in models_combine]

            # cmds.delete(new_models[0])

            cmds.select(models_combine, r=True)
            count = len(cmds.ls(sl=True))
            if count > 1:
                combine_model = cmds.polyUnite(
                    ch=0, mergeUVSets=1, centerPivot=True)
                nm = cmds.rename(combine_model, '%s_%d' % (mat, index))
            else:

                nm = cmds.rename(models_combine[0], '%s_%d' % (mat, index))
            self.extract_models.append(nm)

            if ori_parent:
                cmds.parent(nm, ori_parent[0])
            else:
                try:
                    cmds.parent(nm, world=True)
                except BaseException:
                    pass

            if cmds.objExists(model_name):
                cmds.delete(model_name)

        else:
            cmds.warning(u'Please select a face of the model')

    def check_faces_model(self, model_list, material):
        if not material:
            return

        model_valid = []
        for model in model_list:
            cmds.hyperShade(objects=material)
            cmds.select(cmds.polyListComponentConversion(toFace=True))
            components = cmds.ls(sl=True, flatten=True, long=True)
            for f in components:
                if f.startswith(model):
                    model_valid.append(model)
                    break
        return list(set(model_valid))

    def transfer_skin(self):
        source_skin_node = GetDeformerNode.get_info(self.model)
        if source_skin_node:
            if self.model and self.target:
                cmds.select(self.model, self.target)
                CopyWeightsAndSkin.doit()
                return
