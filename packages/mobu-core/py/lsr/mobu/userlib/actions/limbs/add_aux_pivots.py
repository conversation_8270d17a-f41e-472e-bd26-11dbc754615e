"""
Add Aux Pivots to a HumanIK character
"""

import pyfbsdk as fb

import lsr.protostar.core.parameter as pa
from lsr.mobu.base_actions import RigAction
from lsr.mobu.nodezoo.node import Character


class FootPivots(RigAction):
    """
    Create humanIK Foot Pivots
    """

    _UI_ICON = 'foot_pivots'

    @pa.message_param()
    def character(self):
        """The character node."""

    def run(self):
        """Executes this action."""
        character = None
        if self.character.value:
            character = self.character.value

        if not character:
            character = Character(fb.FBApplication().CurrentCharacter)

        if not character:
            self.error('No character has been defined')
            raise ValueError('No character has been defined')

        if not character.has_control_rig:
            self.error('No Control rig has been defined')
            raise ValueError('No Control rig has been defined')

        # Create Foot Pivots
        character.add_ankle_aux_pivot()
        character.add_foot_aux_pivot()

        self.info("Added {} foot pivots".format(character.name))
