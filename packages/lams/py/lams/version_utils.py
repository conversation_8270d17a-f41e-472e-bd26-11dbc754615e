import os
from glob import glob

import path_utils
prj_dir = path_utils.get_prj_root_dir()

from lams import project_api

def get_next_version(path=''):
  version_dir_list = glob(os.path.join(path,'*[0-9]'))
  if not version_dir_list:
    return '001'
  else:
    version_list = [int(os.path.basename(v)) for v in version_dir_list]
    version_list.sort()
    next_version = '%03d' % (int(version_list[-1])+1)
    return next_version

def get_asset_version_dir(project='',
                          context='',
                          asset='',
                          task='',
                          component='',
                          version=''):
  if context == 'publish':
    pub_root = project_api.get_source_asset_publish_root(project=project)
    asset_path = os.path.join(pub_root,project,'source_assets','publish',asset,task,component)
  else:
    asset_path = os.path.join(prj_dir,project,'source_assets',context,asset,task,component)

  ap_v_dir = os.path.join(asset_path,version)

  return ap_v_dir
