{"DeformerDecompose": {"command": "PY:import lsr.maya.rigtools.deformer_decompose as dd; dd.run()", "icon": "editor.png", "tag": ["rig", "ui"]}, "PoseEditor": {"command": "PY:import lsr.maya.rigtools.pose_editor as pose_editor;pose_editor.run()", "icon": "chimpamzee.png", "tag": ["rig", "face", "ui"], "help_link": "https://iwiki.woa.com/pages/viewpage.action?pageId=761500592"}, "PoseCleanUp": {"command": "PY:import lsr.maya.rigtools.pose_editor.api.pose_controller as pc ;pc.do_clean_up()", "tag": ["rig", "face"]}, "RigDataExporter": {"command": "PY:import lsr.maya.rigtools.rig_data_exporter.view as rd; rd.launch()", "annotation": "Open LSR rig data exporter.", "icon": "mr_export.png", "tag": ["rig", "ui"]}, "PoseWrangler": {"command": "PY:from lsr.maya.rigtools.pose_wrangler.epic_pose_wrangler import main;pose_wrangler = main.PoseWrangler()", "annotation": "Open Pose Wrangler UI.", "icon": "PoseWrangler.png", "tag": ["rig", "ui"]}, "Protostar": {"command": "PY:from lsr.protostar.ui import launch_with_context; launch_with_context('maya')", "annotation": "Open Protostar UI.", "icon": "protostar.png", "tag": ["rig", "ui"]}, "CyberHumanRegister": {"command": "PY:from lsr.maya.rigtools.cyberhuman.gui.registration_gui import RegistrationGUI; ui = RegistrationGUI(); ui.show()", "annotation": "register UI.", "icon": "protostar.png", "tag": ["rig", "ui"]}, "CyberHumanLandmark": {"command": "PY:from lsr.maya.rigtools.cyberhuman.gui.landmark_gui import LandmarksGUI; ui = LandmarksGUI(); ui.show()", "annotation": "landmarks UI.", "icon": "protostar.png", "tag": ["rig", "ui"]}, "MetahumanRetarget": {"command": "PY:from lsr.maya.rigtools.metahuman import ui;from lsr.maya.rigtools.metahuman import ui; ui.show()", "annotation": "landmarks UI.", "icon": "metahuman.png", "tag": ["rig", "face", "ui"]}, "LIMBLIBRARY": {"command": "PY:from lsr.maya.rigtools.limb_library.ui import main; main.show()", "annotation": "Limb Library", "icon": "limblib.png", "tag": ["rig", "ui"]}, "ProtostarWithRig": {"command": "PY:from lsr.maya.rigtools.ps_graph.show_rig_graph import show_rig_graph_data;show_rig_graph_data()", "annotation": "Open Protostar UI with Rig Graph data.", "icon": "protostar.png", "tag": ["rig", "ui"]}, "AddLsrConstraint": {"command": "PY:from lsr.maya.rigtools.add_constraints.main import add_lsr_transformConstraint;add_lsr_transformConstraint()", "annotation": "add lsr transformConstraint.", "tag": ["rig", "ui"]}, "AddLsrMultiConstraint": {"command": "PY:from lsr.maya.rigtools.add_constraints.main import add_lsr_transformMultiConstraint;add_lsr_transformMultiConstraint()", "annotation": "add lsr Muitiple transformConstraint.", "tag": ["rig", "ui"]}, "CSFacialPoseEdit": {"command": "PY:import lsr.maya.rigtools.custom_facial_editor.ui.main as main;main.launch()", "annotation": "open custom facial editor.", "icon": "np-head.png", "tag": ["rig", "ui"]}, "QuickRig": {"command": "PY:import lsr.maya.rigtools.quick_rig.qr_view as qv;qv.QuickRigWindow.launch()", "annotation": "Open Quick Rig UI.", "icon": "quickrig.png", "tag": ["rig", "ui"]}}