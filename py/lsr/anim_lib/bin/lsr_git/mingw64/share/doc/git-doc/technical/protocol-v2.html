<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
<meta charset="UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
<meta name="generator" content="Asciidoctor 2.0.17"/>
<title>Git Wire Protocol, Version 2</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"/>
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.square li ul,ul.circle li ul,ul.disc li ul{list-style:inherit}
ul.square{list-style-type:square}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child,body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.exampleblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child{margin-bottom:0}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>:first-child{margin-top:0}
.sidebarblock>:last-child{margin-bottom:0}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="article">
<div id="header">
<h1>Git Wire Protocol, Version 2</h1>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>This document presents a specification for a version 2 of Git&#8217;s wire
protocol.  Protocol v2 will improve upon v1 in the following ways:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Instead of multiple service names, multiple commands will be
supported by a single service</p>
</li>
<li>
<p>Easily extendable as capabilities are moved into their own section
of the protocol, no longer being hidden behind a NUL byte and
limited by the size of a pkt-line</p>
</li>
<li>
<p>Separate out other information hidden behind NUL bytes (e.g. agent
string as a capability and symrefs can be requested using <em>ls-refs</em>)</p>
</li>
<li>
<p>Reference advertisement will be omitted unless explicitly requested</p>
</li>
<li>
<p>ls-refs command to explicitly request some refs</p>
</li>
<li>
<p>Designed with http and stateless-rpc in mind.  With clear flush
semantics the http remote helper can simply act as a proxy</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>In protocol v2 communication is command oriented.  When first contacting a
server a list of capabilities will advertised.  Some of these capabilities
will be commands which a client can request be executed.  Once a command
has completed, a client can reuse the connection and request that other
commands be executed.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_packet_line_framing">Packet-Line Framing</h2>
<div class="sectionbody">
<div class="paragraph">
<p>All communication is done using packet-line framing, just as in v1.  See
<code>Documentation/technical/pack-protocol.txt</code> and
<code>Documentation/technical/protocol-common.txt</code> for more information.</p>
</div>
<div class="paragraph">
<p>In protocol v2 these special packets will have the following semantics:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><em>0000</em> Flush Packet (flush-pkt) - indicates the end of a message</p>
</li>
<li>
<p><em>0001</em> Delimiter Packet (delim-pkt) - separates sections of a message</p>
</li>
<li>
<p><em>0002</em> Response End Packet (response-end-pkt) - indicates the end of a
response for stateless connections</p>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_initial_client_request">Initial Client Request</h2>
<div class="sectionbody">
<div class="paragraph">
<p>In general a client can request to speak protocol v2 by sending
<code>version=2</code> through the respective side-channel for the transport being
used which inevitably sets <code>GIT_PROTOCOL</code>.  More information can be
found in <code>pack-protocol.txt</code> and <code>http-protocol.txt</code>, as well as the
<code>GIT_PROTOCOL</code> definition in <code>git.txt</code>. In all cases the
response from the server is the capability advertisement.</p>
</div>
<div class="sect2">
<h3 id="_git_transport">Git Transport</h3>
<div class="paragraph">
<p>When using the git:// transport, you can request to use protocol v2 by
sending "version=2" as an extra parameter:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>003egit-upload-pack /project.git\0host=myserver.com\0\0version=2\0</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_ssh_and_file_transport">SSH and File Transport</h3>
<div class="paragraph">
<p>When using either the ssh:// or file:// transport, the GIT_PROTOCOL
environment variable must be set explicitly to include "version=2".
The server may need to be configured to allow this environment variable
to pass.</p>
</div>
</div>
<div class="sect2">
<h3 id="_http_transport">HTTP Transport</h3>
<div class="paragraph">
<p>When using the http:// or https:// transport a client makes a "smart"
info/refs request as described in <code>http-protocol.txt</code> and requests that
v2 be used by supplying "version=2" in the <code>Git-Protocol</code> header.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>C: GET $GIT_URL/info/refs?service=git-upload-pack HTTP/1.0
C: Git-Protocol: version=2</pre>
</div>
</div>
<div class="paragraph">
<p>A v2 server would reply:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>S: 200 OK
S: &lt;Some headers&gt;
S: ...
S:
S: 000eversion 2\n
S: &lt;capability-advertisement&gt;</pre>
</div>
</div>
<div class="paragraph">
<p>Subsequent requests are then made directly to the service
<code>$GIT_URL/git-upload-pack</code>. (This works the same for git-receive-pack).</p>
</div>
<div class="paragraph">
<p>Uses the <code>--http-backend-info-refs</code> option to
<a href="../git-upload-pack.html">git-upload-pack(1)</a>.</p>
</div>
<div class="paragraph">
<p>The server may need to be configured to pass this header&#8217;s contents via
the <code>GIT_PROTOCOL</code> variable. See the discussion in <code>git-http-backend.txt</code>.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_capability_advertisement">Capability Advertisement</h2>
<div class="sectionbody">
<div class="paragraph">
<p>A server which decides to communicate (based on a request from a client)
using protocol version 2, notifies the client by sending a version string
in its initial response followed by an advertisement of its capabilities.
Each capability is a key with an optional value.  Clients must ignore all
unknown keys.  Semantics of unknown values are left to the definition of
each key.  Some capabilities will describe commands which can be requested
to be executed by the client.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>capability-advertisement = protocol-version
      capability-list
      flush-pkt</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>protocol-version = PKT-LINE("version 2" LF)
capability-list = *capability
capability = PKT-LINE(key[=value] LF)</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>key = 1*(ALPHA | DIGIT | "-_")
value = 1*(ALPHA | DIGIT | " -_.,?\/{}[]()&lt;&gt;!@#$%^&amp;*+=:;")</pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_command_request">Command Request</h2>
<div class="sectionbody">
<div class="paragraph">
<p>After receiving the capability advertisement, a client can then issue a
request to select the command it wants with any particular capabilities
or arguments.  There is then an optional section where the client can
provide any command specific parameters or queries.  Only a single
command can be requested at a time.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>request = empty-request | command-request
empty-request = flush-pkt
command-request = command
    capability-list
    delim-pkt
    command-args
    flush-pkt
command = PKT-LINE("command=" key LF)
command-args = *command-specific-arg</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>command-specific-args are packet line framed arguments defined by
each individual command.</pre>
</div>
</div>
<div class="paragraph">
<p>The server will then check to ensure that the client&#8217;s request is
comprised of a valid command as well as valid capabilities which were
advertised.  If the request is valid the server will then execute the
command.  A server MUST wait till it has received the client&#8217;s entire
request before issuing a response.  The format of the response is
determined by the command being executed, but in all cases a flush-pkt
indicates the end of the response.</p>
</div>
<div class="paragraph">
<p>When a command has finished, and the client has received the entire
response from the server, a client can either request that another
command be executed or can terminate the connection.  A client may
optionally send an empty request consisting of just a flush-pkt to
indicate that no more requests will be made.</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_capabilities">Capabilities</h2>
<div class="sectionbody">
<div class="paragraph">
<p>There are two different types of capabilities: normal capabilities,
which can be used to convey information or alter the behavior of a
request, and commands, which are the core actions that a client wants to
perform (fetch, push, etc).</p>
</div>
<div class="paragraph">
<p>Protocol version 2 is stateless by default.  This means that all commands
must only last a single round and be stateless from the perspective of the
server side, unless the client has requested a capability indicating that
state should be maintained by the server.  Clients MUST NOT require state
management on the server side in order to function correctly.  This
permits simple round-robin load-balancing on the server side, without
needing to worry about state management.</p>
</div>
<div class="sect2">
<h3 id="_agent">agent</h3>
<div class="paragraph">
<p>The server can advertise the <code>agent</code> capability with a value <code>X</code> (in the
form <code>agent=X</code>) to notify the client that the server is running version
<code>X</code>.  The client may optionally send its own agent string by including
the <code>agent</code> capability with a value <code>Y</code> (in the form <code>agent=Y</code>) in its
request to the server (but it MUST NOT do so if the server did not
advertise the agent capability). The <code>X</code> and <code>Y</code> strings may contain any
printable ASCII characters except space (i.e., the byte range 32 &lt; x &lt;
127), and are typically of the form "package/version" (e.g.,
"git/1.8.3.1"). The agent strings are purely informative for statistics
and debugging purposes, and MUST NOT be used to programmatically assume
the presence or absence of particular features.</p>
</div>
</div>
<div class="sect2">
<h3 id="_ls_refs">ls-refs</h3>
<div class="paragraph">
<p><code>ls-refs</code> is the command used to request a reference advertisement in v2.
Unlike the current reference advertisement, ls-refs takes in arguments
which can be used to limit the refs sent from the server.</p>
</div>
<div class="paragraph">
<p>Additional features not supported in the base command will be advertised
as the value of the command in the capability advertisement in the form
of a space separated list of features: "&lt;command&gt;=&lt;feature 1&gt; &lt;feature 2&gt;"</p>
</div>
<div class="paragraph">
<p>ls-refs takes in the following arguments:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   symrefs
In addition to the object pointed by it, show the underlying ref
pointed by it when showing a symbolic ref.
   peel
Show peeled tags.
   ref-prefix &lt;prefix&gt;
When specified, only references having a prefix matching one of
the provided prefixes are displayed. Multiple instances may be
given, in which case references matching any prefix will be
shown. Note that this is purely for optimization; a server MAY
show refs not matching the prefix if it chooses, and clients
should filter the result themselves.</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>unborn</em> feature is advertised the following argument can be
included in the client&#8217;s request.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   unborn
The server will send information about HEAD even if it is a symref
pointing to an unborn branch in the form "unborn HEAD
symref-target:&lt;target&gt;".</pre>
</div>
</div>
<div class="paragraph">
<p>The output of ls-refs is as follows:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>output = *ref
  flush-pkt
obj-id-or-unborn = (obj-id | "unborn")
ref = PKT-LINE(obj-id-or-unborn SP refname *(SP ref-attribute) LF)
ref-attribute = (symref | peeled)
symref = "symref-target:" symref-target
peeled = "peeled:" obj-id</pre>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_fetch">fetch</h3>
<div class="paragraph">
<p><code>fetch</code> is the command used to fetch a packfile in v2.  It can be looked
at as a modified version of the v1 fetch where the ref-advertisement is
stripped out (since the <code>ls-refs</code> command fills that role) and the
message format is tweaked to eliminate redundancies and permit easy
addition of future extensions.</p>
</div>
<div class="paragraph">
<p>Additional features not supported in the base command will be advertised
as the value of the command in the capability advertisement in the form
of a space separated list of features: "&lt;command&gt;=&lt;feature 1&gt; &lt;feature 2&gt;"</p>
</div>
<div class="paragraph">
<p>A <code>fetch</code> request can take the following arguments:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   want &lt;oid&gt;
Indicates to the server an object which the client wants to
retrieve.  Wants can be anything and are not limited to
advertised objects.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   have &lt;oid&gt;
Indicates to the server an object which the client has locally.
This allows the server to make a packfile which only contains
the objects that the client needs. Multiple 'have' lines can be
supplied.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   done
Indicates to the server that negotiation should terminate (or
not even begin if performing a clone) and that the server should
use the information supplied in the request to construct the
packfile.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   thin-pack
Request that a thin pack be sent, which is a pack with deltas
which reference base objects not contained within the pack (but
are known to exist at the receiving end). This can reduce the
network traffic significantly, but it requires the receiving end
to know how to "thicken" these packs by adding the missing bases
to the pack.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   no-progress
Request that progress information that would normally be sent on
side-band channel 2, during the packfile transfer, should not be
sent.  However, the side-band channel 3 is still used for error
responses.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   include-tag
Request that annotated tags should be sent if the objects they
point to are being sent.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   ofs-delta
Indicate that the client understands PACKv2 with delta referring
to its base by position in pack rather than by an oid.  That is,
they can read OBJ_OFS_DELTA (aka type 6) in a packfile.</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>shallow</em> feature is advertised the following arguments can be
included in the clients request as well as the potential addition of the
<em>shallow-info</em> section in the server&#8217;s response as explained below.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   shallow &lt;oid&gt;
A client must notify the server of all commits for which it only
has shallow copies (meaning that it doesn't have the parents of
a commit) by supplying a 'shallow &lt;oid&gt;' line for each such
object so that the server is aware of the limitations of the
client's history.  This is so that the server is aware that the
client may not have all objects reachable from such commits.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   deepen &lt;depth&gt;
Requests that the fetch/clone should be shallow having a commit
depth of &lt;depth&gt; relative to the remote side.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   deepen-relative
Requests that the semantics of the "deepen" command be changed
to indicate that the depth requested is relative to the client's
current shallow boundary, instead of relative to the requested
commits.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   deepen-since &lt;timestamp&gt;
Requests that the shallow clone/fetch should be cut at a
specific time, instead of depth.  Internally it's equivalent to
doing "git rev-list --max-age=&lt;timestamp&gt;". Cannot be used with
"deepen".</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   deepen-not &lt;rev&gt;
Requests that the shallow clone/fetch should be cut at a
specific revision specified by '&lt;rev&gt;', instead of a depth.
Internally it's equivalent of doing "git rev-list --not &lt;rev&gt;".
Cannot be used with "deepen", but can be used with
"deepen-since".</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>filter</em> feature is advertised, the following argument can be
included in the client&#8217;s request:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   filter &lt;filter-spec&gt;
Request that various objects from the packfile be omitted
using one of several filtering techniques. These are intended
for use with partial clone and partial fetch operations. See
`rev-list` for possible "filter-spec" values. When communicating
with other processes, senders SHOULD translate scaled integers
(e.g. "1k") into a fully-expanded form (e.g. "1024") to aid
interoperability with older receivers that may not understand
newly-invented scaling suffixes. However, receivers SHOULD
accept the following suffixes: 'k', 'm', and 'g' for 1024,
1048576, and 1073741824, respectively.</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>ref-in-want</em> feature is advertised, the following argument can
be included in the client&#8217;s request as well as the potential addition of
the <em>wanted-refs</em> section in the server&#8217;s response as explained below.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   want-ref &lt;ref&gt;
Indicates to the server that the client wants to retrieve a
particular ref, where &lt;ref&gt; is the full name of a ref on the
server.</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>sideband-all</em> feature is advertised, the following argument can be
included in the client&#8217;s request:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   sideband-all
Instruct the server to send the whole response multiplexed, not just
the packfile section. All non-flush and non-delim PKT-LINE in the
response (not only in the packfile section) will then start with a byte
indicating its sideband (1, 2, or 3), and the server may send "0005\2"
(a PKT-LINE of sideband 2 with no payload) as a keepalive packet.</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>packfile-uris</em> feature is advertised, the following argument
can be included in the client&#8217;s request as well as the potential
addition of the <em>packfile-uris</em> section in the server&#8217;s response as
explained below.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   packfile-uris &lt;comma-separated list of protocols&gt;
Indicates to the server that the client is willing to receive
URIs of any of the given protocols in place of objects in the
sent packfile. Before performing the connectivity check, the
client should download from all given URIs. Currently, the
protocols supported are "http" and "https".</pre>
</div>
</div>
<div class="paragraph">
<p>If the <em>wait-for-done</em> feature is advertised, the following argument
can be included in the client&#8217;s request.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>   wait-for-done
Indicates to the server that it should never send "ready", but
should wait for the client to say "done" before sending the
packfile.</pre>
</div>
</div>
<div class="paragraph">
<p>The response of <code>fetch</code> is broken into a number of sections separated by
delimiter packets (0001), with each section beginning with its section
header. Most sections are sent only when the packfile is sent.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>output = acknowledgements flush-pkt |
  [acknowledgments delim-pkt] [shallow-info delim-pkt]
  [wanted-refs delim-pkt] [packfile-uris delim-pkt]
  packfile flush-pkt</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>acknowledgments = PKT-LINE("acknowledgments" LF)
    (nak | *ack)
    (ready)
ready = PKT-LINE("ready" LF)
nak = PKT-LINE("NAK" LF)
ack = PKT-LINE("ACK" SP obj-id LF)</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>shallow-info = PKT-LINE("shallow-info" LF)
 *PKT-LINE((shallow | unshallow) LF)
shallow = "shallow" SP obj-id
unshallow = "unshallow" SP obj-id</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>wanted-refs = PKT-LINE("wanted-refs" LF)
*PKT-LINE(wanted-ref LF)
wanted-ref = obj-id SP refname</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>packfile-uris = PKT-LINE("packfile-uris" LF) *packfile-uri
packfile-uri = PKT-LINE(40*(HEXDIGIT) SP *%x20-ff LF)</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>packfile = PKT-LINE("packfile" LF)
    *PKT-LINE(%x01-03 *%x00-ff)</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>   acknowledgments section
* If the client determines that it is finished with negotiations by
  sending a "done" line (thus requiring the server to send a packfile),
  the acknowledgments sections MUST be omitted from the server's
  response.</pre>
</div>
</div>
<div class="ulist">
<ul>
<li>
<p>Always begins with the section header "acknowledgments"</p>
</li>
<li>
<p>The server will respond with "NAK" if none of the object ids sent
as have lines were common.</p>
</li>
<li>
<p>The server will respond with "ACK obj-id" for all of the
object ids sent as have lines which are common.</p>
</li>
<li>
<p>A response cannot have both "ACK" lines as well as a "NAK"
line.</p>
</li>
<li>
<p>The server will respond with a "ready" line indicating that
the server has found an acceptable common base and is ready to
make and send a packfile (which will be found in the packfile
section of the same response)</p>
</li>
<li>
<p>If the server has found a suitable cut point and has decided
to send a "ready" line, then the server can decide to (as an
optimization) omit any "ACK" lines it would have sent during
its response.  This is because the server will have already
determined the objects it plans to send to the client and no
further negotiation is needed.</p>
<div class="literalblock">
<div class="content">
<pre>   shallow-info section
* If the client has requested a shallow fetch/clone, a shallow
  client requests a fetch or the server is shallow then the
  server's response may include a shallow-info section.  The
  shallow-info section will be included if (due to one of the
  above conditions) the server needs to inform the client of any
  shallow boundaries or adjustments to the clients already
  existing shallow boundaries.</pre>
</div>
</div>
</li>
<li>
<p>Always begins with the section header "shallow-info"</p>
</li>
<li>
<p>If a positive depth is requested, the server will compute the
set of commits which are no deeper than the desired depth.</p>
</li>
<li>
<p>The server sends a "shallow obj-id" line for each commit whose
parents will not be sent in the following packfile.</p>
</li>
<li>
<p>The server sends an "unshallow obj-id" line for each commit
which the client has indicated is shallow, but is no longer
shallow as a result of the fetch (due to its parents being
sent in the following packfile).</p>
</li>
<li>
<p>The server MUST NOT send any "unshallow" lines for anything
which the client has not indicated was shallow as a part of
its request.</p>
<div class="literalblock">
<div class="content">
<pre>   wanted-refs section
* This section is only included if the client has requested a
  ref using a 'want-ref' line and if a packfile section is also
  included in the response.</pre>
</div>
</div>
</li>
<li>
<p>Always begins with the section header "wanted-refs".</p>
</li>
<li>
<p>The server will send a ref listing ("&lt;oid&gt; &lt;refname&gt;") for
each reference requested using <em>want-ref</em> lines.</p>
</li>
<li>
<p>The server MUST NOT send any refs which were not requested
using <em>want-ref</em> lines.</p>
<div class="literalblock">
<div class="content">
<pre>   packfile-uris section
* This section is only included if the client sent
  'packfile-uris' and the server has at least one such URI to
  send.</pre>
</div>
</div>
</li>
<li>
<p>Always begins with the section header "packfile-uris".</p>
</li>
<li>
<p>For each URI the server sends, it sends a hash of the pack&#8217;s
contents (as output by git index-pack) followed by the URI.</p>
</li>
<li>
<p>The hashes are 40 hex characters long. When Git upgrades to a new
hash algorithm, this might need to be updated. (It should match
whatever index-pack outputs after "pack\t" or "keep\t".</p>
<div class="literalblock">
<div class="content">
<pre>   packfile section
* This section is only included if the client has sent 'want'
  lines in its request and either requested that no more
  negotiation be done by sending 'done' or if the server has
  decided it has found a sufficient cut point to produce a
  packfile.</pre>
</div>
</div>
</li>
<li>
<p>Always begins with the section header "packfile"</p>
</li>
<li>
<p>The transmission of the packfile begins immediately after the
section header</p>
</li>
<li>
<p>The data transfer of the packfile is always multiplexed, using
the same semantics of the <em>side-band-64k</em> capability from
protocol version 1.  This means that each packet, during the
packfile data stream, is made up of a leading 4-byte pkt-line
length (typical of the pkt-line format), followed by a 1-byte
stream code, followed by the actual data.</p>
<div class="literalblock">
<div class="content">
<pre> The stream code can be one of:
1 - pack data
2 - progress messages
3 - fatal error message just before stream aborts</pre>
</div>
</div>
</li>
</ul>
</div>
</div>
<div class="sect2">
<h3 id="_server_option">server-option</h3>
<div class="paragraph">
<p>If advertised, indicates that any number of server specific options can be
included in a request.  This is done by sending each option as a
"server-option=&lt;option&gt;" capability line in the capability-list section of
a request.</p>
</div>
<div class="paragraph">
<p>The provided options must not contain a NUL or LF character.</p>
</div>
</div>
<div class="sect2">
<h3 id="_object_format"> object-format</h3>
<div class="paragraph">
<p>The server can advertise the <code>object-format</code> capability with a value <code>X</code> (in the
form <code>object-format=X</code>) to notify the client that the server is able to deal
with objects using hash algorithm X.  If not specified, the server is assumed to
only handle SHA-1.  If the client would like to use a hash algorithm other than
SHA-1, it should specify its object-format string.</p>
</div>
</div>
<div class="sect2">
<h3 id="_session_idsession_id">session-id=&lt;session id&gt;</h3>
<div class="paragraph">
<p>The server may advertise a session ID that can be used to identify this process
across multiple requests. The client may advertise its own session ID back to
the server as well.</p>
</div>
<div class="paragraph">
<p>Session IDs should be unique to a given process. They must fit within a
packet-line, and must not contain non-printable or whitespace characters. The
current implementation uses trace2 session IDs (see
<a href="api-trace2.html">api-trace2</a> for details), but this may change and users of
the session ID should not rely on this fact.</p>
</div>
</div>
<div class="sect2">
<h3 id="_object_info">object-info</h3>
<div class="paragraph">
<p><code>object-info</code> is the command to retrieve information about one or more objects.
Its main purpose is to allow a client to make decisions based on this
information without having to fully fetch objects. Object size is the only
information that is currently supported.</p>
</div>
<div class="paragraph">
<p>An <code>object-info</code> request takes the following arguments:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>size
Requests size information to be returned for each listed object id.</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>oid &lt;oid&gt;
Indicates to the server an object which the client wants to obtain
information for.</pre>
</div>
</div>
<div class="paragraph">
<p>The response of <code>object-info</code> is a list of the requested object ids
and associated requested information, each separated by a single space.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>output = info flush-pkt</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>info = PKT-LINE(attrs) LF)
	*PKT-LINE(obj-info LF)</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>attrs = attr | attrs SP attrs</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>attr = "size"</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>obj-info = obj-id SP obj-size</pre>
</div>
</div>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2022-05-09 13:28:27 UTC
</div>
</div>
</body>
</html>