"""
TEST action of ES_Game
"""

import lsr.protostar.core.parameter as pa
from lsr.protostar.core.action import MotionBuilderAction
import pythonidelib


class ES_TEST_ACTION(MotionBuilderAction):
    """
    ES TEST
    """

    _TAGS = ['export']
    _UI_COLOR = (168, 58, 168)
    _UI_ICON = 'export'

    # --- input parameters

    @pa.bool_param(default=True)
    def export_body(self):
        """If True, enable body animation export."""

    def run(self):
        """
        ES Anim Export run method
        """
        super(ES_TEST_ACTION, self).run()

        # self.mobu_print('This is a mobu test')
        self.info('This is a mobu test')
        pythonidelib.FlushOutput()
