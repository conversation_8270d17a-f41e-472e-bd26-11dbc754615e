#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
贝塞尔曲线K帧编辑器UI
"""

import sys
import math
from Qt import QtWidgets, QtCore, QtGui


class BezierPoint:
    """贝塞尔曲线关键点"""
    
    def __init__(self, time=0.0, value=0.0, in_tangent=(0, 0), out_tangent=(0, 0)):
        self.time = time
        self.value = value
        self.in_tangent = in_tangent    # 入切线
        self.out_tangent = out_tangent  # 出切线
        self.selected = False


class BezierCurveWidget(QtWidgets.QWidget):
    """贝塞尔曲线编辑器控件"""
    
    keyframe_changed = QtCore.Signal(list)  # 关键帧改变信号
    
    def __init__(self, parent=None):
        super(BezierCurveWidget, self).__init__(parent)
        self.setMinimumSize(400, 300)
        self.setMouseTracking(True)
        
        # 曲线数据
        self.keyframes = []
        self.selected_keyframe = None
        self.dragging_keyframe = None
        self.dragging_tangent = None
        self.drag_start_pos = None
        
        # 视图参数
        self.time_range = (0.0, 10.0)
        self.value_range = (-1.0, 1.0)
        self.grid_enabled = True
        
        # 绘制参数
        self.keyframe_size = 8
        self.tangent_size = 6
        
        # 初始化一些默认关键帧
        self.add_keyframe(0.0, 0.0)
        self.add_keyframe(5.0, 1.0)
        self.add_keyframe(10.0, 0.0)
    
    def add_keyframe(self, time, value):
        """添加关键帧"""
        # 计算默认切线
        in_tangent = (-1.0, 0.0)
        out_tangent = (1.0, 0.0)
        
        keyframe = BezierPoint(time, value, in_tangent, out_tangent)
        self.keyframes.append(keyframe)
        self.keyframes.sort(key=lambda k: k.time)
        self.update()
        self.keyframe_changed.emit(self.keyframes)
    
    def remove_keyframe(self, keyframe):
        """删除关键帧"""
        if keyframe in self.keyframes and len(self.keyframes) > 2:
            self.keyframes.remove(keyframe)
            if self.selected_keyframe == keyframe:
                self.selected_keyframe = None
            self.update()
            self.keyframe_changed.emit(self.keyframes)
    
    def world_to_screen(self, time, value):
        """世界坐标转屏幕坐标"""
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        time_norm = (time - self.time_range[0]) / (self.time_range[1] - self.time_range[0])
        value_norm = (value - self.value_range[0]) / (self.value_range[1] - self.value_range[0])
        
        x = rect.left() + time_norm * rect.width()
        y = rect.bottom() - value_norm * rect.height()
        
        return QtCore.QPointF(x, y)
    
    def screen_to_world(self, screen_pos):
        """屏幕坐标转世界坐标"""
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        time_norm = (screen_pos.x() - rect.left()) / rect.width()
        value_norm = (rect.bottom() - screen_pos.y()) / rect.height()
        
        time = self.time_range[0] + time_norm * (self.time_range[1] - self.time_range[0])
        value = self.value_range[0] + value_norm * (self.value_range[1] - self.value_range[0])
        
        return time, value
    
    def get_keyframe_at_pos(self, pos):
        """获取指定位置的关键帧"""
        # 确保pos是QPointF类型
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)

        for keyframe in self.keyframes:
            screen_pos = self.world_to_screen(keyframe.time, keyframe.value)
            if (pos - screen_pos).manhattanLength() < self.keyframe_size:
                return keyframe
        return None
    
    def get_tangent_at_pos(self, pos):
        """获取指定位置的切线控制点"""
        # 确保pos是QPointF类型
        if isinstance(pos, QtCore.QPoint):
            pos = QtCore.QPointF(pos)

        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)

            # 检查入切线
            in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * 30, -keyframe.in_tangent[1] * 30)
            if (pos - in_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'in'

            # 检查出切线
            out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * 30, -keyframe.out_tangent[1] * 30)
            if (pos - out_pos).manhattanLength() < self.tangent_size:
                return keyframe, 'out'

        return None, None
    
    def evaluate_curve(self, time):
        """计算指定时间的曲线值"""
        if not self.keyframes:
            return 0.0
        
        # 找到时间范围内的关键帧
        if time <= self.keyframes[0].time:
            return self.keyframes[0].value
        if time >= self.keyframes[-1].time:
            return self.keyframes[-1].value
        
        # 找到相邻的两个关键帧
        for i in range(len(self.keyframes) - 1):
            kf1 = self.keyframes[i]
            kf2 = self.keyframes[i + 1]
            
            if kf1.time <= time <= kf2.time:
                # 使用贝塞尔插值
                t = (time - kf1.time) / (kf2.time - kf1.time)
                
                p0 = (kf1.time, kf1.value)
                p1 = (kf1.time + kf1.out_tangent[0], kf1.value + kf1.out_tangent[1])
                p2 = (kf2.time + kf2.in_tangent[0], kf2.value + kf2.in_tangent[1])
                p3 = (kf2.time, kf2.value)
                
                # 三次贝塞尔曲线
                value = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
                return value
        
        return 0.0
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QtGui.QPainter(self)
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        rect = self.rect().adjusted(20, 20, -20, -20)
        
        # 绘制背景
        painter.fillRect(rect, QtGui.QColor(40, 40, 40))
        painter.setPen(QtGui.QPen(QtGui.QColor(100, 100, 100), 1))
        painter.drawRect(rect)
        
        # 绘制网格
        if self.grid_enabled:
            self.draw_grid(painter, rect)
        
        # 绘制曲线
        self.draw_curve(painter)
        
        # 绘制关键帧
        self.draw_keyframes(painter)
    
    def draw_grid(self, painter, rect):
        """绘制网格"""
        painter.setPen(QtGui.QPen(QtGui.QColor(60, 60, 60), 1))
        
        # 垂直网格线
        for i in range(11):
            x = rect.left() + i * rect.width() / 10
            painter.drawLine(x, rect.top(), x, rect.bottom())
        
        # 水平网格线
        for i in range(11):
            y = rect.top() + i * rect.height() / 10
            painter.drawLine(rect.left(), y, rect.right(), y)
    
    def draw_curve(self, painter):
        """绘制贝塞尔曲线"""
        if len(self.keyframes) < 2:
            return
        
        painter.setPen(QtGui.QPen(QtGui.QColor(100, 200, 255), 2))
        
        # 绘制曲线段
        path = QtGui.QPainterPath()
        
        start_time = self.time_range[0]
        end_time = self.time_range[1]
        steps = 200
        
        first_point = True
        for i in range(steps + 1):
            t = start_time + (end_time - start_time) * i / steps
            value = self.evaluate_curve(t)
            screen_pos = self.world_to_screen(t, value)
            
            if first_point:
                path.moveTo(screen_pos)
                first_point = False
            else:
                path.lineTo(screen_pos)
        
        painter.drawPath(path)
    
    def draw_keyframes(self, painter):
        """绘制关键帧和切线"""
        for keyframe in self.keyframes:
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)
            
            # 绘制切线
            if keyframe.selected or keyframe == self.selected_keyframe:
                painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 0), 1))
                
                # 入切线
                in_pos = kf_pos + QtCore.QPointF(keyframe.in_tangent[0] * 30, -keyframe.in_tangent[1] * 30)
                painter.drawLine(kf_pos, in_pos)
                painter.fillRect(in_pos.x() - self.tangent_size//2, in_pos.y() - self.tangent_size//2,
                               self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))
                
                # 出切线
                out_pos = kf_pos + QtCore.QPointF(keyframe.out_tangent[0] * 30, -keyframe.out_tangent[1] * 30)
                painter.drawLine(kf_pos, out_pos)
                painter.fillRect(out_pos.x() - self.tangent_size//2, out_pos.y() - self.tangent_size//2,
                               self.tangent_size, self.tangent_size, QtGui.QColor(255, 255, 0))
            
            # 绘制关键帧点
            color = QtGui.QColor(255, 100, 100) if keyframe == self.selected_keyframe else QtGui.QColor(200, 200, 200)
            painter.setBrush(QtGui.QBrush(color))
            painter.setPen(QtGui.QPen(QtGui.QColor(255, 255, 255), 2))
            
            painter.drawEllipse(kf_pos, self.keyframe_size, self.keyframe_size)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == QtCore.Qt.LeftButton:
            # 检查是否点击了切线控制点
            tangent_kf, tangent_type = self.get_tangent_at_pos(event.pos())
            if tangent_kf:
                self.dragging_tangent = (tangent_kf, tangent_type)
                self.drag_start_pos = event.pos()
                return
            
            # 检查是否点击了关键帧
            keyframe = self.get_keyframe_at_pos(event.pos())
            if keyframe:
                self.selected_keyframe = keyframe
                self.dragging_keyframe = keyframe
                self.drag_start_pos = event.pos()
            else:
                # 添加新关键帧
                time, value = self.screen_to_world(event.pos())
                self.add_keyframe(time, value)
            
            self.update()
        
        elif event.button() == QtCore.Qt.RightButton:
            # 删除关键帧
            keyframe = self.get_keyframe_at_pos(event.pos())
            if keyframe:
                self.remove_keyframe(keyframe)
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging_keyframe:
            # 拖拽关键帧
            time, value = self.screen_to_world(event.pos())
            self.dragging_keyframe.time = max(self.time_range[0], min(self.time_range[1], time))
            self.dragging_keyframe.value = max(self.value_range[0], min(self.value_range[1], value))
            self.keyframes.sort(key=lambda k: k.time)
            self.update()
            self.keyframe_changed.emit(self.keyframes)
        
        elif self.dragging_tangent:
            # 拖拽切线
            keyframe, tangent_type = self.dragging_tangent
            kf_pos = self.world_to_screen(keyframe.time, keyframe.value)
            delta = event.pos() - kf_pos
            
            tangent_x = delta.x() / 30.0
            tangent_y = -delta.y() / 30.0
            
            if tangent_type == 'in':
                keyframe.in_tangent = (tangent_x, tangent_y)
            else:
                keyframe.out_tangent = (tangent_x, tangent_y)
            
            self.update()
            self.keyframe_changed.emit(self.keyframes)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        self.dragging_keyframe = None
        self.dragging_tangent = None
        self.drag_start_pos = None


class KeyframePropertiesWidget(QtWidgets.QWidget):
    """关键帧属性编辑器"""

    def __init__(self, parent=None):
        super(KeyframePropertiesWidget, self).__init__(parent)
        self.current_keyframe = None
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QtWidgets.QFormLayout()

        # 时间输入
        self.time_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_spinbox.setRange(-999.0, 999.0)
        self.time_spinbox.setDecimals(3)
        self.time_spinbox.valueChanged.connect(self.on_time_changed)
        layout.addRow("时间:", self.time_spinbox)

        # 值输入
        self.value_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_spinbox.setRange(-999.0, 999.0)
        self.value_spinbox.setDecimals(3)
        self.value_spinbox.valueChanged.connect(self.on_value_changed)
        layout.addRow("值:", self.value_spinbox)

        # 入切线
        self.in_tangent_x = QtWidgets.QDoubleSpinBox()
        self.in_tangent_x.setRange(-10.0, 10.0)
        self.in_tangent_x.setDecimals(3)
        self.in_tangent_x.valueChanged.connect(self.on_in_tangent_changed)

        self.in_tangent_y = QtWidgets.QDoubleSpinBox()
        self.in_tangent_y.setRange(-10.0, 10.0)
        self.in_tangent_y.setDecimals(3)
        self.in_tangent_y.valueChanged.connect(self.on_in_tangent_changed)

        in_layout = QtWidgets.QHBoxLayout()
        in_layout.addWidget(QtWidgets.QLabel("X:"))
        in_layout.addWidget(self.in_tangent_x)
        in_layout.addWidget(QtWidgets.QLabel("Y:"))
        in_layout.addWidget(self.in_tangent_y)

        in_widget = QtWidgets.QWidget()
        in_widget.setLayout(in_layout)
        layout.addRow("入切线:", in_widget)

        # 出切线
        self.out_tangent_x = QtWidgets.QDoubleSpinBox()
        self.out_tangent_x.setRange(-10.0, 10.0)
        self.out_tangent_x.setDecimals(3)
        self.out_tangent_x.valueChanged.connect(self.on_out_tangent_changed)

        self.out_tangent_y = QtWidgets.QDoubleSpinBox()
        self.out_tangent_y.setRange(-10.0, 10.0)
        self.out_tangent_y.setDecimals(3)
        self.out_tangent_y.valueChanged.connect(self.on_out_tangent_changed)

        out_layout = QtWidgets.QHBoxLayout()
        out_layout.addWidget(QtWidgets.QLabel("X:"))
        out_layout.addWidget(self.out_tangent_x)
        out_layout.addWidget(QtWidgets.QLabel("Y:"))
        out_layout.addWidget(self.out_tangent_y)

        out_widget = QtWidgets.QWidget()
        out_widget.setLayout(out_layout)
        layout.addRow("出切线:", out_widget)

        self.setLayout(layout)
        self.setEnabled(False)

    def set_keyframe(self, keyframe):
        """设置当前编辑的关键帧"""
        self.current_keyframe = keyframe

        if keyframe:
            self.setEnabled(True)
            self.time_spinbox.blockSignals(True)
            self.value_spinbox.blockSignals(True)
            self.in_tangent_x.blockSignals(True)
            self.in_tangent_y.blockSignals(True)
            self.out_tangent_x.blockSignals(True)
            self.out_tangent_y.blockSignals(True)

            self.time_spinbox.setValue(keyframe.time)
            self.value_spinbox.setValue(keyframe.value)
            self.in_tangent_x.setValue(keyframe.in_tangent[0])
            self.in_tangent_y.setValue(keyframe.in_tangent[1])
            self.out_tangent_x.setValue(keyframe.out_tangent[0])
            self.out_tangent_y.setValue(keyframe.out_tangent[1])

            self.time_spinbox.blockSignals(False)
            self.value_spinbox.blockSignals(False)
            self.in_tangent_x.blockSignals(False)
            self.in_tangent_y.blockSignals(False)
            self.out_tangent_x.blockSignals(False)
            self.out_tangent_y.blockSignals(False)
        else:
            self.setEnabled(False)

    def on_time_changed(self):
        """时间改变"""
        if self.current_keyframe:
            self.current_keyframe.time = self.time_spinbox.value()

    def on_value_changed(self):
        """值改变"""
        if self.current_keyframe:
            self.current_keyframe.value = self.value_spinbox.value()

    def on_in_tangent_changed(self):
        """入切线改变"""
        if self.current_keyframe:
            self.current_keyframe.in_tangent = (
                self.in_tangent_x.value(),
                self.in_tangent_y.value()
            )

    def on_out_tangent_changed(self):
        """出切线改变"""
        if self.current_keyframe:
            self.current_keyframe.out_tangent = (
                self.out_tangent_x.value(),
                self.out_tangent_y.value()
            )


class BezierCurveEditor(QtWidgets.QWidget):
    """贝塞尔曲线K帧编辑器主窗口"""

    def __init__(self, parent=None):
        super(BezierCurveEditor, self).__init__(parent)
        self.setWindowTitle("贝塞尔曲线K帧编辑器")
        self.setMinimumSize(800, 600)
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """初始化UI"""
        main_layout = QtWidgets.QHBoxLayout()

        # 左侧：曲线编辑器
        left_widget = QtWidgets.QWidget()
        left_layout = QtWidgets.QVBoxLayout()

        # 工具栏
        toolbar = self.create_toolbar()
        left_layout.addWidget(toolbar)

        # 曲线编辑器
        self.curve_widget = BezierCurveWidget()
        left_layout.addWidget(self.curve_widget)

        # 时间轴控制
        time_control = self.create_time_control()
        left_layout.addWidget(time_control)

        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget, 3)

        # 右侧：属性面板
        right_widget = QtWidgets.QWidget()
        right_layout = QtWidgets.QVBoxLayout()

        # 关键帧属性
        properties_group = QtWidgets.QGroupBox("关键帧属性")
        self.properties_widget = KeyframePropertiesWidget()
        properties_layout = QtWidgets.QVBoxLayout()
        properties_layout.addWidget(self.properties_widget)
        properties_group.setLayout(properties_layout)
        right_layout.addWidget(properties_group)

        # 曲线设置
        settings_group = self.create_settings_group()
        right_layout.addWidget(settings_group)

        # 预设按钮
        presets_group = self.create_presets_group()
        right_layout.addWidget(presets_group)

        right_layout.addStretch()
        right_widget.setLayout(right_layout)
        main_layout.addWidget(right_widget, 1)

        self.setLayout(main_layout)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        # 添加关键帧按钮
        self.add_keyframe_btn = QtWidgets.QPushButton("添加关键帧")
        self.add_keyframe_btn.clicked.connect(self.add_keyframe)
        layout.addWidget(self.add_keyframe_btn)

        # 删除关键帧按钮
        self.delete_keyframe_btn = QtWidgets.QPushButton("删除关键帧")
        self.delete_keyframe_btn.clicked.connect(self.delete_keyframe)
        layout.addWidget(self.delete_keyframe_btn)

        layout.addWidget(QtWidgets.QLabel("|"))

        # 网格开关
        self.grid_checkbox = QtWidgets.QCheckBox("显示网格")
        self.grid_checkbox.setChecked(True)
        self.grid_checkbox.toggled.connect(self.toggle_grid)
        layout.addWidget(self.grid_checkbox)

        layout.addStretch()
        toolbar.setLayout(layout)
        return toolbar

    def create_time_control(self):
        """创建时间轴控制"""
        widget = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout()

        layout.addWidget(QtWidgets.QLabel("时间范围:"))

        self.time_start_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_start_spinbox.setRange(-999.0, 999.0)
        self.time_start_spinbox.setValue(0.0)
        self.time_start_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_start_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.time_end_spinbox = QtWidgets.QDoubleSpinBox()
        self.time_end_spinbox.setRange(-999.0, 999.0)
        self.time_end_spinbox.setValue(10.0)
        self.time_end_spinbox.valueChanged.connect(self.update_time_range)
        layout.addWidget(self.time_end_spinbox)

        layout.addWidget(QtWidgets.QLabel("|"))
        layout.addWidget(QtWidgets.QLabel("值范围:"))

        self.value_min_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_min_spinbox.setRange(-999.0, 999.0)
        self.value_min_spinbox.setValue(-1.0)
        self.value_min_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_min_spinbox)

        layout.addWidget(QtWidgets.QLabel("到"))

        self.value_max_spinbox = QtWidgets.QDoubleSpinBox()
        self.value_max_spinbox.setRange(-999.0, 999.0)
        self.value_max_spinbox.setValue(1.0)
        self.value_max_spinbox.valueChanged.connect(self.update_value_range)
        layout.addWidget(self.value_max_spinbox)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_settings_group(self):
        """创建设置组"""
        group = QtWidgets.QGroupBox("曲线设置")
        layout = QtWidgets.QVBoxLayout()

        # 插值类型
        interp_layout = QtWidgets.QHBoxLayout()
        interp_layout.addWidget(QtWidgets.QLabel("插值类型:"))
        self.interp_combo = QtWidgets.QComboBox()
        self.interp_combo.addItems(["贝塞尔", "线性", "常量"])
        interp_layout.addWidget(self.interp_combo)
        layout.addLayout(interp_layout)

        # 切线模式
        tangent_layout = QtWidgets.QHBoxLayout()
        tangent_layout.addWidget(QtWidgets.QLabel("切线模式:"))
        self.tangent_combo = QtWidgets.QComboBox()
        self.tangent_combo.addItems(["自由", "对称", "平滑"])
        tangent_layout.addWidget(self.tangent_combo)
        layout.addLayout(tangent_layout)

        group.setLayout(layout)
        return group

    def create_presets_group(self):
        """创建预设组"""
        group = QtWidgets.QGroupBox("预设曲线")
        layout = QtWidgets.QVBoxLayout()

        # 预设按钮
        presets = [
            ("线性", self.apply_linear_preset),
            ("缓入", self.apply_ease_in_preset),
            ("缓出", self.apply_ease_out_preset),
            ("缓入缓出", self.apply_ease_in_out_preset),
            ("弹跳", self.apply_bounce_preset),
            ("重置", self.reset_curve)
        ]

        for name, callback in presets:
            btn = QtWidgets.QPushButton(name)
            btn.clicked.connect(callback)
            layout.addWidget(btn)

        group.setLayout(layout)
        return group

    def connect_signals(self):
        """连接信号"""
        self.curve_widget.keyframe_changed.connect(self.on_keyframe_changed)

    def on_keyframe_changed(self, keyframes):
        """关键帧改变回调"""
        # 更新属性面板
        if self.curve_widget.selected_keyframe:
            self.properties_widget.set_keyframe(self.curve_widget.selected_keyframe)
        else:
            self.properties_widget.set_keyframe(None)

        # 更新曲线显示
        self.curve_widget.update()

    def add_keyframe(self):
        """添加关键帧"""
        # 在时间轴中间添加关键帧
        time_range = self.curve_widget.time_range
        mid_time = (time_range[0] + time_range[1]) / 2
        self.curve_widget.add_keyframe(mid_time, 0.0)

    def delete_keyframe(self):
        """删除选中的关键帧"""
        if self.curve_widget.selected_keyframe:
            self.curve_widget.remove_keyframe(self.curve_widget.selected_keyframe)

    def toggle_grid(self, enabled):
        """切换网格显示"""
        self.curve_widget.grid_enabled = enabled
        self.curve_widget.update()

    def update_time_range(self):
        """更新时间范围"""
        start = self.time_start_spinbox.value()
        end = self.time_end_spinbox.value()
        if start < end:
            self.curve_widget.time_range = (start, end)
            self.curve_widget.update()

    def update_value_range(self):
        """更新值范围"""
        min_val = self.value_min_spinbox.value()
        max_val = self.value_max_spinbox.value()
        if min_val < max_val:
            self.curve_widget.value_range = (min_val, max_val)
            self.curve_widget.update()

    def apply_linear_preset(self):
        """应用线性预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        # 设置线性切线
        for kf in self.curve_widget.keyframes:
            kf.in_tangent = (0.0, 0.0)
            kf.out_tangent = (0.0, 0.0)

    def apply_ease_in_preset(self):
        """应用缓入预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (3.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-1.0, 0.0)

    def apply_ease_out_preset(self):
        """应用缓出预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (1.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-3.0, 0.0)

    def apply_ease_in_out_preset(self):
        """应用缓入缓出预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(10.0, 1.0)

        self.curve_widget.keyframes[0].out_tangent = (2.0, 0.0)
        self.curve_widget.keyframes[1].in_tangent = (-2.0, 0.0)

    def apply_bounce_preset(self):
        """应用弹跳预设"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(3.0, 1.2)
        self.curve_widget.add_keyframe(6.0, 0.8)
        self.curve_widget.add_keyframe(10.0, 1.0)

    def reset_curve(self):
        """重置曲线"""
        self.curve_widget.keyframes.clear()
        self.curve_widget.add_keyframe(0.0, 0.0)
        self.curve_widget.add_keyframe(5.0, 1.0)
        self.curve_widget.add_keyframe(10.0, 0.0)
        self.curve_widget.update()


# 演示应用
class BezierCurveApp(QtWidgets.QApplication):
    """贝塞尔曲线编辑器应用"""

    def __init__(self, sys_argv):
        super(BezierCurveApp, self).__init__(sys_argv)
        self.editor = BezierCurveEditor()
        self.editor.show()


if __name__ == '__main__':
    app = BezierCurveApp(sys.argv)
    sys.exit(app.exec_())
