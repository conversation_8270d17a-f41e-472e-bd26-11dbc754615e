

class TrajAnimTableConfig:

    headers = [
        {
            'title': 'Direction',
            'editor_type': 'combobox',
            'items': ['EAI_None',
                      'EAI_Forward', 'EAI_ForwardRight', 'EAI_Right', 'EAI_BackwardRight',
                      'EAI_Backward', 'EAI_BackwardLeft', 'EAI_Left', 'EAI_ForwardLeft'],
            'width': 150,
            'default': 'EAI_None'
        },
        {
            'title': 'TimeRange',
            'editor_type': 'doublespinbox',
            'width': 300,
            'default': '0'
        },
        {
            'title': 'MovementSpeed',
            'editor_type': 'combobox',
            'items': ['Walk', 'Run'],
            'width': 150,
            'default': 'Walk'
        },
        {
            'title': 'MovementPattern',
            'editor_type': 'combobox',
            'items': ['Casual', 'Combat', 'Aim', 'AimFire',
                      'HipFire', 'Cover', 'Panic'],
            'width': 150,
            'default': 'Casual'
        },
        {
            'title': 'IsStrafe',
            'editor_type': 'combobox',
            'items': ['Strafe', 'Pivot'],
            'width': 150,
            'default': 'Strafe'
        }
    ]
